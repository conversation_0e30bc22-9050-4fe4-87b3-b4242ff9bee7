/** @type {import('tailwindcss').Config} */
import { fontFamily } from 'tailwindcss/defaultTheme'

export default {
  darkMode: ['class'],
  content: [
    './pages/**/*.{ts,tsx}',
    './components/**/*.{ts,tsx}',
    './app/**/*.{ts,tsx}',
    './src/**/*.{ts,tsx}',
  ],
  prefix: '',
  theme: {
    container: {
      center: true,
      padding: '2rem',
      screens: {
        '2xl': '1400px',
      },
    },
    extend: {
      colors: {
        // Navy Blue Primary Palette
        'navy-blue': {
          50: '#f0f7ff',
          100: '#e0efff',
          200: '#bae0ff',
          300: '#7cc8ff',
          400: '#36adff',
          500: '#0891ff',
          600: '#005baa', // Primary brand color
          700: '#0054a3',
          800: '#004286',
          900: '#003670',
          950: '#002347',
        },
        
        // Semantic colors
        border: 'hsl(var(--border))',
        input: 'hsl(var(--input))',
        ring: 'hsl(var(--ring))',
        background: 'hsl(var(--background))',
        foreground: 'hsl(var(--foreground))',
        primary: {
          DEFAULT: 'hsl(var(--primary))',
          foreground: 'hsl(var(--primary-foreground))',
        },
        secondary: {
          DEFAULT: 'hsl(var(--secondary))',
          foreground: 'hsl(var(--secondary-foreground))',
        },
        destructive: {
          DEFAULT: 'hsl(var(--destructive))',
          foreground: 'hsl(var(--destructive-foreground))',
        },
        success: {
          DEFAULT: 'hsl(var(--success))',
          foreground: 'hsl(var(--success-foreground))',
        },
        warning: {
          DEFAULT: 'hsl(var(--warning))',
          foreground: 'hsl(var(--warning-foreground))',
        },
        muted: {
          DEFAULT: 'hsl(var(--muted))',
          foreground: 'hsl(var(--muted-foreground))',
        },
        accent: {
          DEFAULT: 'hsl(var(--accent))',
          foreground: 'hsl(var(--accent-foreground))',
        },
        popover: {
          DEFAULT: 'hsl(var(--popover))',
          foreground: 'hsl(var(--popover-foreground))',
        },
        card: {
          DEFAULT: 'hsl(var(--card))',
          foreground: 'hsl(var(--card-foreground))',
        },
        sidebar: {
          DEFAULT: 'hsl(var(--sidebar))',
          foreground: 'hsl(var(--sidebar-foreground))',
          primary: 'hsl(var(--sidebar-primary))',
          'primary-foreground': 'hsl(var(--sidebar-primary-foreground))',
          accent: 'hsl(var(--sidebar-accent))',
          'accent-foreground': 'hsl(var(--sidebar-accent-foreground))',
          border: 'hsl(var(--sidebar-border))',
          ring: 'hsl(var(--sidebar-ring))',
        },
      },
      
      borderRadius: {
        lg: 'var(--radius)',
        md: 'calc(var(--radius) - 2px)',
        sm: 'calc(var(--radius) - 4px)',
      },
      
      fontFamily: {
        sans: ['Inter', ...fontFamily.sans],
        display: ['Manrope', 'Inter', ...fontFamily.sans],
        mono: [...fontFamily.mono],
      },
      
      fontSize: {
        xs: ['0.75rem', { lineHeight: '1rem' }],
        sm: ['0.875rem', { lineHeight: '1.25rem' }],
        base: ['1rem', { lineHeight: '1.5rem' }],
        lg: ['1.125rem', { lineHeight: '1.75rem' }],
        xl: ['1.25rem', { lineHeight: '1.75rem' }],
        '2xl': ['1.5rem', { lineHeight: '2rem' }],
        '3xl': ['1.875rem', { lineHeight: '2.25rem' }],
        '4xl': ['2.25rem', { lineHeight: '2.5rem' }],
        '5xl': ['3rem', { lineHeight: '1' }],
        '6xl': ['3.75rem', { lineHeight: '1' }],
        '7xl': ['4.5rem', { lineHeight: '1' }],
        '8xl': ['6rem', { lineHeight: '1' }],
        '9xl': ['8rem', { lineHeight: '1' }],
      },
      
      spacing: {
        '18': '4.5rem',
        '88': '22rem',
      },
      
      animation: {
        'accordion-down': 'accordion-down 0.2s ease-out',
        'accordion-up': 'accordion-up 0.2s ease-out',
        'collapsible-down': 'collapsible-down 0.2s ease-out',
        'collapsible-up': 'collapsible-up 0.2s ease-out',
      },
      
      keyframes: {
        'accordion-down': {
          from: { height: '0' },
          to: { height: 'var(--radix-accordion-content-height)' },
        },
        'accordion-up': {
          from: { height: 'var(--radix-accordion-content-height)' },
          to: { height: '0' },
        },
        'collapsible-down': {
          from: { height: '0' },
          to: { height: 'var(--radix-collapsible-content-height)' },
        },
        'collapsible-up': {
          from: { height: 'var(--radix-collapsible-content-height)' },
          to: { height: '0' },
        },
      },
      
      boxShadow: {
        'navy': '0 10px 15px -3px rgba(0, 91, 170, 0.1), 0 4px 6px -2px rgba(0, 91, 170, 0.05)',
        'navy-lg': '0 20px 25px -5px rgba(0, 91, 170, 0.1), 0 10px 10px -5px rgba(0, 91, 170, 0.04)',
      },
    },
  },
  
  safelist: [
    // Dynamic font classes to prevent purging
    'font-inter',
    'font-manrope',
    'font-system',
    
    // Navy blue variations
    'text-navy-blue-50',
    'text-navy-blue-100',
    'text-navy-blue-200',
    'text-navy-blue-300',
    'text-navy-blue-400',
    'text-navy-blue-500',
    'text-navy-blue-600',
    'text-navy-blue-700',
    'text-navy-blue-800',
    'text-navy-blue-900',
    'text-navy-blue-950',
    
    'bg-navy-blue-50',
    'bg-navy-blue-100',
    'bg-navy-blue-200',
    'bg-navy-blue-300',
    'bg-navy-blue-400',
    'bg-navy-blue-500',
    'bg-navy-blue-600',
    'bg-navy-blue-700',
    'bg-navy-blue-800',
    'bg-navy-blue-900',
    'bg-navy-blue-950',
    
    'border-navy-blue-50',
    'border-navy-blue-100',
    'border-navy-blue-200',
    'border-navy-blue-300',
    'border-navy-blue-400',
    'border-navy-blue-500',
    'border-navy-blue-600',
    'border-navy-blue-700',
    'border-navy-blue-800',
    'border-navy-blue-900',
    'border-navy-blue-950',
  ],
  
  plugins: [require('tailwindcss-animate')],
} 