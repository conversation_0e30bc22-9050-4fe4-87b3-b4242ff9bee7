---
type: "always_apply"
---



## 1. Feature-Driven Structure

```
src/features/[domain]/[subdomain]/
├── components/         # Feature-specific UI components
├── hooks/              # Feature-specific custom hooks
├── context/            # Feature-specific context providers
├── data/               # Data schemas and types
└── index.tsx           # Main feature entry point
```

### Benefits
- **Logical grouping**: Related functionality stays together
- **Easy navigation**: Find feature code in predictable locations
- **Scalability**: Add new features without affecting existing structure
- **Team collaboration**: Clear ownership boundaries

## 2. Modular Hook Organization

### Structure Rules
- **Create dedicated `hooks/` directories** within feature folders
- **One hook per file** with focused responsibility
- **Use index.ts** for clean exports: `export { useFeatureHook } from './use-feature-hook'`
- **Hook naming**: `use[Domain][Action]` (e.g., `useCustomizationForm`, `useMenuItemSelection`)

### Example Structure
```
src/features/menu/
├── hooks/
│   ├── index.ts
│   ├── use-menu-items.ts
│   ├── use-menu-categories.ts
│   └── use-menu-form.ts
└── components/
    └── menu-list.tsx
```

### Hook Responsibilities
- **Data fetching**: API calls and caching
- **Form management**: Validation and submission
- **State management**: Local component state
- **Business logic**: Domain-specific operations

## 3. Component Architecture

### Design Principles
- **Prefer composition over inheritance**
- **Extract reusable logic into custom hooks**
- **Keep components focused on rendering and user interaction**
- **Use TypeScript interfaces for all props and data structures**

### Component Types
- **Feature Components**: Domain-specific UI logic
- **Layout Components**: Page structure and navigation
- **UI Components**: Reusable design system elements
- **POS Components**: Business-specific shared components


## 4. Separation of Concerns

### Clear Boundaries
- **UI Layer**: Components handle rendering and user interaction
- **Logic Layer**: Hooks manage state and business logic
- **Data Layer**: API clients handle server communication
- **Type Layer**: Interfaces define data contracts