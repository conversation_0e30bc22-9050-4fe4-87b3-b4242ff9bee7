---
type: "always_apply"
---

# Code Style Standards

## Naming Over Comments

- **Self-documenting code**: Use clear, descriptive variable and function names instead of comments
- **Meaningful identifiers**: Choose names that explain the purpose and behavior
- **Avoid inline comments**: Code should be readable without explanatory comments
- **Exception**: Complex business logic or algorithms may require brief explanations

## JSX Conditional Rendering

- **Use && operator**: Prefer `condition && <Component />` for conditional rendering
- **Avoid ternary for simple conditions**: Use ternary only when rendering alternative content
- **Clear boolean expressions**: Ensure conditions are explicit and readable
- **Example**: `isLoading && <Spinner />` instead of `{isLoading ? <Spinner /> : null}`

## Component Styling Patterns

- **Consistent formatting**: Follow established prettier configuration
- **Logical grouping**: Group related JSX elements together
- **Clean prop passing**: Use object destructuring for cleaner prop handling
- **Minimal nesting**: Avoid deeply nested conditional rendering
- **NOT README**: Not creating README after succesfully task