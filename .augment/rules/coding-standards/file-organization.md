---
type: "always_apply"
---

# File Organization Standards

## Directory Structure Rules

```
src/
├── components/ui/          # Reusable UI components (shadcn/ui)
├── components/layout/      # Layout-specific components
├── components/pos/         # POS-specific shared components
├── features/               # Feature modules
├── hooks/                  # Global custom hooks
├── hooks/api/              # API-related hooks
├── lib/                    # Utility libraries and API clients
├── stores/                 # Zustand state management
├── types/                  # Global TypeScript definitions
├── types/api/              # API-specific TypeScript definitions
├── utils/                  # Utility functions
└── constants/              # Application constants
```

## Import Organization

Follow the established import order from `.prettierrc`:

1. **External libraries** (React, third-party packages)
2. **Internal libraries** (@/lib, @/utils, @/constants)
3. **Hooks** (@/hooks)
4. **Components** (@/components)
5. **Features** (@/features)
6. **Relative imports** (./hooks, ../components)

### Import Grouping Rules
- **Combine imports from same module**: Group multiple imports from the same module into a single import statement
- **UI Components**: Always combine `@/components/ui` imports: `import { Button, Card, Input } from '@/components/ui'`
- **Feature Components**: Combine feature component imports when possible
- **Maintain alphabetical order**: Sort imports alphabetically within each group

### Example Import Order
```typescript
import React from 'react'
import { useQuery } from '@tanstack/react-query'

import { cn } from '@/lib/utils'
import { API_ENDPOINTS } from '@/constants/api'

import { useToast } from '@/hooks/use-toast'

import { Button, Card } from '@/components/ui'

import { MenuItemForm } from '@/features/menu/components'

import { useMenuItems } from './hooks/use-menu-items'
```

## Module Organization Patterns

### Feature Module Structure
```
src/features/menu/
├── components/
│   ├── index.ts
│   ├── menu-list.tsx
│   ├── menu-item-card.tsx
│   └── menu-form.tsx
├── hooks/
│   ├── index.ts
│   ├── use-menu-items.ts
│   ├── use-menu-form.ts
│   └── use-menu-categories.ts
├── context/
│   ├── index.ts
│   └── menu-context.tsx
├── data/
│   ├── index.ts
│   ├── menu-schemas.ts
│   └── menu-types.ts
└── index.tsx
```

### Index File Patterns
```typescript
// features/menu/components/index.ts
export { MenuList } from './menu-list'
export { MenuItemCard } from './menu-item-card'
export { MenuForm } from './menu-form'

// features/menu/hooks/index.ts
export { useMenuItems } from './use-menu-items'
export { useMenuForm } from './use-menu-form'
export { useMenuCategories } from './use-menu-categories'

// features/menu/index.tsx
export * from './components'
export * from './hooks'
export * from './context'
export * from './data'
```

## File Naming Conventions

### Component Files
- **PascalCase**: `MenuItemCard.tsx`
- **kebab-case**: `menu-item-card.tsx` (preferred)
- **Consistent within project**: Choose one and stick to it

### Hook Files
- **kebab-case**: `use-menu-items.ts`
- **Descriptive names**: `use-menu-form-validation.ts`

### Utility Files
- **kebab-case**: `format-currency.ts`
- **Clear purpose**: `date-helpers.ts`, `api-client.ts`

### Type Files
- **kebab-case**: `menu-types.ts`
- **Domain-specific**: `api-types.ts`, `form-types.ts`

## Best Practices

### Avoid Deep Nesting
```typescript
// Bad: Deep nesting
import { MenuForm } from '../../../features/menu/components/forms/menu-form'

// Good: Proper index exports
import { MenuForm } from '@/features/menu'
```

### Group Related Files
```
src/features/inventory/
├── components/
│   ├── tables/           # Table-related components
│   ├── forms/            # Form-related components
│   └── modals/           # Modal-related components
```

### Consistent Exports
```typescript
// Prefer named exports for better tree-shaking
export const MenuList = () => { ... }

// Use default exports sparingly
export default MenuList
```
