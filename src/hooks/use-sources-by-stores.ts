import { useMemo, useEffect } from 'react'

import { useQueries, useQueryClient } from '@tanstack/react-query'

import { usePosStores, useCurrentCompany } from '@/stores/posStore'

import { sourcesApi, SourceData } from '@/lib/sale-sources-api'
import { fetchAndSyncStores } from '@/lib/stores-api'

interface UseSourcesByStoresOptions {
  dateRange: {
    from: Date
    to: Date
  }
  selectedStores?: string[]
  filterType?: 'monthly' | 'daily'
  autoFetch?: boolean
}

interface StoreSourcesData {
  storeId: string
  storeName: string
  allSources: SourceData[]
  grabSources: SourceData[]
  beSources: SourceData[]
  shopeeSources: SourceData[]
  taiChoSources: SourceData[]
  grabRevenue: number
  beRevenue: number
  shopeeRevenue: number
  taiChoRevenue: number
  grabTotalBill: number
  beTotalBill: number
  shopeeTotalBill: number
  taiChoTotalBill: number
}

// Platform keywords - tách riêng từng platform
const GRAB_KEYWORDS = ['10000232', 'GRAPDHI']
const BE_KEYWORDS = ['BEFOOD', 'BE_TUTIMI', 'BE-DHI']
const SHOPEE_KEYWORDS = ['10000169', 'NOWDHI']
const TAI_CHO_KEYWORDS = ['10000172']

const PLATFORM_KEYWORDS = {
  grab: GRAB_KEYWORDS,
  be: BE_KEYWORDS,
  shopee: SHOPEE_KEYWORDS,
  'tai-cho': TAI_CHO_KEYWORDS
}

// Export individual platform keywords for use in components
export { GRAB_KEYWORDS, BE_KEYWORDS, SHOPEE_KEYWORDS, TAI_CHO_KEYWORDS }

export function useSourcesByStores({
  dateRange,
  selectedStores = ['all-stores'],
  filterType = 'monthly',
  autoFetch = true
}: UseSourcesByStoresOptions) {
  const { selectedBrand, currentBrandStores, currentBrandApiStores, setApiStores } = usePosStores()
  const { company } = useCurrentCompany()
  const queryClient = useQueryClient()

  const fallbackCompanyUid = '269717a1-7bb6-4fa3-9150-dea2f709c081'
  const fallbackBrandUid = '8b8f15f9-6986-4c5a-86bc-f7dba8966659'

  const companyId = company?.id || fallbackCompanyUid
  const brandId = selectedBrand?.id || fallbackBrandUid

  const stableBrandId = useMemo(() => brandId, [brandId])
  const stableCompanyId = useMemo(() => companyId, [companyId])
  const stableFilterType = useMemo(() => filterType, [filterType])

  useEffect(() => {
    const fetchStores = async () => {
      if (stableCompanyId && stableBrandId && currentBrandApiStores.length === 0) {
        try {
          const fetchedStores = await fetchAndSyncStores(stableCompanyId, stableBrandId)
          setApiStores(fetchedStores)
        } catch (error) {
          // eslint-disable-next-line no-console
          console.log(error)
        }
      }
    }

    fetchStores()
  }, [stableCompanyId, stableBrandId, currentBrandApiStores.length, setApiStores])

  useEffect(() => {
    const handleBrandChange = () => {
      queryClient.invalidateQueries({
        queryKey: ['sources-by-store-all']
      })
    }

    window.addEventListener('brandChanged', handleBrandChange)
    return () => {
      window.removeEventListener('brandChanged', handleBrandChange)
    }
  }, [queryClient])

  const startTime = dateRange?.from ? dateRange.from.getTime() : null
  const endTime = dateRange?.to ? dateRange.to.getTime() : null

  const storesToFetch = useMemo(() => {
    // Use API stores first (more reliable), fallback to auth stores
    const availableStores =
      currentBrandApiStores.length > 0 ? currentBrandApiStores : currentBrandStores

    if (selectedStores.includes('all-stores')) {
      return availableStores?.filter(store => store.active === 1)?.slice(0, 20) // Limit to avoid too many API calls
    }
    return availableStores?.filter(store => selectedStores.includes(store.id))
  }, [selectedStores, currentBrandStores, currentBrandApiStores])

  const byDays = stableFilterType === 'daily' ? 1 : 0

  const storeQueries = useQueries({
    queries: (storesToFetch || []).map(store => ({
      queryKey: [
        'sources-by-store-all',
        stableCompanyId,
        stableBrandId,
        store.id,
        startTime,
        endTime,
        byDays,
        stableFilterType
      ],
      queryFn: async () => {
        if (!stableBrandId || !stableCompanyId || !startTime || !endTime) {
          throw new Error('Missing required parameters')
        }

        const response = await sourcesApi.getSourcesSummary({
          companyUid: stableCompanyId,
          brandUid: stableBrandId,
          startDate: startTime,
          endDate: endTime,
          storeUids: [store.id],
          byDays,
          limit: 100
        })

        return {
          storeId: store.id,
          storeName: store.store_name || `Store ${store.id}`,
          sources: response.data || []
        }
      },
      enabled: autoFetch && !!stableBrandId && !!stableCompanyId && !!startTime && !!endTime,
      staleTime: 2 * 60 * 1000, // 2 minutes - shorter for more responsive updates
      gcTime: 10 * 60 * 1000, // 10 minutes
      refetchOnWindowFocus: false,
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      retry: (failureCount: number, error: any) => {
        if (error?.message?.includes('401') || error?.message?.includes('403')) {
          return false
        }
        return failureCount < 2
      }
    }))
  })

  // Extract stable values from queries for dependencies
  const queryResults = storeQueries.map(query => ({
    data: query.data,
    isLoading: query.isLoading,
    error: query.error
  }))

  const processedStoresData = useMemo(() => {
    const results: StoreSourcesData[] = []

    queryResults.forEach(queryResult => {
      if (queryResult.data) {
        const { storeId, storeName, sources } = queryResult.data

        const grabSources = sources.filter((source: SourceData) =>
          PLATFORM_KEYWORDS.grab.includes(source.source_id)
        )

        const beSources = sources.filter((source: SourceData) =>
          PLATFORM_KEYWORDS.be.includes(source.source_id)
        )

        const shopeeSources = sources.filter((source: SourceData) =>
          PLATFORM_KEYWORDS.shopee.includes(source.source_id)
        )

        const taiChoSources = sources.filter((source: SourceData) =>
          PLATFORM_KEYWORDS['tai-cho'].includes(source.source_id)
        )

        // Calculate totals for each platform
        const grabRevenue = grabSources.reduce(
          (sum, source) => sum + (source.revenue_gross || 0),
          0
        )
        const beRevenue = beSources.reduce((sum, source) => sum + (source.revenue_gross || 0), 0)
        const shopeeRevenue = shopeeSources.reduce(
          (sum, source) => sum + (source.revenue_gross || 0),
          0
        )
        const taiChoRevenue = taiChoSources.reduce(
          (sum, source) => sum + (source.revenue_gross || 0),
          0
        )

        const grabTotalBill = grabSources.reduce((sum, source) => sum + (source.total_bill || 0), 0)
        const beTotalBill = beSources.reduce((sum, source) => sum + (source.total_bill || 0), 0)
        const shopeeTotalBill = shopeeSources.reduce(
          (sum, source) => sum + (source.total_bill || 0),
          0
        )
        const taiChoTotalBill = taiChoSources.reduce(
          (sum, source) => sum + (source.total_bill || 0),
          0
        )

        results.push({
          storeId,
          storeName,
          allSources: sources,
          grabSources,
          beSources,
          shopeeSources,
          taiChoSources,
          grabRevenue,
          beRevenue,
          shopeeRevenue,
          taiChoRevenue,
          grabTotalBill,
          beTotalBill,
          shopeeTotalBill,
          taiChoTotalBill
        })
      }
    })

    return results
  }, [queryResults])

  // Check loading and error states
  const isLoading = storeQueries.some(query => query.isLoading)
  const hasError = storeQueries.some(query => query.error)
  const errors = storeQueries.filter(query => query.error).map(query => query.error)

  // Calculate platform rankings
  const platformRankings = useMemo(() => {
    return {
      grab: {
        leastRevenue: [...processedStoresData].sort((a, b) => a.grabRevenue - b.grabRevenue),
        mostRevenue: [...processedStoresData].sort((a, b) => b.grabRevenue - a.grabRevenue)
      },
      be: {
        leastRevenue: [...processedStoresData].sort((a, b) => a.beRevenue - b.beRevenue),
        mostRevenue: [...processedStoresData].sort((a, b) => b.beRevenue - a.beRevenue)
      },
      shopee: {
        leastRevenue: [...processedStoresData].sort((a, b) => a.shopeeRevenue - b.shopeeRevenue),
        mostRevenue: [...processedStoresData].sort((a, b) => b.shopeeRevenue - a.shopeeRevenue)
      },
      taiCho: {
        leastRevenue: [...processedStoresData].sort((a, b) => a.taiChoRevenue - b.taiChoRevenue),
        mostRevenue: [...processedStoresData].sort((a, b) => b.taiChoRevenue - a.taiChoRevenue)
      }
    }
  }, [processedStoresData])

  return {
    storesData: processedStoresData,
    platformRankings,
    isLoading,
    hasError,
    errors,
    refetch: async () => {
      // Refetch all store queries in parallel
      const refetchPromises = storeQueries.map(query => query.refetch())
      await Promise.allSettled(refetchPromises)
    },
    // Additional debugging info
    queryInfo: {
      brandId: stableBrandId,
      companyId: stableCompanyId,
      filterType: stableFilterType,
      storeCount: storesToFetch?.length || 0
    }
  }
}

// Individual platform hooks
export function useGrabStores(options: UseSourcesByStoresOptions) {
  const result = useSourcesByStores(options)
  return {
    ...result,
    grabStores: result.storesData,
    grabRankings: result.platformRankings.grab
  }
}

export function useBeStores(options: UseSourcesByStoresOptions) {
  const result = useSourcesByStores(options)
  return {
    ...result,
    beStores: result.storesData,
    beRankings: result.platformRankings.be
  }
}

export function useShopeeStores(options: UseSourcesByStoresOptions) {
  const result = useSourcesByStores(options)
  return {
    ...result,
    shopeeStores: result.storesData,
    shopeeRankings: result.platformRankings.shopee
  }
}

export function useTaiChoStores(options: UseSourcesByStoresOptions) {
  const result = useSourcesByStores(options)
  return {
    ...result,
    taiChoStores: result.storesData,
    taiChoRankings: result.platformRankings.taiCho
  }
}

export default useSourcesByStores
