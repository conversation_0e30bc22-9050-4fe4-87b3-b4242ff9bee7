import { useMemo, useEffect, useState } from 'react'
import { useQueries } from '@tanstack/react-query'
import { usePosStores, useCurrentCompany } from '@/stores/posStore'
import { salesApi, SaleNotSyncVatData } from '@/lib/sales-api'
import { getStoredApiStores, fetchAndSyncStores } from '@/lib/stores-api'

// Default source ID for delivery (10000172)
export const DEFAULT_SOURCE_ID = 10000172

interface SaleNotSyncVatSummary {
  storeUid: string
  storeName: string
  totalTransactions: number
  totalPrice: number // amount_origin total
  totalAmount: number // net amount after discount
  discountAmount: number
  discountPercentage: number
  salesData: SaleNotSyncVatData[]
}

interface UseSaleNotSyncVatDataOptions {
  dateRange?: {
    from: Date
    to: Date
  }
  selectedStores?: string[]
  sourceId?: number
  autoFetch?: boolean
  paymentMethodId?: string // Add payment method filter
}

interface UseSaleNotSyncVatDataReturn {
  data: SaleNotSyncVatSummary[]
  totalPrice: number
  totalAmount: number
  totalTransactions: number
  isLoading: boolean
  error: string | null
  refetch: () => void
}

// Generate cache key with date range, stores, sourceId, and paymentMethodId
const generateCacheKey = (
  startTime: number,
  endTime: number,
  selectedStores: string[],
  sourceId: number,
  paymentMethodId?: string
) => {
  const storesKey = selectedStores.includes('all-stores')
    ? 'all-stores'
    : selectedStores.sort().join('-')
  const paymentKey = paymentMethodId ? `-${paymentMethodId}` : ''
  return `sale-not-sync-vat-data-${startTime}-${endTime}-${storesKey}-${sourceId}${paymentKey}`
}

// Cache data structure
interface CachedSaleNotSyncVatData {
  data: SaleNotSyncVatSummary[]
  totalPrice: number
  totalAmount: number
  totalTransactions: number
  timestamp: number
  selectedStores: string[]
  sourceId: number
  paymentMethodId?: string
}

// Save data to localStorage
const saveToCache = (key: string, data: CachedSaleNotSyncVatData) => {
  try {
    localStorage.setItem(key, JSON.stringify(data))
  } catch (_error) {
    // Ignore localStorage errors
  }
}

// Load data from localStorage
const loadFromCache = (key: string): CachedSaleNotSyncVatData | null => {
  try {
    const cached = localStorage.getItem(key)
    if (cached) {
      const parsed = JSON.parse(cached) as CachedSaleNotSyncVatData
      // Check if cache is not too old (24 hours)
      const maxAge = 24 * 60 * 60 * 1000 // 24 hours
      if (Date.now() - parsed.timestamp < maxAge) {
        return parsed
      }
    }
  } catch (_error) {
    // Ignore localStorage errors
  }
  return null
}

// Optimized filter function for "Tại chỗ", discount rules, and payment method
const filterSaleData = (
  salesData: SaleNotSyncVatData[],
  paymentMethodId?: string
): SaleNotSyncVatData[] => {
  // Pre-compile regex for better performance
  const tableServiceRegex = /TẠI CHỖ/i

  return salesData.filter((sale) => {
    // Filter 1: Only "Tại chỗ" (table_name contains "TẠI CHỖ")
    if (!sale.table_name || !tableServiceRegex.test(sale.table_name)) {
      return false
    }

    // Filter 2: Payment method filter (if specified)
    if (paymentMethodId && sale.payment_method_id !== paymentMethodId) {
      return false
    }

    // Filter 3: No discount OR discount <= 50%
    // Optimize: avoid division if amounts are equal
    if (sale.amount_origin === sale.total_amount) {
      return true // No discount
    }

    if (sale.amount_origin <= 0) {
      return false // Invalid data
    }

    const discountPercentage =
      ((sale.amount_origin - sale.total_amount) / sale.amount_origin) * 100
    return discountPercentage <= 50
  })
}

/**
 * Hook to fetch sale-not-sync-vat data with filtering for "Tại chỗ" and discount rules
 * Implements stale-while-revalidate pattern with localStorage persistence
 */
export function useSaleNotSyncVatData({
  dateRange,
  selectedStores = ['all-stores'],
  sourceId = DEFAULT_SOURCE_ID,
  autoFetch = true,
  paymentMethodId,
}: UseSaleNotSyncVatDataOptions): UseSaleNotSyncVatDataReturn {
  const { selectedBrand, currentBrandApiStores, setApiStores } = usePosStores()
  const { company } = useCurrentCompany()

  const fallbackCompanyUid = '269717a1-7bb6-4fa3-9150-dea2f709c081'
  const fallbackBrandUid = '5ed8968a-e4ed-4a04-870d-b53b7758fdc7'

  const companyId = company?.id || fallbackCompanyUid
  const brandId = selectedBrand?.id || fallbackBrandUid

  const stableBrandId = useMemo(() => brandId, [brandId])
  const stableCompanyId = useMemo(() => companyId, [companyId])

  const [cachedData, setCachedData] = useState<CachedSaleNotSyncVatData | null>(
    null
  )
  const [isLoadingFromCache, setIsLoadingFromCache] = useState(true)

  // Clear cached data when brand changes
  useEffect(() => {
    setCachedData(null)
    setIsLoadingFromCache(true)
  }, [stableBrandId])

  // Fetch stores when brand changes and no stores are available
  useEffect(() => {
    const fetchStores = async () => {
      if (
        stableCompanyId &&
        stableBrandId &&
        currentBrandApiStores.length === 0
      ) {
        try {
          const fetchedStores = await fetchAndSyncStores(
            stableCompanyId,
            stableBrandId
          )
          setApiStores(fetchedStores)
        } catch (_error) {
          // Silent error handling - will use cached stores if available
        }
      }
    }

    fetchStores()
  }, [
    stableCompanyId,
    stableBrandId,
    currentBrandApiStores.length,
    setApiStores,
  ])

  const { startTime, endTime } = useMemo(() => {
    if (!dateRange?.from || !dateRange?.to) {
      return {
        startTime: null,
        endTime: null,
      }
    }

    const startOfDay = new Date(dateRange.from)
    startOfDay.setHours(0, 0, 0, 0)

    const endOfDay = new Date(dateRange.to)
    endOfDay.setHours(23, 59, 59, 999)

    return {
      startTime: startOfDay.getTime(),
      endTime: endOfDay.getTime(),
    }
  }, [dateRange])

  const storeNameMap = useMemo(() => {
    const map = new Map<string, string>()
    currentBrandApiStores?.forEach((store) => {
      map.set(store.id, store.store_name)
    })
    return map
  }, [currentBrandApiStores])

  const storesToFetch = useMemo(() => {
    if (!currentBrandApiStores || currentBrandApiStores.length === 0) {
      return []
    }

    if (selectedStores.includes('all-stores')) {
      // Limit to first 20 stores for performance
      return currentBrandApiStores.slice(0, 20).map((store) => store.id)
    }

    return selectedStores
      .filter((storeId) =>
        currentBrandApiStores.some((store) => store.id === storeId)
      )
      .slice(0, 20) // Limit concurrent requests
  }, [currentBrandApiStores, selectedStores])

  // Generate cache key with date range, stores, sourceId, and paymentMethodId
  const cacheKey = useMemo(() => {
    if (!startTime || !endTime) {
      return null
    }
    return generateCacheKey(
      startTime,
      endTime,
      selectedStores,
      sourceId,
      paymentMethodId
    )
  }, [startTime, endTime, selectedStores, sourceId, paymentMethodId])

  // Load from cache when cache key changes
  useEffect(() => {
    if (cacheKey) {
      const cached = loadFromCache(cacheKey)
      if (cached) {
        setCachedData(cached)
      }
    }
    setIsLoadingFromCache(false)
  }, [cacheKey])

  // Optimized: Fetch sale-not-sync-vat data with batching and pagination control
  const saleNotSyncVatQueries = useQueries({
    queries: storesToFetch.map((storeUid) => ({
      queryKey: [
        'sale-not-sync-vat-data',
        stableCompanyId,
        stableBrandId,
        storeUid,
        startTime,
        endTime,
        sourceId,
      ],
      queryFn: async () => {
        if (!stableBrandId || !stableCompanyId || !startTime || !endTime) {
          throw new Error('Missing required parameters')
        }

        // Get store name first (cached)
        const storeNameFromMap = storeNameMap.get(storeUid)
        const storeInfo = currentBrandApiStores?.find(
          (store) => store.id === storeUid
        )
        const storedStores = getStoredApiStores()
        const storedStoreInfo = storedStores.find(
          (store) => store.id === storeUid
        )
        const storeName =
          storeNameFromMap ||
          storeInfo?.store_name ||
          storedStoreInfo?.store_name ||
          `Store ${storeUid}`

        // Optimized: Fetch with limited pages and early filtering
        const allSalesData: SaleNotSyncVatData[] = []
        let currentPage = 1
        const maxPages = 10 // Limit to prevent infinite loops
        const resultsPerPage = 50 // Larger page size for efficiency

        while (currentPage <= maxPages) {
          try {
            const response = await salesApi.getSaleNotSyncVat({
              companyUid: stableCompanyId,
              brandUid: stableBrandId,
              storeUid,
              startDate: startTime,
              endDate: endTime,
              sourceId,
              page: currentPage,
              resultsPerPage,
            })

            if (!response.data || response.data.length === 0) {
              break
            }

            // Apply filters immediately to reduce memory usage
            const filteredPageData = filterSaleData(
              response.data,
              paymentMethodId
            )
            allSalesData.push(...filteredPageData)

            // Early break if we have enough data
            if (allSalesData.length >= 1000) {
              break
            }

            currentPage++
          } catch (_error) {
            // Error fetching page, break the loop
            break
          }
        }

        return {
          storeId: storeUid,
          storeName,
          salesData: allSalesData,
          hasData: allSalesData.length > 0,
        }
      },
      enabled:
        autoFetch &&
        !!stableBrandId &&
        !!stableCompanyId &&
        !!startTime &&
        !!endTime,
      staleTime: 10 * 60 * 1000, // Increased to 10 minutes
      gcTime: 30 * 60 * 1000, // Increased to 30 minutes
      refetchInterval: 30 * 60 * 1000, // Reduced frequency to 30 minutes
      refetchIntervalInBackground: false, // Disable background refetch for performance
      retry: 1, // Reduced retry attempts
    })),
  })

  // Extract data and status arrays
  const saleData = saleNotSyncVatQueries.map((q) => q.data)
  const saleStatus = saleNotSyncVatQueries.map((q) => q.status)

  // Process and combine data from all stores
  const processedData = useMemo(() => {
    const summaries: SaleNotSyncVatSummary[] = []
    let totalPrice = 0
    let totalAmount = 0
    let totalTransactions = 0

    // Process sale data
    saleData.forEach((storeData, index) => {
      if (storeData?.hasData && saleStatus[index] === 'success') {
        const { salesData } = storeData

        // Calculate totals for this store
        const storeAmountOrigin = salesData.reduce(
          (sum, sale) => sum + sale.amount_origin,
          0
        )
        const storeTotalAmount = salesData.reduce(
          (sum, sale) => sum + sale.total_amount,
          0
        )
        const storeDiscountAmount = storeAmountOrigin - storeTotalAmount
        const storeDiscountPercentage =
          storeAmountOrigin > 0
            ? (storeDiscountAmount / storeAmountOrigin) * 100
            : 0

        summaries.push({
          storeUid: storeData.storeId,
          storeName: storeData.storeName,
          totalTransactions: salesData.length,
          totalPrice: storeAmountOrigin,
          totalAmount: storeTotalAmount,
          discountAmount: storeDiscountAmount,
          discountPercentage: storeDiscountPercentage,
          salesData,
        })

        totalPrice += storeAmountOrigin
        totalAmount += storeTotalAmount
        totalTransactions += salesData.length
      }
    })

    const result = {
      data: summaries,
      totalPrice,
      totalAmount,
      totalTransactions,
    }

    // Save to cache when data is successfully processed
    if (cacheKey && summaries.length > 0) {
      const cacheData: CachedSaleNotSyncVatData = {
        ...result,
        timestamp: Date.now(),
        selectedStores,
        sourceId,
        paymentMethodId,
      }
      saveToCache(cacheKey, cacheData)
    }

    return result
  }, [
    saleData,
    saleStatus,
    cacheKey,
    selectedStores,
    sourceId,
    paymentMethodId,
  ])

  // Check loading and error states
  const isApiLoading = saleNotSyncVatQueries.some((query) => query.isLoading)

  const error =
    saleNotSyncVatQueries.find((query) => query.error)?.error?.message || null

  // Refetch function
  const refetch = () => {
    saleNotSyncVatQueries.forEach((query) => query.refetch())
  }

  const hasFreshData = processedData.data.length > 0

  // Stale-while-revalidate pattern: show cached data immediately, then fresh data
  const finalData = hasFreshData ? processedData : cachedData || processedData

  // Show loading only when no cached data is available
  const isLoading = isLoadingFromCache || (isApiLoading && !cachedData)

  return {
    ...finalData,
    isLoading,
    error,
    refetch,
  }
}

export default useSaleNotSyncVatData
