import { useMemo } from 'react'
import type { Brand, Store, City } from '@/lib/auth-api'
import { useCurrentUser } from './use-auth'

/**
 * Hook to easily access POS data with utility functions
 */
export const usePosData = () => {
  const {
    user,
    userRole,
    company,
    brands,
    cities,
    stores,
    activeBrands,
    activeStores,
    isAuthenticated,
  } = useCurrentUser()

  // Memoize utility functions to prevent infinite loops
  const utilityFunctions = useMemo(
    () => ({
      /**
       * Get brand by ID
       */
      getBrandById: (brandId: string): Brand | undefined => {
        return brands.find(
          (brand) => brand.id === brandId || brand.brand_id === brandId
        )
      },

      /**
       * Get store by ID
       */
      getStoreById: (storeId: string): Store | undefined => {
        return stores.find(
          (store) => store.id === storeId || store.store_id === storeId
        )
      },

      /**
       * Get city by ID
       */
      getCityById: (cityId: string): City | undefined => {
        return cities.find(
          (city) => city.id === cityId || city.city_id === cityId
        )
      },

      /**
       * Get stores by brand
       */
      getStoresByBrand: (brandId: string): Store[] => {
        return stores.filter((store) => store.brand_uid === brandId)
      },

      /**
       * Get stores by city
       */
      getStoresByCity: (cityId: string): Store[] => {
        return stores.filter((store) => store.city_uid === cityId)
      },

      /**
       * Get active stores by brand
       */
      getActiveStoresByBrand: (brandId: string): Store[] => {
        return stores.filter(
          (store) => store.brand_uid === brandId && store.active === 1
        )
      },

      /**
       * Check if user has access to specific store
       */
      hasStoreAccess: (storeId: string): boolean => {
        // For now, return true if user is authenticated
        // You can implement more complex logic based on user permissions
        return (
          isAuthenticated &&
          stores.some(
            (store) => store.id === storeId || store.store_id === storeId
          )
        )
      },

      /**
       * Get user's primary brand (first active brand)
       */
      getPrimaryBrand: (): Brand | undefined => {
        return activeBrands[0]
      },

      /**
       * Get user's primary store (first active store)
       */
      getPrimaryStore: (): Store | undefined => {
        return activeStores[0]
      },

      /**
       * Get summary statistics
       */
      getStats: () => ({
        totalBrands: brands.length,
        activeBrands: activeBrands.length,
        totalStores: stores.length,
        activeStores: activeStores.length,
        totalCities: cities.length,
        userRole: userRole?.role_name,
        companyName: company?.company_name,
      }),

      /**
       * Export all data for debugging or backup
       */
      exportData: () => ({
        user,
        userRole,
        company,
        brands,
        cities,
        stores,
        timestamp: new Date().toISOString(),
      }),
    }),
    [
      brands,
      stores,
      cities,
      activeBrands,
      activeStores,
      isAuthenticated,
      userRole,
      company,
      user,
    ]
  )

  return {
    // Raw data
    user,
    userRole,
    company,
    brands,
    cities,
    stores,

    // Filtered data
    activeBrands,
    activeStores,

    // Utility functions
    isAuthenticated,

    // Memoized utility functions
    ...utilityFunctions,
  }
}

/**
 * Hook to get localStorage data directly (for debugging)
 */
export const useLocalStorageData = () => {
  const getUserData = () => {
    try {
      const stored = localStorage.getItem('pos_user_data')
      return stored ? JSON.parse(stored) : null
    } catch {
      return null
    }
  }

  const getJwtToken = () => {
    try {
      return localStorage.getItem('pos_jwt_token') || null
    } catch {
      return null
    }
  }

  const clearAllData = () => {
    try {
      localStorage.removeItem('pos_user_data')
      localStorage.removeItem('pos_jwt_token')
      return true
    } catch {
      return false
    }
  }

  return {
    getUserData,
    getJwtToken,
    clearAllData,

    // Get all POS-related localStorage keys
    getAllKeys: () => {
      const keys = []
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i)
        if (key && key.startsWith('pos_')) {
          keys.push(key)
        }
      }
      return keys
    },
  }
}
