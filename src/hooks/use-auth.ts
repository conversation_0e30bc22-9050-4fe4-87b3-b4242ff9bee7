import * as React from 'react'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { useRouter } from '@tanstack/react-router'
import { toast } from 'sonner'
import { useAuthStore } from '@/stores/authStore'
import { usePosStore, initializePosStore } from '@/stores/posStore'
import { authApi, type LoginRequest, type LoginResponse } from '@/lib/auth-api'

/**
 * Query keys for auth-related queries
 */
export const authKeys = {
  all: ['auth'] as const,
  profile: () => [...authKeys.all, 'profile'] as const,
  verify: () => [...authKeys.all, 'verify'] as const,
}

interface AuthError {
  response?: {
    data?: {
      message?: string
      error?: string
    }
    status?: number
  }
  message?: string
}

/**
 * Hook for user login
 */
export const useLogin = () => {
  const router = useRouter()
  const queryClient = useQueryClient()
  const { setLoginData } = useAuthStore((state) => state.auth)
  // Import initializePosStore directly since it's not part of the store state

  return useMutation({
    mutationFn: (credentials: LoginRequest) => authApi.login(credentials),
    onSuccess: (data: LoginResponse) => {
      // Store auth data
      setLoginData(data)

      // Initialize the new unified POS store
      initializePosStore(data)

      // Invalidate and refetch user profile
      queryClient.invalidateQueries({ queryKey: authKeys.profile() })

      // Show success message with user name
      toast.success(`Welcome back, ${data.user.full_name || data.user.email}!`)

      // Redirect to dashboard or intended page
      const searchParams = new URLSearchParams(router.history.location.search)
      const redirect = searchParams.get('redirect') || '/'
      router.navigate({ to: redirect })
    },
    onError: (error: AuthError) => {
      const errorMessage =
        error.response?.data?.message ||
        error.response?.data?.error ||
        'Login failed. Please check your credentials.'
      toast.error(errorMessage)
    },
  })
}

/**
 * Hook for user logout
 */
export const useLogout = () => {
  const router = useRouter()
  const queryClient = useQueryClient()
  const { reset } = useAuthStore((state) => state.auth)
  const { clearAuth } = usePosStore()

  return useMutation({
    mutationFn: () => authApi.logout(),
    onSuccess: () => {
      // Clear auth state
      reset()

      // Clear POS store
      clearAuth()

      // Clear all queries
      queryClient.clear()

      // Show success message
      toast.success('Logged out successfully!')

      // Redirect to login
      router.navigate({ to: '/sign-in' })
    },
    onError: (_error: AuthError) => {
      // Still clear local state even if API call fails
      reset()
      clearAuth()
      queryClient.clear()
      router.navigate({ to: '/sign-in' })
    },
  })
}

/**
 * Hook to get user profile
 */
export const useProfile = () => {
  const { accessToken } = useAuthStore((state) => state.auth)

  return useQuery({
    queryKey: authKeys.profile(),
    queryFn: () => authApi.getProfile(),
    enabled: !!accessToken,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: (failureCount, error: AuthError) => {
      // Don't retry on 401/403 errors
      if (error?.response?.status === 401 || error?.response?.status === 403) {
        return false
      }
      return failureCount < 3
    },
  })
}

/**
 * Hook to verify token
 */
export const useVerifyToken = () => {
  const { accessToken } = useAuthStore((state) => state.auth)

  return useQuery({
    queryKey: authKeys.verify(),
    queryFn: () => authApi.verifyToken(),
    enabled: !!accessToken,
    staleTime: 2 * 60 * 1000, // 2 minutes
    retry: false,
  })
}

/**
 * Hook to refresh token
 */
export const useRefreshToken = () => {
  const { setLoginData } = useAuthStore((state) => state.auth)

  return useMutation({
    mutationFn: () => authApi.refreshToken(),
    onSuccess: (data: LoginResponse) => {
      // Store complete refresh response data
      setLoginData(data)
    },
    onError: (_error: AuthError) => {
      // If refresh fails, user needs to login again
      useAuthStore.getState().auth.reset()
    },
  })
}

/**
 * Hook to get current user data
 */
export const useCurrentUser = () => {
  // Use separate selectors to avoid recomputation issues
  const user = useAuthStore((state) => state.auth.user)
  const userRole = useAuthStore((state) => state.auth.userRole)
  const company = useAuthStore((state) => state.auth.company)
  const brands = useAuthStore((state) => state.auth.brands)
  const cities = useAuthStore((state) => state.auth.cities)
  const stores = useAuthStore((state) => state.auth.stores)
  const jwtToken = useAuthStore((state) => state.auth.jwtToken)

  // Compute derived values with useMemo to prevent infinite loops
  const isAuthenticated = React.useMemo(() => {
    return !!(user && jwtToken)
  }, [user, jwtToken])

  const activeBrands = React.useMemo(() => {
    return brands.filter((brand) => brand.active === 1)
  }, [brands])

  const activeStores = React.useMemo(() => {
    return stores.filter((store) => store.active === 1)
  }, [stores])

  const currentStore = React.useMemo(() => {
    return activeStores[0] || null
  }, [activeStores])

  return {
    user,
    userRole,
    company,
    brands,
    cities,
    stores,
    isAuthenticated,
    currentStore,
    activeBrands,
    activeStores,
  }
}

/**
 * Hook to check if user has specific permissions
 */
export const usePermissions = () => {
  const { userRole } = useAuthStore((state) => state.auth)

  return {
    hasAccess: (access: string) => {
      return userRole?.allow_access.includes(access) || false
    },
    isOwner: () => {
      return userRole?.role_id === 'OWNER'
    },
    canAccessPOS: () => {
      return userRole?.allow_access.includes('POS_CLIENT') || false
    },
    canAccessCMS: () => {
      return userRole?.allow_access.includes('POS_CMS') || false
    },
    canAccessManager: () => {
      return userRole?.allow_access.includes('POS_MANAGER') || false
    },
  }
}
