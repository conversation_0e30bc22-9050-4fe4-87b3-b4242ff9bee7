import { useMemo, useEffect } from 'react'
import { useQuery, useQueryClient } from '@tanstack/react-query'
import { usePosStores, useCurrentCompany } from '@/stores/posStore'
import { revenueApi } from '@/lib/revenue-api'

interface StoreDiscountData {
  storeUid: string
  storeName: string
  totalSales: number
  revenueGross: number
  discountAmount: number
  discountRate: number // Calculated as (discount_amount / revenue_gross) * 100
  revenueNet: number
  discountExtraAmount: number
  voucherAmount: number
  partnerMarketingAmount: number
}

interface UseStoresDiscountDataOptions {
  dateRange?: {
    from: Date
    to: Date
  }
  selectedStores?: string[]
  autoFetch?: boolean
}

interface UseStoresDiscountDataReturn {
  storesData: StoreDiscountData[]
  isLoading: boolean
  error: string | null
  totalStores: number
  averageDiscountRate: number
  refetch: () => void
}

/**
 * Query keys for stores discount data queries
 */
export const storesDiscountKeys = {
  all: ['stores-discount'] as const,
  lists: () => [...storesDiscountKeys.all, 'list'] as const,
  list: (filters: {
    companyUid?: string
    brandUid?: string
    startDate?: number
    endDate?: number
    storeUids?: string[]
  }) => [...storesDiscountKeys.lists(), filters] as const,
}

/**
 * Hook to get stores discount data using TanStack Query
 * Fetches stores data with by_days=0 and calculates discount rates
 */
export function useStoresDiscountData(
  options: UseStoresDiscountDataOptions = {}
): UseStoresDiscountDataReturn {
  const {
    dateRange,
    selectedStores = ['all-stores'],
    autoFetch = true,
  } = options

  const { selectedBrand, currentBrandStores } = usePosStores()
  const { company } = useCurrentCompany()
  const queryClient = useQueryClient()

  const startTime = useMemo(() => dateRange?.from?.getTime(), [dateRange?.from])
  const endTime = useMemo(() => dateRange?.to?.getTime(), [dateRange?.to])
  const brandId = selectedBrand?.id
  const companyId = company?.id

  // Listen for brand changes and invalidate queries
  useEffect(() => {
    const handleBrandChange = () => {
      // Invalidate all stores-discount queries when brand changes
      queryClient.invalidateQueries({
        queryKey: storesDiscountKeys.all,
      })
    }

    window.addEventListener('brandChanged', handleBrandChange)
    return () => {
      window.removeEventListener('brandChanged', handleBrandChange)
    }
  }, [queryClient])

  // Prepare store UIDs for API call
  const storeUids = useMemo(() => {
    if (
      selectedStores &&
      selectedStores.length > 0 &&
      !selectedStores.includes('all-stores')
    ) {
      return selectedStores.filter(
        (id) => id !== 'all-stores' && id !== 'no-stores'
      )
    } else {
      const activeStores = currentBrandStores.filter(
        (store) => store.active === 1
      )
      return activeStores.length > 0
        ? activeStores.map((store) => store.id)
        : undefined
    }
  }, [selectedStores, currentBrandStores])

  // TanStack Query for fetching stores data
  const {
    data: rawData,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: storesDiscountKeys.list({
      companyUid: companyId,
      brandUid: brandId,
      startDate: startTime,
      endDate: endTime,
      storeUids,
    }),
    queryFn: async () => {
      if (!brandId || !companyId || !startTime || !endTime) {
        throw new Error('Brand, company, or date range not selected')
      }

      return await revenueApi.getRevenueSummary({
        companyUid: companyId,
        brandUid: brandId,
        startDate: startTime,
        endDate: endTime,
        storeUids,
        byDays: 0, // Important: by_days=0 for aggregated data
        limit: 1000,
      })
    },
    enabled: autoFetch && !!brandId && !!companyId && !!startTime && !!endTime,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes (garbage collection time)
    retry: (failureCount, error) => {
      // Don't retry on authentication errors
      if (error?.message?.includes('401') || error?.message?.includes('403')) {
        return false
      }
      return failureCount < 3
    },
  })

  // Process the raw data into discount data format
  const storesData = useMemo(() => {
    if (!rawData?.data) return []

    return (
      rawData.data
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        .filter((store: any) => store.revenue_gross > 0)
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        .map((store: any) => {
          const discountRate =
            store.revenue_gross > 0
              ? (store.discount_amount / store.revenue_gross) * 100
              : 0

          return {
            storeUid: store.store_uid,
            storeName: store.store_name,
            totalSales: store.total_sales || 0,
            revenueGross: store.revenue_gross || 0,
            discountAmount: store.discount_amount || 0,
            discountRate: Math.round(discountRate * 100) / 100,
            revenueNet: store.revenue_net || 0,
            discountExtraAmount: store.discount_extra_amount || 0,
            voucherAmount: store.voucher_amount || 0,
            partnerMarketingAmount: store.partner_marketing_amount || 0,
          }
        })
        .sort((a, b) => b.discountRate - a.discountRate)
    )
  }, [rawData?.data])

  // Memoized computed values
  const totalStores = useMemo(() => storesData.length, [storesData])

  const averageDiscountRate = useMemo(() => {
    if (storesData.length === 0) return 0
    const totalRate = storesData.reduce(
      (sum, store) => sum + store.discountRate,
      0
    )
    return Math.round((totalRate / storesData.length) * 100) / 100
  }, [storesData])

  return {
    storesData,
    isLoading,
    error: error?.message || null,
    totalStores,
    averageDiscountRate,
    refetch: () => {
      refetch()
    },
  }
}

export default useStoresDiscountData
