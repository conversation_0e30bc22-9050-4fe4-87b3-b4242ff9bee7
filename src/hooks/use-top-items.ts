import { useMemo } from 'react'
import { useItems } from './use-items'

interface TopItem {
  itemId: string
  itemName: string
  itemClass: string
  itemType: string
  quantitySold: number
  revenue: number
  revenueNet: number
  percentage: number
  unit: string
}

interface UseTopItemsReturn {
  topItems: TopItem[]
  isLoading: boolean
  error: string | null
}

/**
 * Hook to get top items with least sales (slowest selling items)
 */
export function useTopItemsLeastSales(dateRange?: {
  from: Date
  to: Date
}): UseTopItemsReturn {
  const { processedItems, isLoading, error } = useItems({
    dateRange,
    autoFetch: true,
  })

  const topItems = useMemo(() => {
    if (!processedItems || processedItems.length === 0) {
      return []
    }

    // API returns data sorted by quantity_sold descending (high to low)
    // To get the 5 items with lowest sales, we take the last 5 items and reverse them
    // so they display from lowest to highest
    return processedItems
      .slice(-5) // Take last 5 items (lowest sales)
      .reverse() // Reverse to show from lowest to highest
  }, [processedItems])

  return {
    topItems,
    isLoading,
    error,
  }
}

/**
 * Hook to get top items with most sales (fastest selling items)
 */
export function useTopItemsMostSales(
  dateRange?: { from: Date; to: Date },
  filterType: 'monthly' | 'daily' = 'daily'
): UseTopItemsReturn {
  const { processedItems, isLoading, error } = useItems({
    dateRange,
    filterType,
    autoFetch: true,
  })

  const topItems = useMemo(() => {
    if (!processedItems || processedItems.length === 0) {
      return []
    }

    // Sort by quantity sold (descending - most sales first)
    return processedItems
      .sort((a, b) => b.quantitySold - a.quantitySold)
      .slice(0, 5) // Top 5 best selling items
  }, [processedItems])

  return {
    topItems,
    isLoading,
    error,
  }
}

/**
 * Hook to get top items with least revenue
 */
export function useTopItemsLeastRevenue(
  dateRange?: { from: Date; to: Date },
  filterType: 'monthly' | 'daily' = 'daily'
): UseTopItemsReturn {
  const { processedItems, isLoading, error } = useItems({
    dateRange,
    filterType,
    autoFetch: true,
  })

  const topItems = useMemo(() => {
    if (!processedItems || processedItems.length === 0) {
      return []
    }

    // Sort by revenue (ascending - least revenue first)
    return processedItems.sort((a, b) => a.revenue - b.revenue).slice(0, 5) // Top 5 least revenue items
  }, [processedItems])

  return {
    topItems,
    isLoading,
    error,
  }
}

/**
 * Hook to get top items with most revenue
 */
export function useTopItemsMostRevenue(
  dateRange?: { from: Date; to: Date },
  filterType: 'monthly' | 'daily' = 'daily'
): UseTopItemsReturn {
  const { processedItems, isLoading, error } = useItems({
    dateRange,
    filterType,
    autoFetch: true,
  })

  const topItems = useMemo(() => {
    if (!processedItems || processedItems.length === 0) {
      return []
    }

    // Sort by revenue (descending - most revenue first)
    return processedItems.sort((a, b) => b.revenue - a.revenue).slice(0, 5) // Top 5 highest revenue items
  }, [processedItems])

  return {
    topItems,
    isLoading,
    error,
  }
}

/**
 * Hook to get items by percentage (market share)
 */
export function useTopItemsByPercentage(
  dateRange?: { from: Date; to: Date },
  filterType: 'monthly' | 'daily' = 'daily',
  order: 'asc' | 'desc' = 'desc'
): UseTopItemsReturn {
  const { processedItems, isLoading, error } = useItems({
    dateRange,
    filterType,
    autoFetch: true,
  })

  const topItems = useMemo(() => {
    if (!processedItems || processedItems.length === 0) {
      return []
    }

    // Sort by percentage
    const sorted = processedItems.sort((a, b) => {
      return order === 'desc'
        ? b.percentage - a.percentage
        : a.percentage - b.percentage
    })

    return sorted.slice(0, 5) // Top 5 items by percentage
  }, [processedItems, order])

  return {
    topItems,
    isLoading,
    error,
  }
}

/**
 * Generic hook to get top items with custom sorting
 */
export function useTopItemsCustom(
  dateRange?: { from: Date; to: Date },
  filterType: 'monthly' | 'daily' = 'daily',
  sortBy: keyof TopItem = 'quantitySold',
  order: 'asc' | 'desc' = 'desc',
  limit: number = 5
): UseTopItemsReturn {
  const { processedItems, isLoading, error } = useItems({
    dateRange,
    filterType,
    autoFetch: true,
  })

  const topItems = useMemo(() => {
    if (!processedItems || processedItems.length === 0) {
      return []
    }

    // Sort by specified field
    const sorted = processedItems.sort((a, b) => {
      const aValue = a[sortBy]
      const bValue = b[sortBy]

      if (typeof aValue === 'number' && typeof bValue === 'number') {
        return order === 'desc' ? bValue - aValue : aValue - bValue
      } else {
        // For string comparison
        const aStr = String(aValue)
        const bStr = String(bValue)
        return order === 'desc'
          ? bStr.localeCompare(aStr)
          : aStr.localeCompare(bStr)
      }
    })

    return sorted.slice(0, limit)
  }, [processedItems, sortBy, order, limit])

  return {
    topItems,
    isLoading,
    error,
  }
}
