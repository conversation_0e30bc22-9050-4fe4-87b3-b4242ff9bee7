import { useQuery } from '@tanstack/react-query'

import { unitsApi, type GetUnitsParams, type Unit } from '@/lib/units-api'

import { QUERY_KEYS } from '@/constants/query-keys'

export interface UseUnitsDataOptions {
  params?: Partial<GetUnitsParams>
  enabled?: boolean
}

export const useUnitsData = (options: UseUnitsDataOptions = {}) => {
  const { params = {}, enabled = true } = options

  const dynamicParams: GetUnitsParams = {
    active: 1, // Only active units by default
    sort: 'sort',
    ...params
  }

  return useQuery({
    queryKey: [QUERY_KEYS.UNITS, dynamicParams],
    queryFn: async (): Promise<Unit[]> => {
      const response = await unitsApi.getUnits(dynamicParams)
      return response.data || []
    },
    enabled: enabled,
    staleTime: 30 * 60 * 1000, // 30 minutes
    refetchInterval: 60 * 60 * 1000, // 1 hour (units change very rarely)
    retry: (failureCount, error) => {
      // Don't retry on auth errors
      if (error?.message?.includes('401') || error?.message?.includes('403')) {
        return false
      }
      return failureCount < 3
    }
  })
}

// Hook for filtered units data
export const useFilteredUnitsData = (
  filters: {
    searchTerm?: string
    active?: number
  } = {}
) => {
  const { searchTerm, active } = filters

  const unitsQuery = useUnitsData({
    params: {
      search: searchTerm,
      active
    }
  })

  return {
    ...unitsQuery,
    data: unitsQuery.data || []
  }
}

// Hook for getting a specific unit by ID
export const useUnitData = (unitId: string, enabled: boolean = true) => {
  const unitsQuery = useUnitsData()

  return useQuery({
    queryKey: [QUERY_KEYS.UNITS, 'detail', unitId],
    queryFn: async (): Promise<Unit | undefined> => {
      // First try to find in cached data
      const cachedUnit = unitsApi.getUnitById(unitId)
      if (cachedUnit) {
        return cachedUnit
      }

      // If not found in cache, use the data from the main query
      return unitsQuery.data?.find(unit => unit.id === unitId)
    },
    enabled: enabled && !!unitId,
    staleTime: 30 * 60 * 1000 // 30 minutes
  })
}

// Hook for getting unit by unit_id
export const useUnitByUnitId = (unitId: string, enabled: boolean = true) => {
  const unitsQuery = useUnitsData()

  return useQuery({
    queryKey: [QUERY_KEYS.UNITS, 'by-unit-id', unitId],
    queryFn: async (): Promise<Unit | undefined> => {
      // First try to find in cached data
      const cachedUnit = unitsApi.getUnitByUnitId(unitId)
      if (cachedUnit) {
        return cachedUnit
      }

      // If not found in cache, use the data from the main query
      return unitsQuery.data?.find(unit => unit.unit_id === unitId)
    },
    enabled: enabled && !!unitId,
    staleTime: 30 * 60 * 1000 // 30 minutes
  })
}

// Hook for units stats
export const useUnitsStats = () => {
  const unitsQuery = useUnitsData()

  const stats = {
    total: unitsQuery.data?.length || 0,
    active: unitsQuery.data?.filter(unit => unit.active === 1).length || 0,
    inactive: unitsQuery.data?.filter(unit => unit.active === 0).length || 0
  }

  return {
    ...unitsQuery,
    stats
  }
}

// Hook for units as options (for select components)
export const useUnitsOptions = () => {
  const unitsQuery = useUnitsData()

  const options =
    unitsQuery.data?.map(unit => ({
      label: unit.unit_name,
      value: unit.id,
      unitId: unit.unit_id,
      symbol: unit.unit_symbol,
      active: unit.active === 1
    })) || []

  return {
    ...unitsQuery,
    options,
    activeOptions: options.filter(option => option.active)
  }
}
