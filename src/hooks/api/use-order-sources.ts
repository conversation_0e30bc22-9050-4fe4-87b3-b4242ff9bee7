import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'

import type {
  OrderSourceDetail,
  CreateOrderSourceRequest,
  UpdateOrderSourceRequest
} from '@/types/api/order-sources'
import { toast } from 'sonner'

import { useAuthStore } from '@/stores/authStore'

import { orderSourcesApi } from '@/lib/order-sources-api'

const QUERY_KEYS = {
  ORDER_SOURCES_LIST: 'order-sources-list'
}

export const useOrderSources = (page: number = 1, search?: string, storeUid?: string) => {
  const { company, brands } = useAuthStore(state => state.auth)
  const selectedBrand = brands?.[0]

  return useQuery({
    queryKey: [QUERY_KEYS.ORDER_SOURCES_LIST, page, search, storeUid],
    queryFn: async () => {
      const companyUid = company?.id || ''
      const brandUid = selectedBrand?.id || ''

      if (!companyUid || !brandUid) {
        throw new Error('Thiếu thông tin công ty hoặc thương hiệu')
      }

      return await orderSourcesApi.getOrderSources(companyUid, brandUid, page, search, storeUid)
    },
    enabled: !!(company?.id && selectedBrand?.id && storeUid),
    staleTime: 5 * 60 * 1000 // 5 minutes
  })
}

export const useUpdateOrderSource = () => {
  const queryClient = useQueryClient()

  const { mutate, isPending } = useMutation({
    mutationFn: async (data: UpdateOrderSourceRequest) => {
      return await orderSourcesApi.updateOrderSource(data)
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.ORDER_SOURCES_LIST]
      })
      toast.success('Cập nhật nguồn đơn hàng thành công')
    },
    onError: (error: any) => {
      const errorMessage =
        error?.response?.data?.message || 'Có lỗi xảy ra khi cập nhật nguồn đơn hàng'
      toast.error(errorMessage)
    }
  })

  return {
    updateOrderSource: mutate,
    isUpdating: isPending
  }
}

export const useOrderSourcesForSort = () => {
  const { company, brands } = useAuthStore(state => state.auth)
  const selectedBrand = brands?.[0]

  return useQuery({
    queryKey: [QUERY_KEYS.ORDER_SOURCES_LIST, 'sort'],
    queryFn: async () => {
      const companyUid = company?.id || ''
      const brandUid = selectedBrand?.id || ''

      if (!companyUid || !brandUid) {
        throw new Error('Thiếu thông tin công ty hoặc thương hiệu')
      }

      return await orderSourcesApi.getOrderSourcesForSort(companyUid, brandUid)
    },
    enabled: !!(company?.id && selectedBrand?.id),
    staleTime: 5 * 60 * 1000 // 5 minutes
  })
}

export const useCreateOrderSource = () => {
  const queryClient = useQueryClient()
  const { company, brands } = useAuthStore(state => state.auth)
  const selectedBrand = brands?.[0]

  const { mutate, isPending } = useMutation({
    mutationFn: async (data: CreateOrderSourceRequest) => {
      const companyUid = company?.id || ''
      const brandUid = selectedBrand?.id || ''

      if (!companyUid || !brandUid) {
        throw new Error('Thiếu thông tin công ty hoặc thương hiệu')
      }

      const createData = {
        ...data,
        company_uid: companyUid,
        brand_uid: brandUid
      }

      return await orderSourcesApi.createOrderSource(createData)
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.ORDER_SOURCES_LIST]
      })
      toast.success('Tạo nguồn đơn hàng thành công')
    },
    onError: (error: any) => {
      const errorMessage = error?.response?.data?.message || 'Có lỗi xảy ra khi tạo nguồn đơn hàng'
      toast.error(errorMessage)
    }
  })

  return {
    createOrderSource: mutate,
    isCreating: isPending
  }
}

export const useUpdateOrderSourcesSort = () => {
  const queryClient = useQueryClient()
  const { company, brands } = useAuthStore(state => state.auth)
  const selectedBrand = brands?.[0]

  const { mutate, isPending } = useMutation({
    mutationFn: async (sources: Array<{ source_id: string; sort: number }>) => {
      const companyUid = company?.id || ''
      const brandUid = selectedBrand?.id || ''

      if (!companyUid || !brandUid) {
        throw new Error('Thiếu thông tin công ty hoặc thương hiệu')
      }

      const sortData = sources.map(source => ({
        source_id: source.source_id,
        sort: source.sort,
        company_uid: companyUid,
        brand_uid: brandUid
      }))

      return await orderSourcesApi.updateOrderSourcesSort(sortData)
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.ORDER_SOURCES_LIST]
      })
      toast.success('Cập nhật thứ tự nguồn đơn hàng thành công')
    },
    onError: (error: any) => {
      const errorMessage = error?.response?.data?.message || 'Có lỗi xảy ra khi cập nhật thứ tự'
      toast.error(errorMessage)
    }
  })

  return {
    updateSort: mutate,
    isUpdating: isPending
  }
}

export const useDeleteOrderSource = () => {
  const queryClient = useQueryClient()
  const { company, brands } = useAuthStore(state => state.auth)
  const selectedBrand = brands?.[0]

  const { mutate, isPending } = useMutation({
    mutationFn: async (data: { id: string }) => {
      const companyUid = company?.id || ''
      const brandUid = selectedBrand?.id || ''

      if (!companyUid || !brandUid) {
        throw new Error('Thiếu thông tin công ty hoặc thương hiệu')
      }

      return await orderSourcesApi.deleteOrderSource({
        id: data.id,
        company_uid: companyUid,
        brand_uid: brandUid
      })
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.ORDER_SOURCES_LIST]
      })
      toast.success('Xóa nguồn đơn hàng thành công')
    },
    onError: (error: any) => {
      const errorMessage = error?.response?.data?.message || 'Có lỗi xảy ra khi xóa nguồn đơn hàng'
      toast.error(errorMessage)
    }
  })

  return {
    deleteOrderSource: mutate,
    isDeleting: isPending
  }
}

export const useOrderSourceById = (id: string | undefined) => {
  return useQuery<OrderSourceDetail>({
    queryKey: [QUERY_KEYS.ORDER_SOURCES_LIST, 'detail', id],
    queryFn: () => orderSourcesApi.getOrderSourceById(id!),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000 // 10 minutes
  })
}

export const useUpdateOrderSourceById = () => {
  const queryClient = useQueryClient()
  const { company, brands } = useAuthStore(state => state.auth)
  const selectedBrand = brands?.[0]

  const { mutate, isPending } = useMutation({
    mutationFn: async (data: CreateOrderSourceRequest) => {
      const companyUid = company?.id || ''
      const brandUid = selectedBrand?.id || ''

      if (!companyUid || !brandUid) {
        throw new Error('Thiếu thông tin công ty hoặc thương hiệu')
      }

      const updateData = {
        ...data,
        company_uid: companyUid,
        brand_uid: brandUid
      }

      return await orderSourcesApi.updateOrderSourceById(updateData)
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.ORDER_SOURCES_LIST]
      })
      toast.success('Cập nhật nguồn đơn hàng thành công')
    },
    onError: (error: any) => {
      const errorMessage =
        error?.response?.data?.message || 'Có lỗi xảy ra khi cập nhật nguồn đơn hàng'
      toast.error(errorMessage)
    }
  })

  return {
    updateOrderSource: mutate,
    isUpdating: isPending
  }
}
