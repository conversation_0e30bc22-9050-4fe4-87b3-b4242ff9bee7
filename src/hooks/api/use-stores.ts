import { useQuery } from '@tanstack/react-query'

import { convertApiStoreToStore, type Store } from '@/types/store'

import { useAuthStore } from '@/stores/authStore'

import { apiClient } from '@/lib/api'
import { getStores, type StoreListParams } from '@/lib/stores-api'

import { QUERY_KEYS } from '@/constants/query-keys'

export interface UseStoresDataOptions {
  params?: Partial<StoreListParams>
  enabled?: boolean
}

export const useStoresData = (options: UseStoresDataOptions = {}) => {
  const { params = {}, enabled = true } = options
  const { company, brands } = useAuthStore(state => state.auth)

  const selectedBrand = brands?.[0]

  const dynamicParams: StoreListParams = {
    company_uid: company?.id || '',
    brand_uid: selectedBrand?.id || '',
    page: 1,
    limit: 50
  }

  const finalParams = { ...dynamicParams, ...params }

  const hasRequiredAuth = !!(company?.id && selectedBrand?.id)

  return useQuery({
    queryKey: [QUERY_KEYS.STORES_LIST, finalParams],
    queryFn: async (): Promise<Store[]> => {
      const response = await getStores(finalParams)
      return response.data?.map(convertApiStoreToStore) || []
    },
    enabled: enabled && hasRequiredAuth,
    staleTime: 10 * 60 * 1000,
    refetchInterval: 5 * 60 * 1000
  })
}

export const useFilteredStoresData = (
  filters: {
    searchTerm?: string
    status?: string
    storeType?: string
  } = {}
) => {
  const storesQuery = useStoresData()

  const filteredData = storesQuery.data?.filter(store => {
    if (filters.searchTerm) {
      const searchLower = filters.searchTerm.toLowerCase()
      const matchesSearch =
        store.name.toLowerCase().includes(searchLower) ||
        store.address.toLowerCase().includes(searchLower) ||
        store.phone.toLowerCase().includes(searchLower)

      if (!matchesSearch) return false
    }

    if (filters.status && filters.status !== 'all') {
      if (filters.status === 'active' && !store.isActive) return false
      if (filters.status === 'inactive' && store.isActive) return false
    }

    if (filters.storeType && filters.storeType !== 'all') {
      // This would depend on the actual API response structure
      // For now, we'll skip this filter since the current API doesn't seem to have store_type
    }

    return true
  })

  return {
    ...storesQuery,
    data: filteredData || []
  }
}

export const useStoreData = (storeId: string, enabled: boolean = true) => {
  const { company, brands } = useAuthStore(state => state.auth)
  const selectedBrand = brands?.[0]

  return useQuery({
    queryKey: [QUERY_KEYS.STORES_DETAIL, storeId, company?.id, selectedBrand?.id],
    queryFn: async (): Promise<Store | undefined> => {
      if (!company?.id || !selectedBrand?.id || !storeId) {
        return undefined
      }

      const queryParams = new URLSearchParams({
        company_uid: company.id,
        brand_uid: selectedBrand.id,
        id: storeId
      })

      const response = await apiClient.get(`/mdata/v1/store?${queryParams.toString()}`)

      if (!response.data?.data) {
        return undefined
      }

      const storeData = response.data.data

      return {
        id: storeData.id,
        name: storeData.store_name,
        code: storeData.store_id,
        status: storeData.active === 1 ? 'active' : 'inactive',
        address: storeData.address,
        phone: storeData.phone,
        email: storeData.email || '',
        companyId: storeData.company_uid,
        brandId: storeData.brand_uid,
        cityId: storeData.city_uid,
        cityName: storeData.city?.city_name || '',
        isActive: storeData.active === 1,
        isFabi: storeData.is_fabi === 1,
        isAhamoveActive: storeData.is_ahamove_active === 1,
        latitude: storeData.latitude || 0,
        longitude: storeData.longitude || 0,
        deliveryServices: storeData.delivery_services || '',
        createdAt: new Date(storeData.created_at * 1000),
        updatedAt: new Date(storeData.updated_at * 1000),
        expiryDate: new Date(storeData.expiry_date * 1000)
      } as Store
    },
    enabled: enabled && !!storeId && !!company?.id && !!selectedBrand?.id,
    staleTime: 15 * 60 * 1000, // 15 minutes
    gcTime: 30 * 60 * 1000 // 30 minutes
  })
}

export const useStoresStats = () => {
  const storesQuery = useStoresData()

  const stats = {
    total: storesQuery.data?.length || 0,
    active: storesQuery.data?.filter(store => store.isActive).length || 0,
    inactive: storesQuery.data?.filter(store => !store.isActive).length || 0
  }

  return {
    ...storesQuery,
    stats
  }
}
