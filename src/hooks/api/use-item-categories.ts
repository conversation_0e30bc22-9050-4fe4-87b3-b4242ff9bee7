import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'

import { ItemCategory } from '@/types/item-categories'

import {
  itemCategoriesApi,
  type GetItemCategoriesParams,
  type CreateItemCategoryParams,
  type BulkCreateItemCategoryParams,
  type UpdateItemCategoryParams
} from '@/lib/item-categories-api'

import { QUERY_KEYS } from '@/constants/query-keys'

export interface UseItemCategoriesParams extends GetItemCategoriesParams {
  enabled?: boolean
}

export function useItemCategoriesData(params: UseItemCategoriesParams = {}) {
  const { enabled = true, ...apiParams } = params

  return useQuery({
    queryKey: [QUERY_KEYS.ITEM_CATEGORIES, apiParams],
    queryFn: () => itemCategoriesApi.getItemCategories(apiParams),
    enabled,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000 // 10 minutes
  })
}

export function useItemCategoryData(id: string, enabled: boolean = true) {
  return useQuery({
    queryKey: [QUERY_KEYS.ITEM_CATEGORIES, 'single', id],
    queryFn: () => itemCategoriesApi.getItemCategoryById(id),
    enabled: enabled && !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000 // 10 minutes
  })
}

export function useCreateItemCategory() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (params: CreateItemCategoryParams) => itemCategoriesApi.createItemCategory(params),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.ITEM_CATEGORIES]
      })
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.ITEM_TYPES]
      })
    }
  })
}

export function useBulkCreateItemCategories() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (categories: BulkCreateItemCategoryParams[]) =>
      itemCategoriesApi.bulkCreateItemCategories(categories),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.ITEM_CATEGORIES]
      })
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.ITEM_TYPES]
      })
    }
  })
}

export function useUpdateItemCategory() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({
      categoryId,
      params
    }: {
      categoryId: string
      params: UpdateItemCategoryParams
    }) => itemCategoriesApi.updateItemCategory(categoryId, params),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.ITEM_CATEGORIES]
      })
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.ITEM_TYPES]
      })
    }
  })
}

export function useUpdateItemCategoryStatus() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (category: ItemCategory) => itemCategoriesApi.updateItemCategoryStatus(category),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.ITEM_CATEGORIES]
      })
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.ITEM_TYPES]
      })
    }
  })
}

export function useDeleteItemCategory() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (categoryId: string) => itemCategoriesApi.deleteItemCategory(categoryId),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.ITEM_CATEGORIES]
      })
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.ITEM_TYPES]
      })
    }
  })
}

export function useImportItemCategories() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (file: File) => itemCategoriesApi.importCategories(file),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.ITEM_CATEGORIES]
      })
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.ITEM_TYPES]
      })
    }
  })
}

export function useExportItemCategories() {
  return useMutation({
    mutationFn: () => itemCategoriesApi.exportCategories()
  })
}
