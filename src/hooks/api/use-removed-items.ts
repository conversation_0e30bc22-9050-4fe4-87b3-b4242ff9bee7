import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'

import { removedItemsApi, type GetRemovedItemsParams } from '@/lib/item-removed-api'

import { QUERY_KEYS } from '@/constants/query-keys'

export interface UseRemovedItemsDataParams extends GetRemovedItemsParams {
  enabled?: boolean
}

export function useRemovedItemsData(params: UseRemovedItemsDataParams = {}) {
  const { enabled = true, ...apiParams } = params

  return useQuery({
    queryKey: [QUERY_KEYS.REMOVED_ITEMS, apiParams],
    queryFn: () => removedItemsApi.getRemovedItems(apiParams),
    enabled,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000 // 10 minutes
  })
}

export function useRestoreRemovedItem() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (itemId: string) => removedItemsApi.restoreRemovedItem(itemId),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.REMOVED_ITEMS]
      })
    }
  })
}

export function useBulkRestoreRemovedItems() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (itemUids: string[]) => removedItemsApi.bulkRestoreRemovedItems(itemUids),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.REMOVED_ITEMS]
      })
    }
  })
}

export function useExportRemovedItemsReport() {
  return useMutation({
    mutationFn: (cityUids: string[]) => removedItemsApi.exportRemovedItemsReport(cityUids)
  })
}

export function useExportRemovedItemsReportByStores() {
  return useMutation({
    mutationFn: (storeUids: string[]) => removedItemsApi.exportRemovedItemsReportByStores(storeUids)
  })
}

export function useCitiesData() {
  return useQuery({
    queryKey: [QUERY_KEYS.CITIES_LOCAL],
    queryFn: () => removedItemsApi.getCitiesFromLocalStorage(),
    staleTime: 30 * 60 * 1000, // 30 minutes
    gcTime: 60 * 60 * 1000 // 60 minutes
  })
}
