import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'

import type { Source } from '@/types/sources'

import { useAuthStore } from '@/stores/authStore'

import { sourcesApi, type GetSourcesParams } from '@/lib/sources-api'

import { QUERY_KEYS } from '@/constants/query-keys'

export interface UseSourcesDataParams extends Partial<GetSourcesParams> {
  enabled?: boolean
}

export function useSourcesData(params: UseSourcesDataParams = {}) {
  const { enabled = true, ...apiParams } = params
  const { company, brands } = useAuthStore(state => state.auth)

  const selectedBrand = brands?.[0]

  const finalParams: GetSourcesParams = {
    company_uid: company?.id || '',
    brand_uid: selectedBrand?.id || '',
    city_uid: '',
    skip_limit: true,
    ...apiParams
  }

  return useQuery({
    queryKey: [QUERY_KEYS.SOURCES, finalParams],
    queryFn: () => sourcesApi.getSources(finalParams),
    enabled: enabled && !!finalParams.company_uid && !!finalParams.brand_uid,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000 // 10 minutes
  })
}

/**
 * Hook to fetch sources for autocomplete (with is_fb=1 parameter)
 */
export function useSourcesForAutocomplete(enabled: boolean = true) {
  return useQuery({
    queryKey: [QUERY_KEYS.SOURCES, 'autocomplete', 'is_fb=1'],
    queryFn: () => sourcesApi.getSourcesForAutocomplete(),
    enabled,
    staleTime: 10 * 60 * 1000, // 10 minutes
    gcTime: 30 * 60 * 1000 // 30 minutes
  })
}

export function useUpdateSource() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (source: Source) => sourcesApi.updateSource(source),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.SOURCES]
      })
    }
  })
}

export function useDeleteSource() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (sourceId: string) => sourcesApi.deleteSource(sourceId),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.SOURCES]
      })
    }
  })
}
