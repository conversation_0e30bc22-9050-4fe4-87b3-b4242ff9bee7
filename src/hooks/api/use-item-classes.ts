import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'

import type { ItemClass } from '@/types/item-class'

import {
  itemClassesApi,
  type GetItemClassesParams,
  type CreateItemClassParams
} from '@/lib/item-classes-api'

import { QUERY_KEYS } from '@/constants/query-keys'

export interface UseItemClassesDataParams extends GetItemClassesParams {
  enabled?: boolean
}

export function useItemClassesData(params: UseItemClassesDataParams = {}) {
  const { enabled = true, ...apiParams } = params

  return useQuery({
    queryKey: [QUERY_KEYS.ITEM_CLASSES, apiParams],
    queryFn: () => itemClassesApi.getItemClasses(apiParams),
    enabled,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000 // 10 minutes
  })
}

export function useItemClassData(id: string, enabled: boolean = true) {
  return useQuery({
    queryKey: [QUERY_KEYS.ITEM_CLASSES, 'detail', id],
    queryFn: () => itemClassesApi.getItemClass(id),
    enabled: enabled && !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000 // 10 minutes
  })
}

export function useCreateItemClass() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (params: CreateItemClassParams) => itemClassesApi.createItemClass(params),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.ITEM_CLASSES]
      })
    }
  })
}

export function useUpdateItemClass() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (itemClass: ItemClass) => itemClassesApi.updateItemClass(itemClass),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.ITEM_CLASSES]
      })
    }
  })
}

export function useDeleteItemClass() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (itemClassId: string) => itemClassesApi.deleteItemClass(itemClassId),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.ITEM_CLASSES]
      })
    }
  })
}
