import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'

import type { Channel } from '@/types/channels'
import { convertApiChannelToChannel } from '@/types/channels'

import { useAuthStore } from '@/stores/authStore'

import { apiClient } from '@/lib/api'
import { channelsApi, type GetChannelsParams } from '@/lib/channels-api'

import { QUERY_KEYS } from '@/constants/query-keys'

export interface UseChannelsDataParams extends GetChannelsParams {
  enabled?: boolean
}

export function useChannelsData(params: UseChannelsDataParams = {}) {
  const { enabled = true, ...apiParams } = params

  return useQuery({
    queryKey: [QUERY_KEYS.CHANNELS, apiParams],
    queryFn: () => channelsApi.getChannels(apiParams),
    enabled,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000 // 10 minutes
  })
}

export function useChannelById(channelId?: string) {
  const { company, brands } = useAuthStore(state => state.auth)
  const selectedBrand = brands?.[0]

  return useQuery({
    queryKey: [QUERY_KEYS.CHANNELS, 'detail', channelId, company?.id, selectedBrand?.id],
    queryFn: async () => {
      if (!company?.id || !selectedBrand?.id || !channelId) {
        throw new Error('Missing required auth data or channelId')
      }

      // Build query parameters
      const queryParams = new URLSearchParams({
        company_uid: company.id,
        brand_uid: selectedBrand.id,
        id: channelId
      })

      const apiUrl = `/mdata/v1/channel?${queryParams.toString()}`
      const response = await apiClient.get<{ data: any; track_id?: string }>(apiUrl)

      if (response.data?.data) {
        // Convert API data to Channel object
        return convertApiChannelToChannel(response.data.data)
      }

      throw new Error('Channel not found')
    },
    enabled: !!(channelId && company?.id && selectedBrand?.id),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000 // 10 minutes
  })
}

export function useCreateChannel() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: channelsApi.createChannel,
    onSuccess: () => {
      // Invalidate and refetch channels data
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.CHANNELS]
      })
    }
  })
}

export function useUpdateChannel() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: channelsApi.updateChannel,
    onSuccess: () => {
      // Invalidate and refetch channels data
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.CHANNELS]
      })
    }
  })
}

export function useDeleteChannel() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: channelsApi.deleteChannel,
    onSuccess: () => {
      // Invalidate and refetch channels data
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.CHANNELS]
      })
    }
  })
}

interface UseCopyChannelsParams {
  onSuccess?: () => void
  onError?: (error: Error) => void
}

interface CopyChannelsMutationParams {
  sourceStoreId: string
  channelIds: string[]
  targetStoreIds: string[]
  channels: Channel[]
}

export function useCopyChannels(params?: UseCopyChannelsParams) {
  const queryClient = useQueryClient()
  const { company, brands } = useAuthStore(state => state.auth)
  const selectedBrand = brands?.[0]

  return useMutation({
    mutationFn: async ({ channelIds, targetStoreIds, channels }: CopyChannelsMutationParams) => {
      if (!company?.id || !selectedBrand?.id) {
        throw new Error('Missing authentication data')
      }

      // Filter channels by selected IDs
      const selectedChannels = channels.filter(channel => channelIds.includes(channel.id))

      if (selectedChannels.length === 0) {
        throw new Error('No channels selected')
      }

      if (targetStoreIds.length === 0) {
        throw new Error('No target stores selected')
      }

      // Call the API function to copy channels
      return channelsApi.copyChannels(
        {
          channels: selectedChannels,
          targetStoreIds
        },
        company.id,
        selectedBrand.id
      )
    },
    onSuccess: () => {
      // Invalidate channels query to refresh the data
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.CHANNELS]
      })

      // Call the onSuccess callback if provided
      if (params?.onSuccess) {
        params.onSuccess()
      }
    },
    onError: error => {
      // Call the onError callback if provided
      if (params?.onError && error instanceof Error) {
        params.onError(error)
      }
    }
  })
}
