import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'

import { useAuthStore } from '@/stores/authStore'

import {
  customizationsApi,
  type GetCustomizationsParams,
  type CopyCustomizationParams,
  type CreateCustomizationParams
} from '@/lib/customizations-api'

import { QUERY_KEYS } from '@/constants/query-keys'

export interface UseCustomizationsDataParams extends GetCustomizationsParams {
  enabled?: boolean
}

/**
 * Hook to fetch customizations data
 */
export function useCustomizationsData(params: UseCustomizationsDataParams = {}) {
  const { enabled = true, skip_limit, store_uid, ...apiParams } = params
  const { company, brands } = useAuthStore(state => state.auth)
  const selectedBrand = brands?.[0]

  const finalParams = {
    company_uid: company?.id || '',
    brand_uid: selectedBrand?.id || '',
    ...apiParams,
    ...(skip_limit ? { skip_limit } : {}),
    ...(store_uid ? { store_uid } : {})
  }

  return useQuery({
    queryKey: [QUERY_KEYS.CUSTOMIZATIONS, finalParams],
    queryFn: async () => {
      const response = await customizationsApi.getCustomizations(finalParams)
      return response || []
    },
    enabled: enabled && !!(company?.id && selectedBrand?.id),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000 // 10 minutes
  })
}

/**
 * Hook to copy a customization
 */
export function useCopyCustomization() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (params: CopyCustomizationParams) => customizationsApi.copyCustomization(params),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.CUSTOMIZATIONS]
      })
    }
  })
}

/**
 * Hook to create a new customization
 */
export function useCreateCustomization() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (params: CreateCustomizationParams) =>
      customizationsApi.createCustomization(params),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.CUSTOMIZATIONS]
      })
    }
  })
}

/**
 * Hook to delete a customization
 */
export function useDeleteCustomization() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (customizationId: string) => customizationsApi.deleteCustomization(customizationId),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.CUSTOMIZATIONS]
      })
    }
  })
}

/**
 * Hook to export customizations
 */
export function useExportCustomizations() {
  return useMutation({
    mutationFn: (params: GetCustomizationsParams) => customizationsApi.exportCustomizations(params)
  })
}

/**
 * Hook to bulk import customizations
 */
export function useBulkImportCustomizations() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({
      parsedData,
      storeUid
    }: {
      parsedData: import('@/types/customizations').ParsedCustomizationData[]
      storeUid?: string
    }) => customizationsApi.bulkImportCustomizations(parsedData, storeUid),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.CUSTOMIZATIONS]
      })
    }
  })
}
