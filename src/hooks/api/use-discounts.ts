import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'

import type { GetDiscountsParams } from '@/types/discounts'

import { getDiscounts, deleteDiscount, updateDiscount } from '@/lib/discounts-api'

export function useDiscounts(params: GetDiscountsParams = {}) {
  return useQuery({
    queryKey: ['discounts', params],
    queryFn: () => getDiscounts(params),
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 5 * 60 * 1000 // 5 minutes
  })
}

export function useDeleteDiscount() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: deleteDiscount,
    onSuccess: () => {
      // Invalidate and refetch discounts queries
      queryClient.invalidateQueries({ queryKey: ['discounts'] })
    }
  })
}

export function useUpdateDiscount() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: updateDiscount,
    onSuccess: () => {
      // Invalidate and refetch discounts queries
      queryClient.invalidateQueries({ queryKey: ['discounts'] })
    }
  })
}
