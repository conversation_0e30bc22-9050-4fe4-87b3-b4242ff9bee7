import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'

import { useAuthStore } from '@/stores/authStore'

import { rolesApi, type GetRolesParams } from '@/lib/roles-api'
import type { CreateRoleRequest, UpdateRoleRequest } from '@/types/role'

import { QUERY_KEYS } from '@/constants/query-keys'

export interface UseRolesDataParams extends GetRolesParams {
  enabled?: boolean
}

export function useRolesData(params: UseRolesDataParams = {}) {
  const { enabled = true, ...apiParams } = params
  const { company, brands } = useAuthStore(state => state.auth)

  const selectedBrand = brands?.[0]

  // Add company_uid and brand_uid from auth store if not provided
  const finalParams: GetRolesParams = {
    company_uid: company?.id,
    brand_uid: selectedBrand?.id,
    ...apiParams
  }

  return useQuery({
    queryKey: [QUERY_KEYS.ROLES, finalParams],
    queryFn: () => rolesApi.getRoles(finalParams),
    enabled,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000 // 10 minutes
  })
}

export function useRoleById(id: string, enabled = true) {
  return useQuery({
    queryKey: [QUERY_KEYS.ROLES_DETAIL, id],
    queryFn: () => rolesApi.getRoleById(id),
    enabled: enabled && !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000 // 10 minutes
  })
}

export function useCreateRole() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (role: CreateRoleRequest) => rolesApi.createRole(role),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.ROLES]
      })
    }
  })
}

export function useUpdateRole() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (role: UpdateRoleRequest) => rolesApi.updateRole(role),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.ROLES]
      })
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.ROLES_DETAIL]
      })
    }
  })
}

export function useDeleteRole() {
  const queryClient = useQueryClient()
  const { company, brands } = useAuthStore(state => state.auth)
  const selectedBrand = brands?.[0]

  return useMutation({
    mutationFn: (id: string) => {
      if (!company?.id || !selectedBrand?.id) {
        throw new Error('Company or brand information is missing')
      }
      return rolesApi.deleteRole({
        id,
        company_uid: company.id,
        brand_uid: selectedBrand.id
      })
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.ROLES]
      })
    }
  })
}

export function useBulkDeleteRoles() {
  const queryClient = useQueryClient()
  const { company, brands } = useAuthStore(state => state.auth)
  const selectedBrand = brands?.[0]

  return useMutation({
    mutationFn: (ids: string[]) => {
      if (!company?.id || !selectedBrand?.id) {
        throw new Error('Company or brand information is missing')
      }
      return rolesApi.bulkDeleteRoles({
        ids,
        company_uid: company.id,
        brand_uid: selectedBrand.id
      })
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.ROLES]
      })
    }
  })
}

export function useCopyRole() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ sourceRoleId, newRoleName }: { sourceRoleId: string; newRoleName: string }) =>
      rolesApi.copyRole(sourceRoleId, newRoleName),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.ROLES]
      })
    }
  })
}