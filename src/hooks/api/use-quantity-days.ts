import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { toast } from 'sonner'
import { useAuthStore } from '@/stores/authStore'
import {
  quantityDayApi,
  type GetQuantityDaysParams,
  type QuantityDayData,
  type CreateQuantityDayRequest,
  type UpdateQuantityDayRequest,
} from '@/lib/quantity-day-api'
import { QUERY_KEYS } from '@/constants/query-keys'

export interface UseQuantityDaysDataOptions {
  params?: Partial<GetQuantityDaysParams>
  enabled?: boolean
}

export const useQuantityDaysData = (
  options: UseQuantityDaysDataOptions = {}
) => {
  const { params = {}, enabled = true } = options
  const { company, brands } = useAuthStore((state) => state.auth)

  const selectedBrand = brands?.[0]

  const dynamicParams: GetQuantityDaysParams = {
    company_uid: company?.id || '',
    brand_uid: selectedBrand?.id || '',
    page: 1,
  }

  const finalParams = { ...dynamicParams, ...params }

  const hasRequiredAuth = !!(company?.id && selectedBrand?.id)

  return useQuery({
    queryKey: [QUERY_KEYS.QUANTITY_DAYS_LIST, finalParams],
    queryFn: async (): Promise<QuantityDayData[]> => {
      const response = await quantityDayApi.getQuantityDays(finalParams)
      return response.data || []
    },
    enabled: enabled && hasRequiredAuth,
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchInterval: 10 * 60 * 1000, // 10 minutes
  })
}

export const useQuantityDayData = (id: string, enabled: boolean = true) => {
  const quantityDaysQuery = useQuantityDaysData()

  return useQuery({
    queryKey: [QUERY_KEYS.QUANTITY_DAYS_DETAIL, id],
    queryFn: async (): Promise<QuantityDayData | undefined> => {
      // First try to find in cached data
      const cachedItem = quantityDaysQuery.data?.find((item) => item.id === id)
      if (cachedItem) {
        return cachedItem
      }

      // If not found in cache, fetch all and find the item
      const response = await quantityDayApi.getQuantityDays({
        company_uid: '', // Will be filled by the hook
        brand_uid: '', // Will be filled by the hook
      })

      return response.data?.find((item) => item.id === id)
    },
    enabled: enabled && !!id,
    staleTime: 15 * 60 * 1000, // 15 minutes
  })
}

export const useCreateQuantityDay = () => {
  const queryClient = useQueryClient()

  const { mutate, isPending } = useMutation({
    mutationFn: (data: CreateQuantityDayRequest) =>
      quantityDayApi.createQuantityDay(data),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.QUANTITY_DAYS_LIST],
      })
      toast.success('Tạo cấu hình số lượng thành công!')
    },
    onError: (error: Error) => {
      toast.error(`Lỗi khi tạo cấu hình: ${error.message}`)
    },
  })

  return { createQuantityDay: mutate, isCreating: isPending }
}

export const useUpdateQuantityDay = () => {
  const queryClient = useQueryClient()

  const { mutate, isPending } = useMutation({
    mutationFn: (data: UpdateQuantityDayRequest) =>
      quantityDayApi.updateQuantityDay(data),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.QUANTITY_DAYS_LIST],
      })
      toast.success('Cập nhật cấu hình số lượng thành công!')
    },
    onError: (error: Error) => {
      toast.error(`Lỗi khi cập nhật cấu hình: ${error.message}`)
    },
  })

  return { updateQuantityDay: mutate, isUpdating: isPending }
}

export const useDeleteQuantityDay = () => {
  const queryClient = useQueryClient()
  const { company, brands } = useAuthStore((state) => state.auth)
  const selectedBrand = brands?.[0]

  const { mutate, isPending } = useMutation({
    mutationFn: (params: string | string[]) => {
      if (Array.isArray(params)) {
        return quantityDayApi.deleteQuantityDay({
          list_id: params,
        })
      } else {
        if (!company?.id || !selectedBrand?.id) {
          throw new Error('Missing company or brand information')
        }
        return quantityDayApi.deleteQuantityDay({
          company_uid: company.id,
          brand_uid: selectedBrand.id,
          id: params,
        })
      }
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.QUANTITY_DAYS_LIST],
      })
      const count = Array.isArray(variables) ? variables.length : 1
      toast.success(`Xóa ${count} cấu hình số lượng thành công!`)
    },
    onError: (error: Error) => {
      toast.error(`Lỗi khi xóa cấu hình: ${error.message}`)
    },
  })

  return { deleteQuantityDay: mutate, isDeleting: isPending }
}

export const useQuantityDaysForTable = (
  options: UseQuantityDaysDataOptions = {}
) => {
  const quantityDaysQuery = useQuantityDaysData(options)

  const tableData =
    quantityDaysQuery.data?.map((item) => ({
      id: item.id,
      quantity: item.quantity_per_day,
      fromDate: new Date(item.from_date),
      toDate: new Date(item.to_date),
      time:
        item.time_sale_date_week === 0
          ? 'Tất cả các ngày trong tuần'
          : `Thứ ${item.time_sale_date_week}`,
      appliedItems: item.item_list.join(', '),
      storeUid: item.store_uid,
      storeName: '',
      isActive: !item.deleted,
      createdAt: new Date(item.created_at),
      updatedAt: new Date(item.updated_at),
      originalData: item,
    })) || []

  return {
    ...quantityDaysQuery,
    data: tableData,
  }
}
