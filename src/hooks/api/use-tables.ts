import { useQuery } from '@tanstack/react-query'

import type { GetTablesParams, Table } from '@/types'

import { useAuthStore } from '@/stores/authStore'

import { tablesApi } from '@/lib/table-api'

import { QUERY_KEYS } from '@/constants/query-keys'

export interface UseTablesDataParams extends GetTablesParams {
  enabled?: boolean
}

export function useTablesData(params: UseTablesDataParams = {}) {
  const { enabled = true, ...apiParams } = params
  const { company } = useAuthStore(state => state.auth)

  const finalParams: GetTablesParams = {
    company_uid: company?.id,
    ...apiParams
  }

  const hasRequiredAuth = !!(company?.id || finalParams.company_uid)

  return useQuery({
    queryKey: [QUERY_KEYS.TABLES_LIST, finalParams],
    queryFn: async () => {
      const response = await tablesApi.getTables(finalParams)
      return response.data || []
    },
    enabled:
      enabled &&
      hasRequiredAuth &&
      !!finalParams.brand_uid &&
      (!!finalParams.list_store_uid || !!finalParams.store_uid),
    staleTime: 5 * 60 * 1000,
    gcTime: 10 * 60 * 1000
  })
}

export type { Table }
