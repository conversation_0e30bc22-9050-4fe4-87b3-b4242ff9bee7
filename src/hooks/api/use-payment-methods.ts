import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'

import {
  convertApiPaymentMethodToPaymentMethod,
  type PaymentMethod,
  type PaymentMethodFilters
} from '@/types/payment-method'
import { toast } from 'sonner'

import { useAuthStore } from '@/stores/authStore'

import {
  paymentMethodsApi,
  type PaymentMethodsListParams,
  type CreatePaymentMethodRequest
} from '@/lib/payment-methods-api'

import { QUERY_KEYS } from '@/constants/query-keys'

export interface UsePaymentMethodsDataOptions {
  params?: Partial<PaymentMethodsListParams>
  enabled?: boolean
}

/**
 * Hook to fetch payment methods data
 */
export const usePaymentMethodsData = (options: UsePaymentMethodsDataOptions = {}) => {
  const { params = {}, enabled = true } = options
  const { company, brands } = useAuthStore(state => state.auth)
  const selectedBrand = brands?.[0]

  const dynamicParams: PaymentMethodsListParams = {
    company_uid: company?.id || '',
    brand_uid: selectedBrand?.id || '',
    page: 1
  }

  const finalParams = { ...dynamicParams, ...params }
  const hasRequiredAuth = !!(company?.id && selectedBrand?.id)

  return useQuery({
    queryKey: [QUERY_KEYS.PAYMENT_METHODS_LIST, finalParams],
    queryFn: async (): Promise<PaymentMethod[]> => {
      const response = await paymentMethodsApi.getPaymentMethods(finalParams)
      return response.data?.map(convertApiPaymentMethodToPaymentMethod) || []
    },
    enabled: enabled && hasRequiredAuth,
    staleTime: 10 * 60 * 1000, // 10 minutes
    refetchInterval: 5 * 60 * 1000 // 5 minutes
  })
}

/**
 * Hook to fetch filtered payment methods data
 */
export const useFilteredPaymentMethodsData = (filters: PaymentMethodFilters = {}) => {
  const paymentMethodsQuery = usePaymentMethodsData({
    params: {
      store_uid: filters.storeUid
    },
    enabled: !!filters.storeUid // Only fetch when storeUid is provided
  })

  const filteredData = paymentMethodsQuery.data?.filter(paymentMethod => {
    // Search filter
    if (filters.searchTerm) {
      const searchLower = filters.searchTerm.toLowerCase()
      const matchesSearch =
        paymentMethod.name.toLowerCase().includes(searchLower) ||
        paymentMethod.code.toLowerCase().includes(searchLower) ||
        paymentMethod.description.toLowerCase().includes(searchLower)

      if (!matchesSearch) return false
    }

    // Active status filter
    if (filters.isActive !== undefined) {
      if (paymentMethod.isActive !== filters.isActive) return false
    }

    return true
  })

  return {
    ...paymentMethodsQuery,
    data: filteredData || []
  }
}

/**
 * Hook to create a new payment method with image upload
 */
export const useCreatePaymentMethod = () => {
  const queryClient = useQueryClient()
  const { company, brands } = useAuthStore(state => state.auth)
  const selectedBrand = brands?.[0]

  const { mutate, isPending } = useMutation({
    mutationFn: async (data: {
      payment_method_name: string
      payment_method_id?: string
      payment_fee_extra: number
      payment_fee_type: number
      stores: string[]
      extra_data?: { require_traceno?: number }
      logoFile?: File | null
    }) => {
      const companyUid = company?.id || ''
      const brandUid = selectedBrand?.id || ''

      if (!companyUid || !brandUid) {
        throw new Error('Thiếu thông tin công ty hoặc thương hiệu')
      }

      let imageUrl = ''

      // Step 1: Upload image if provided
      if (data.logoFile) {
        const uploadResponse = await paymentMethodsApi.uploadImage(data.logoFile)
        imageUrl = uploadResponse.data.image_url
      }

      // Step 2: Create payment method
      const paymentMethodData: CreatePaymentMethodRequest = {
        payment_method_name: data.payment_method_name,
        payment_method_id: data.payment_method_id || `PAYMENT_METHOD-${Date.now()}`,
        payment_fee_extra: data.payment_fee_extra,
        payment_fee_type: data.payment_fee_type,
        payment_type: 0,
        stores: data.stores,
        config_keys: {},
        company_uid: companyUid,
        brand_uid: brandUid,
        background: imageUrl,
        image_path: imageUrl,
        extra_data: data.extra_data
      }

      return await paymentMethodsApi.createPaymentMethod(paymentMethodData)
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.PAYMENT_METHODS_LIST]
      })
      toast.success('Tạo phương thức thanh toán thành công')
    },
    onError: (error: any) => {
      const errorMessage =
        error?.response?.data?.message || 'Có lỗi xảy ra khi tạo phương thức thanh toán'
      toast.error(errorMessage)
    }
  })

  return { createPaymentMethod: mutate, isCreating: isPending }
}

/**
 * Hook to update an existing payment method
 */
export const useUpdatePaymentMethod = () => {
  const queryClient = useQueryClient()
  const { company, brands } = useAuthStore(state => state.auth)
  const selectedBrand = brands?.[0]

  const { mutate, isPending } = useMutation({
    mutationFn: async (data: {
      id: string
      payment_method_name: string
      payment_method_id?: string
      payment_fee_extra: number
      payment_fee_type: number
      stores: string[]
      extra_data?: { require_traceno?: number }
      logoFile?: File | null
    }) => {
      const companyUid = company?.id || ''
      const brandUid = selectedBrand?.id || ''

      if (!companyUid || !brandUid) {
        throw new Error('Thiếu thông tin công ty hoặc thương hiệu')
      }

      let imageUrl = ''

      // Step 1: Upload image if provided
      if (data.logoFile) {
        const uploadResponse = await paymentMethodsApi.uploadImage(data.logoFile)
        imageUrl = uploadResponse.data.image_url
      }

      // Step 2: Update payment method - match cURL structure
      const paymentMethodData: any = {
        id: data.id,
        payment_method_id: data.payment_method_id,
        payment_method_name: data.payment_method_name,
        payment_type: 0,
        sort: 20,
        is_fb: 0,
        extra_data: data.extra_data,
        active: 1,
        revision: 0,
        brand_uid: brandUid,
        company_uid: companyUid,
        is_fabi: 1,
        payment_fee_extra: data.payment_fee_extra,
        payment_fee_type: data.payment_fee_type,
        stores: data.stores,
        config_keys: {}
      }

      // Include image fields if new image was uploaded
      if (imageUrl) {
        paymentMethodData.background = imageUrl
        paymentMethodData.image_path = imageUrl
      }

      return await paymentMethodsApi.updatePaymentMethod(paymentMethodData)
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.PAYMENT_METHODS_LIST]
      })
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.PAYMENT_METHODS_DETAIL]
      })
      toast.success('Cập nhật phương thức thanh toán thành công')
    },
    onError: (error: any) => {
      const errorMessage =
        error?.response?.data?.message || 'Có lỗi xảy ra khi cập nhật phương thức thanh toán'
      toast.error(errorMessage)
    }
  })

  return { updatePaymentMethod: mutate, isUpdating: isPending }
}

/**
 * Hook to delete a payment method
 */
export const useDeletePaymentMethod = () => {
  const queryClient = useQueryClient()
  const { company, brands } = useAuthStore(state => state.auth)
  const selectedBrand = brands?.[0]

  const { mutate, isPending } = useMutation({
    mutationFn: async (id: string) => {
      const companyUid = company?.id || ''
      const brandUid = selectedBrand?.id || ''

      if (!companyUid || !brandUid) {
        throw new Error('Thiếu thông tin công ty hoặc thương hiệu')
      }

      await paymentMethodsApi.deletePaymentMethod({
        id,
        company_uid: companyUid,
        brand_uid: brandUid
      })
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.PAYMENT_METHODS_LIST]
      })
      toast.success('Xóa phương thức thanh toán thành công')
    },
    onError: (error: any) => {
      const errorMessage =
        error?.response?.data?.message || 'Có lỗi xảy ra khi xóa phương thức thanh toán'
      toast.error(errorMessage)
    }
  })

  return { deletePaymentMethod: mutate, isDeleting: isPending }
}

/**
 * Hook to fetch payment method detail
 */
export const usePaymentMethodDetail = (paymentMethodId: string) => {
  const { company, brands } = useAuthStore(state => state.auth)
  const selectedBrand = brands?.[0]

  return useQuery({
    queryKey: [QUERY_KEYS.PAYMENT_METHODS_DETAIL, paymentMethodId],
    queryFn: async () => {
      const companyUid = company?.id || ''
      const brandUid = selectedBrand?.id || ''

      if (!companyUid || !brandUid || !paymentMethodId) {
        throw new Error('Thiếu thông tin cần thiết')
      }

      return await paymentMethodsApi.getPaymentMethodDetail(paymentMethodId, companyUid, brandUid)
    },
    enabled: !!(company?.id && selectedBrand?.id && paymentMethodId),
    staleTime: 5 * 60 * 1000 // 5 minutes
  })
}
