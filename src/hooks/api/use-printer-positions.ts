import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'

import { useAuthStore } from '@/stores/authStore'

import {
  printerPositionsApi,
  categoryListHelpers,
  type PrinterPositionsApiParams,
  type PrinterPosition
} from '@/lib/printer-positions-api'

import { QUERY_KEYS } from '@/constants/query-keys'

export interface UsePrinterPositionsDataOptions {
  params?: Partial<PrinterPositionsApiParams>
  enabled?: boolean
}

export const usePrinterPositionsData = (options: UsePrinterPositionsDataOptions = {}) => {
  const { company, brands } = useAuthStore(state => state.auth)
  const selectedBrand = brands?.[0]

  return useQuery({
    queryKey: [
      QUERY_KEYS.PRINTER_POSITIONS,
      options.params?.company_uid || company?.id,
      options.params?.brand_uid || selectedBrand?.id
    ],
    queryFn: async () => {
      const companyUid = options.params?.company_uid || company?.id || ''
      const brandUid = options.params?.brand_uid || selectedBrand?.id || ''

      if (!companyUid || !brandUid) {
        throw new Error('Missing company_uid or brand_uid')
      }

      const params: PrinterPositionsApiParams = {
        company_uid: companyUid,
        brand_uid: brandUid,
        results_per_page: options.params?.results_per_page || 15000
      }

      return await printerPositionsApi.fetchPrinterPositions(params)
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000 // 10 minutes
  })
}

export interface UpdatePrinterPositionCategoryParams {
  printerPosition: PrinterPosition
  categoryId: string
  action: 'add' | 'remove'
}

export const useUpdatePrinterPositionCategory = () => {
  const queryClient = useQueryClient()
  const { company, brands } = useAuthStore(state => state.auth)
  const selectedBrand = brands?.[0]

  return useMutation({
    mutationFn: async ({
      printerPosition,
      categoryId,
      action
    }: UpdatePrinterPositionCategoryParams) => {
      let updatedListItemTypeId: string

      if (action === 'add') {
        updatedListItemTypeId = categoryListHelpers.addCategoryToList(
          printerPosition.list_item_type_id,
          categoryId
        )
      } else {
        updatedListItemTypeId = categoryListHelpers.removeCategoryFromList(
          printerPosition.list_item_type_id,
          categoryId
        )
      }

      const updatedPrinterPosition: PrinterPosition = {
        ...printerPosition,
        list_item_type_id: updatedListItemTypeId
      }

      return await printerPositionsApi.updatePrinterPosition(updatedPrinterPosition)
    },
    onSuccess: () => {
      // Invalidate printer positions cache
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.PRINTER_POSITIONS, company?.id, selectedBrand?.id]
      })
    }
  })
}
