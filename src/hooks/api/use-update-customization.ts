import { useMutation, useQueryClient } from '@tanstack/react-query'

import { CreateCustomizationParams } from '@/types/customizations'
import { ExistingCustomization } from '@/types/customizations'

import { updateCustomization } from '@/lib/customizations-api'

import { QUERY_KEYS } from '@/constants/query-keys'

interface UpdateCustomizationParams extends CreateCustomizationParams {
  customizationId: string
  existingCustomization?: ExistingCustomization
}

export function useUpdateCustomization() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async ({
      customizationId,
      existingCustomization,
      ...params
    }: UpdateCustomizationParams) => {
      return updateCustomization(customizationId, params, existingCustomization || {})
    },
    onSuccess: () => {
      // Invalidate and refetch customizations list
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.CUSTOMIZATIONS] })
      // Invalidate specific customization detail
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.CUSTOMIZATIONS_DETAIL] })
    },
    onError: error => {
      console.error('Update customization error:', error)
    }
  })
}
