import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { Device } from '@/types/device'
import { useAuthStore } from '@/stores/authStore'
import {
  fetchDevices,
  convertApiDeviceToDevice,
  copyDevice,
  devicesApi,
  type DevicesApiParams,
  type CopyDeviceParams,
} from '@/lib/devices-api'
import { QUERY_KEYS } from '@/constants/query-keys'

export interface UseDevicesDataOptions {
  params?: Partial<DevicesApiParams>
  enabled?: boolean
  storesData?: Array<{ id: string; store_name: string }>
  searchTerm?: string
  storeUid?: string
  deviceType?: string
}

export const useDevicesData = (options: UseDevicesDataOptions = {}) => {
  const {
    params = {},
    enabled = true,
    storesData,
    searchTerm,
    storeUid,
    deviceType,
  } = options
  const { company, brands, stores } = useAuthStore((state) => state.auth)

  const selectedBrand = brands?.[0]
  const selectedStore = stores?.[0]

  const dynamicParams: DevicesApiParams = {
    company_uid: company?.id || '',
    brand_uid: selectedBrand?.id || '',
    page: 1,
    list_store_uid: selectedStore?.id || '',
  }

  if (storeUid && storeUid !== 'all') {
    dynamicParams.list_store_uid = storeUid
  }

  if (searchTerm) {
    dynamicParams.search = searchTerm
  }

  if (deviceType && deviceType !== 'all') {
    dynamicParams.type = deviceType
  }

  const finalParams = { ...dynamicParams, ...params }

  const hasRequiredAuth = !!(company?.id && selectedBrand?.id)

  return useQuery({
    queryKey: [QUERY_KEYS.DEVICES_LIST, finalParams],
    queryFn: async (): Promise<Device[]> => {
      const response = await fetchDevices(finalParams)

      return response.data.map((apiDevice) => {
        const authStore = stores?.find((s) => s.id === apiDevice.store_uid)
        const apiStore = storesData?.find((s) => s.id === apiDevice.store_uid)
        const storeName =
          apiStore?.store_name ||
          authStore?.store_name ||
          `Store ${apiDevice.store_uid.slice(0, 8)}`
        return convertApiDeviceToDevice(apiDevice, storeName)
      })
    },
    enabled: enabled && hasRequiredAuth,
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchInterval: 30 * 1000, // Refetch every 30 seconds
  })
}

export interface CreateDeviceData {
  deviceName: string
  storeId: string
  deviceType: string
  brandUid: string
}

export function useCreateDevice() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (data: CreateDeviceData) => {
      const response = await devicesApi.createDevice({
        name: data.deviceName,
        store_id: data.storeId,
        device_type: data.deviceType,
        brand_uid: data.brandUid,
      })
      return response
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.DEVICES_LIST] })
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.DEVICES] })
    },
    onError: (error) => {
      throw error
    },
  })
}

export const useCopyDevice = () => {
  const queryClient = useQueryClient()
  const { company, brands } = useAuthStore((state) => state.auth)
  const selectedBrand = brands?.[0]

  return useMutation({
    mutationFn: async ({
      deviceId,
      newDeviceName,
    }: {
      deviceId: string
      newDeviceName: string
    }) => {
      if (!company?.id || !selectedBrand?.id) {
        throw new Error('Missing authentication data')
      }

      const params: CopyDeviceParams = {
        company_uid: company.id,
        brand_uid: selectedBrand.id,
        id: deviceId,
        newDeviceName,
      }

      return copyDevice(params)
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.DEVICES_LIST],
      })
    },
    onError: (error) => {
      throw error
    },
  })
}
