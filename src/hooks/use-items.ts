/* eslint-disable @typescript-eslint/no-explicit-any */
import { useMemo } from 'react'
import { useQuery } from '@tanstack/react-query'
import { usePosStores, useCurrentCompany } from '@/stores/posStore'
import { itemsApi } from '@/lib/items-api'

interface UseItemsOptions {
  dateRange?: {
    from: Date
    to: Date
  }
  selectedStores?: string[]
  filterType?: 'monthly' | 'daily'
  autoFetch?: boolean
}

interface ProcessedChartData {
  dailyData: Array<Record<string, any>>
  itemData: Array<{
    itemId: string
    itemName: string
    itemClass: string
    itemType: string
    quantitySold: number
    revenue: number
    revenueNet: number
    percentage: number
    unit: string
  }>
  totalQuantity: number
  totalRevenue: number
  totalRevenueNet: number
}

interface UseItemsReturn {
  // Raw items data
  items: any[]
  processedItems: Array<{
    itemId: string
    itemName: string
    itemClass: string
    itemType: string
    quantitySold: number
    revenue: number
    revenueNet: number
    percentage: number
    unit: string
  }>

  // Processed chart data based on filterType
  chartData: ProcessedChartData | null

  // Loading and error states
  isLoading: boolean
  error: string | null
  loadingMessage?: string

  // Summary data
  totalQuantity: number
  totalRevenue: number
  totalRevenueNet: number
  itemCount: number

  refetch: () => void

  selectedBrand: any
  currentBrandStores: any[]
}

/**
 * Query keys for items-related queries
 */
export const itemsKeys = {
  all: ['items'] as const,
  lists: () => [...itemsKeys.all, 'list'] as const,
  list: (filters: {
    companyUid?: string
    brandUid?: string
    startDate?: number
    endDate?: number
    storeUids?: string[]
    byDays?: number
    orderBy?: string
  }) => [...itemsKeys.lists(), filters] as const,
}

/**
 * Custom hook for items data using TanStack Query
 * This hook provides better caching and data fetching capabilities
 * compared to the previous useState/useEffect approach
 */
export function useItems(options: UseItemsOptions = {}): UseItemsReturn {
  const {
    dateRange,
    selectedStores = ['all-stores'],
    autoFetch = true,
  } = options

  const { selectedBrand, currentBrandStores } = usePosStores()
  const { company } = useCurrentCompany()

  const startTime = useMemo(() => dateRange?.from?.getTime(), [dateRange?.from])
  const endTime = useMemo(() => dateRange?.to?.getTime(), [dateRange?.to])
  const brandId = selectedBrand?.id
  const companyId = company?.id

  const storeUids = useMemo(() => {
    if (
      selectedStores &&
      selectedStores.length > 0 &&
      !selectedStores.includes('all-stores')
    ) {
      return selectedStores.filter(
        (id) => id !== 'all-stores' && id !== 'no-stores'
      )
    } else {
      const activeStores = currentBrandStores.filter(
        (store) => store.active === 1
      )
      return activeStores.length > 0
        ? activeStores.map((store) => store.id)
        : undefined
    }
  }, [selectedStores, currentBrandStores])

  const {
    data: rawData,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: itemsKeys.list({
      companyUid: companyId,
      brandUid: brandId,
      startDate: startTime,
      endDate: endTime,
      storeUids,
      byDays: 0,
      orderBy: 'quantity_sold',
    }),
    queryFn: async () => {
      if (!brandId || !companyId) {
        throw new Error('Brand or company not selected')
      }

      if (!startTime || !endTime) {
        throw new Error('Date range is required')
      }

      const daysDiff = Math.ceil((endTime - startTime) / (1000 * 60 * 60 * 24))
      const storeCount =
        storeUids?.length ||
        currentBrandStores.filter((s) => s.active === 1).length ||
        1

      let limit = 1000
      if (daysDiff > 90 || storeCount > 10) {
        limit = 500
      }
      if (daysDiff > 180 || storeCount > 20) {
        limit = 200
      }

      return await itemsApi.getItemsSummary({
        companyUid: companyId,
        brandUid: brandId,
        startDate: startTime,
        endDate: endTime,
        storeUids,
        byDays: 0,
        limit,
      })
    },
    enabled: autoFetch && !!brandId && !!companyId && !!startTime && !!endTime,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes (garbage collection time)
    retry: (failureCount, error) => {
      if (error?.message?.includes('401') || error?.message?.includes('403')) {
        return false
      }
      return failureCount < 3
    },
  })

  const summary = useMemo(() => {
    if (!rawData) return null
    return itemsApi.processItemsSummary(rawData)
  }, [rawData])

  // Memoized computed values
  const processedItems = useMemo(() => {
    return summary?.itemData || []
  }, [summary?.itemData])

  const items = useMemo(() => {
    return rawData?.data?.list_data_item_return || []
  }, [rawData?.data?.list_data_item_return])

  const totalQuantity = useMemo(() => {
    return summary?.totalQuantity || 0
  }, [summary?.totalQuantity])

  const totalRevenue = useMemo(() => {
    return summary?.totalRevenue || 0
  }, [summary?.totalRevenue])

  const totalRevenueNet = useMemo(() => {
    return summary?.totalRevenueNet || 0
  }, [summary?.totalRevenueNet])

  const itemCount = useMemo(() => {
    return summary?.itemCount || 0
  }, [summary?.itemCount])

  const chartData = useMemo(() => {
    return {
      dailyData: [],
      itemData: processedItems || [],
      totalQuantity,
      totalRevenue,
      totalRevenueNet,
    }
  }, [processedItems, totalQuantity, totalRevenue, totalRevenueNet])

  return {
    items,
    processedItems,

    chartData,

    isLoading,
    error: error?.message || null,

    totalQuantity,
    totalRevenue,
    totalRevenueNet,
    itemCount,

    refetch: () => {
      refetch()
    },

    selectedBrand,
    currentBrandStores,
  }
}

/**
 * Convenience hook to get items for current date range
 * Similar to how usePosStores provides currentBrandStores
 */
export function useCurrentBrandItems(dateRange?: { from: Date; to: Date }) {
  return useItems({
    dateRange,
    autoFetch: true,
  })
}

/**
 * Hook to get items with specific filters
 * Provides more control over the data fetching
 */
export function useItemsWithFilters(
  dateRange: { from: Date; to: Date },
  selectedStores: string[],
  filterType: 'monthly' | 'daily' = 'daily'
) {
  return useItems({
    dateRange,
    selectedStores,
    filterType,
    autoFetch: true,
  })
}

export default useItems
