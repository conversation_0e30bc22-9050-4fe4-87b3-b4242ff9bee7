import { useMemo } from 'react'
import { RevenueResponse } from '@/lib/revenue-api'
import { useRevenueData } from '@/hooks/use-revenue-data'

interface TopStoreData {
  storeId: string
  storeName: string
  totalSales: number
  totalBills: number
  totalRevenue: number
}

interface UseTopStoresOptions {
  dateRange?: {
    from: Date
    to: Date
  }
  limit?: number
  sortBy?: 'bills' | 'revenue' | 'sales'
  order?: 'asc' | 'desc' // asc for least orders, desc for most orders
  filterType?: 'monthly' | 'daily'
  selectedStores?: string[]
}

interface UseTopStoresReturn {
  topStores: TopStoreData[]
  isLoading: boolean
  error: string | null
  refetch: () => Promise<void>
  rawData: RevenueResponse | null // Add rawData for chart access
}

export function useTopStores(
  options: UseTopStoresOptions = {}
): UseTopStoresReturn {
  const {
    dateRange,
    limit = 5,
    sortBy = 'sales',
    order = 'asc',
    filterType = 'daily',
    selectedStores = [], // Default to all stores
  } = options

  // Convert dateRange to customDateRange format - simple getTime()
  const customDateRange = useMemo(() => {
    if (!dateRange?.from || !dateRange?.to) {
      return undefined
    }

    return {
      startDate: dateRange.from.getTime(),
      endDate: dateRange.to.getTime(),
    }
  }, [dateRange?.from, dateRange?.to])

  // Use existing revenue data hook instead of fetching separately
  const { rawData, isLoading, error, refetch } = useRevenueData({
    customDateRange,
    storeIds: selectedStores,
    filterType,
    autoFetch: true,
  })

  // Calculate top stores from revenue data (no additional API calls)
  const topStores = useMemo(() => {
    if (!rawData?.data || rawData.data.length === 0) {
      return []
    }

    // Transform revenue data to our format
    const transformedStores = rawData.data.map((store) => {
      // Calculate revenue based on filterType and dateRange
      let calculatedRevenue = store.revenue_gross
      let calculatedSales = store.total_sales
      let calculatedBills = store.peo_count

      // If we have list_data, calculate based on filterType
      if (store.list_data && store.list_data.length > 0) {
        if (filterType === 'monthly') {
          // For monthly: Group by month and sum each month's data
          const monthlyData = new Map<
            string,
            { revenue: number; sales: number; bills: number }
          >()

          store.list_data.forEach((dayData) => {
            const date = new Date(dayData.tran_date)
            const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`

            if (!monthlyData.has(monthKey)) {
              monthlyData.set(monthKey, { revenue: 0, sales: 0, bills: 0 })
            }

            const monthData = monthlyData.get(monthKey)!
            monthData.revenue += dayData.revenue_gross
            monthData.sales += dayData.total_sales
            monthData.bills += dayData.peo_count
          })

          // Sum all months in the selected range
          const fromMonth = `${dateRange?.from?.getFullYear()}-${String((dateRange?.from?.getMonth() || 0) + 1).padStart(2, '0')}`
          const toMonth = `${dateRange?.to?.getFullYear()}-${String((dateRange?.to?.getMonth() || 0) + 1).padStart(2, '0')}`

          calculatedRevenue = 0
          calculatedSales = 0
          calculatedBills = 0

          for (const [monthKey, monthData] of monthlyData) {
            if (monthKey >= fromMonth && monthKey <= toMonth) {
              calculatedRevenue += monthData.revenue
              calculatedSales += monthData.sales
              calculatedBills += monthData.bills
            }
          }

          // Monthly calculation completed
        } else {
          // For daily: Filter by exact date range
          const startOfDay = new Date(dateRange?.from || new Date())
          startOfDay.setHours(0, 0, 0, 0)
          const endOfDay = new Date(dateRange?.to || new Date())
          endOfDay.setHours(23, 59, 59, 999)

          const startTime = startOfDay.getTime()
          const endTime = endOfDay.getTime()

          const filteredData = store.list_data.filter((dayData) => {
            const dayTime = dayData.tran_date
            return dayTime >= startTime && dayTime <= endTime
          })

          calculatedRevenue = filteredData.reduce(
            (sum, day) => sum + day.revenue_gross,
            0
          )
          calculatedSales = filteredData.reduce(
            (sum, day) => sum + day.total_sales,
            0
          )
          calculatedBills = filteredData.reduce(
            (sum, day) => sum + day.peo_count,
            0
          )
        }
      }

      return {
        storeId: store.store_uid,
        storeName: store.store_name,
        totalSales: calculatedSales,
        totalBills: calculatedBills,
        totalRevenue: calculatedRevenue,
      }
    })

    // Sort stores based on criteria
    const sortedStores = [...transformedStores].sort((a, b) => {
      let valueA: number
      let valueB: number

      switch (sortBy) {
        case 'bills':
          valueA = a.totalBills
          valueB = b.totalBills
          break
        case 'revenue':
          valueA = a.totalRevenue
          valueB = b.totalRevenue
          break
        case 'sales':
        default:
          valueA = a.totalSales
          valueB = b.totalSales
          break
      }

      return order === 'asc' ? valueA - valueB : valueB - valueA
    })

    // Take top N stores
    return sortedStores.slice(0, limit)
  }, [
    rawData?.data,
    sortBy,
    order,
    limit,
    dateRange?.from,
    dateRange?.to,
    filterType,
  ])

  // Memoize the return value to prevent unnecessary re-renders
  const returnValue = useMemo(
    () => ({
      topStores,
      isLoading,
      error,
      refetch,
      rawData,
    }),
    [topStores, isLoading, error, refetch, rawData]
  )

  return returnValue
}

// Convenience hook for top 5 stores with least sales
export function useTopStoresLeastSales(
  dateRange?: { from: Date; to: Date },
  filterType?: 'monthly' | 'daily'
) {
  return useTopStores({
    dateRange,
    limit: 5,
    sortBy: 'sales',
    order: 'asc', // Ascending = least sales first
    filterType,
  })
}

// Convenience hook for top 5 stores with most sales
export function useTopStoresMostSales(
  dateRange?: { from: Date; to: Date },
  filterType?: 'monthly' | 'daily'
) {
  return useTopStores({
    dateRange,
    limit: 5,
    sortBy: 'sales',
    order: 'desc', // Descending = most sales first
    filterType,
  })
}

// Convenience hook for top 5 stores with least orders
export function useTopStoresLeastOrders(
  dateRange?: { from: Date; to: Date },
  filterType?: 'monthly' | 'daily'
) {
  return useTopStores({
    dateRange,
    limit: 5,
    sortBy: 'bills',
    order: 'asc', // Ascending = least orders first
    filterType,
  })
}

// Convenience hook for top 5 stores with most orders
export function useTopStoresMostOrders(
  dateRange?: { from: Date; to: Date },
  filterType?: 'monthly' | 'daily'
) {
  return useTopStores({
    dateRange,
    limit: 5,
    sortBy: 'bills',
    order: 'desc', // Descending = most orders first
    filterType,
  })
}

// Convenience hook for top 5 stores with least revenue
export function useTopStoresLeastRevenue(
  dateRange?: { from: Date; to: Date },
  filterType?: 'monthly' | 'daily'
) {
  return useTopStores({
    dateRange,
    limit: 5,
    sortBy: 'revenue',
    order: 'asc', // Ascending = least revenue first
    filterType,
  })
}

// Convenience hook for top 5 stores with most revenue
export function useTopStoresMostRevenue(
  dateRange?: { from: Date; to: Date },
  filterType?: 'monthly' | 'daily'
) {
  return useTopStores({
    dateRange,
    limit: 5,
    sortBy: 'revenue',
    order: 'desc', // Descending = most revenue first
    filterType,
  })
}

export default useTopStores
