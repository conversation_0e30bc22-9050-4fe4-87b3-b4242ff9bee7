import { useQuery } from '@tanstack/react-query'
import { useCurrentBrand, useCurrentCompany } from '@/stores/posStore'
import { getStores, type ApiStore } from '@/lib/stores-api'

interface UseAllStoresParams {
  enabled?: boolean
}

const delay = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms))

async function fetchAllStores(
  companyUid: string,
  brandUid: string
): Promise<ApiStore[]> {
  const allStores: ApiStore[] = []
  let currentPage = 1
  let hasMoreData = true
  const limit = 50
  let consecutiveEmptyPages = 0
  const maxEmptyPages = 3
  const maxPages = 20

  while (
    hasMoreData &&
    consecutiveEmptyPages < maxEmptyPages &&
    currentPage <= maxPages
  ) {
    try {
      if (currentPage > 1) {
        await delay(100)
      }

      const response = await getStores({
        company_uid: companyUid,
        brand_uid: brandUid,
        page: currentPage,
        limit: limit,
      })

      const pageData = response.data || []

      if (pageData.length === 0) {
        consecutiveEmptyPages++

        if (consecutiveEmptyPages >= maxEmptyPages) {
          hasMoreData = false
        } else {
          currentPage++
        }
      } else {
        consecutiveEmptyPages = 0
        allStores.push(...pageData)
        currentPage++
      }
    } catch (_error) {
      currentPage++
      consecutiveEmptyPages++

      if (consecutiveEmptyPages >= maxEmptyPages) {
        hasMoreData = false
      }
    }
  }

  return allStores
}

export function useAllStores({ enabled = true }: UseAllStoresParams = {}) {
  const { selectedBrand } = useCurrentBrand()
  const { company } = useCurrentCompany()

  const fallbackCompanyUid = '269717a1-7bb6-4fa3-9150-dea2f709c081'
  const fallbackBrandUid = '8b8f15f9-6986-4c5a-86bc-f7dba8966659'

  const companyUid = company?.id || fallbackCompanyUid
  const brandUid = selectedBrand?.id || fallbackBrandUid

  const query = useQuery({
    queryKey: ['stores-all', companyUid, brandUid],
    queryFn: () => fetchAllStores(companyUid, brandUid),
    enabled: enabled && !!companyUid && !!brandUid,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  })

  const allStores = query.data || []

  return {
    isLoading: query.isLoading,
    error: query.error,
    refetch: query.refetch,

    stores: allStores,
    allStores,
    totalItems: allStores.length,
  }
}
