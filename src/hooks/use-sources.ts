/* eslint-disable @typescript-eslint/no-explicit-any */
import { useMemo, useEffect } from 'react'

import { useQuery, useQueryClient } from '@tanstack/react-query'

import { usePosStores, useCurrentCompany } from '@/stores/posStore'

import { sourcesApi } from '@/lib/sale-sources-api'

interface UseSourcesOptions {
  dateRange?: {
    from: Date
    to: Date
  }
  selectedStores?: string[]
  selectedSources?: string[]
  filterType?: 'monthly' | 'daily'
  autoFetch?: boolean
}

interface ProcessedChartData {
  dailyData: Array<Record<string, any>>
  sourceData: Array<{
    sourceId: string
    sourceName: string
    revenue: number
    bills: number
    percentage: number
  }>
  totalRevenue: number
  totalBills: number
}

interface UseSourcesReturn {
  // Raw sources data
  sources: any[]
  processedSources: Array<{
    sourceId: string
    sourceName: string
    revenue: number
    bills: number
    percentage: number
  }>

  // Processed chart data based on filterType
  chartData: ProcessedChartData | null

  // Loading and error states
  isLoading: boolean
  error: string | null

  // Summary data
  totalRevenue: number
  totalBills: number
  sourceCount: number

  refetch: () => void

  selectedBrand: any
  currentBrandStores: any[]
}

/**
 * Query keys for sources-related queries
 */
export const sourcesKeys = {
  all: ['sources'] as const,
  lists: () => [...sourcesKeys.all, 'list'] as const,
  list: (filters: {
    companyUid?: string
    brandUid?: string
    startDate?: number
    endDate?: number
    storeUids?: string[]
    byDays?: number
    filterType?: string
  }) => [...sourcesKeys.lists(), filters] as const
}

/**
 * Custom hook for sources data using TanStack Query
 * This hook provides better caching and data fetching capabilities
 * compared to the previous useState/useEffect approach
 */
export function useSources(options: UseSourcesOptions = {}): UseSourcesReturn {
  const {
    dateRange,
    selectedStores = ['all-stores'],
    selectedSources = ['all-sources'],
    filterType = 'daily',
    autoFetch = true
  } = options

  const { selectedBrand, currentBrandStores } = usePosStores()
  const { company } = useCurrentCompany()
  const queryClient = useQueryClient()

  const startTime = useMemo(() => dateRange?.from?.getTime(), [dateRange?.from])
  const endTime = useMemo(() => dateRange?.to?.getTime(), [dateRange?.to])
  const brandId = selectedBrand?.id
  const companyId = company?.id

  const storeUids = useMemo(() => {
    if (selectedStores && selectedStores.length > 0 && !selectedStores.includes('all-stores')) {
      return selectedStores.filter(id => id !== 'all-stores' && id !== 'no-stores')
    } else {
      const activeStores = currentBrandStores.filter(store => store.active === 1)
      return activeStores.length > 0 ? activeStores.map(store => store.id) : undefined
    }
  }, [selectedStores, currentBrandStores])

  // Listen for brand changes and invalidate queries
  useEffect(() => {
    const handleBrandChange = () => {
      // Invalidate all sources queries when brand changes
      queryClient.invalidateQueries({
        queryKey: sourcesKeys.all
      })
    }

    window.addEventListener('brandChanged', handleBrandChange)
    return () => {
      window.removeEventListener('brandChanged', handleBrandChange)
    }
  }, [queryClient])

  const {
    data: rawData,
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: sourcesKeys.list({
      companyUid: companyId,
      brandUid: brandId,
      startDate: startTime,
      endDate: endTime,
      storeUids,
      byDays: filterType === 'daily' ? 1 : 0,
      filterType // Include filterType in query key for proper invalidation
    }),
    queryFn: async () => {
      if (!brandId || !companyId) {
        throw new Error('Brand or company not selected')
      }

      if (!startTime || !endTime) {
        throw new Error('Date range is required')
      }

      return await sourcesApi.getSourcesSummary({
        companyUid: companyId,
        brandUid: brandId,
        startDate: startTime,
        endDate: endTime,
        storeUids,
        byDays: filterType === 'daily' ? 1 : 0,
        limit: 100
      })
    },
    enabled: autoFetch && !!brandId && !!companyId && !!startTime && !!endTime,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes (garbage collection time)
    retry: (failureCount: number, error: any) => {
      if (error?.message?.includes('401') || error?.message?.includes('403')) {
        return false
      }
      return failureCount < 3
    }
  })

  // Process the raw data into summary format
  const summary = useMemo(() => {
    if (!rawData?.data) return null
    return sourcesApi.processSourcesSummary(rawData.data)
  }, [rawData?.data])

  // Memoized computed values
  const processedSources = useMemo(() => {
    return summary?.sourceData || []
  }, [summary?.sourceData])

  const sources = useMemo(() => {
    return rawData?.data || []
  }, [rawData?.data])

  const totalRevenue = useMemo(() => {
    return summary?.totalRevenue || 0
  }, [summary?.totalRevenue])

  const totalBills = useMemo(() => {
    return summary?.totalBills || 0
  }, [summary?.totalBills])

  const sourceCount = useMemo(() => {
    return summary?.sourceCount || 0
  }, [summary?.sourceCount])

  const chartData = useMemo(() => {
    if (!sources || sources.length === 0) {
      return {
        dailyData: [],
        sourceData: processedSources || [],
        totalRevenue: 0,
        totalBills: 0
      }
    }

    const filteredSources =
      selectedSources[0] === 'all-sources'
        ? sources
        : sources.filter(source => source.source_id === selectedSources[0])

    let filteredTotalRevenue = 0
    let filteredTotalBills = 0

    filteredSources.forEach(source => {
      filteredTotalRevenue += source.revenue_gross || 0
      filteredTotalBills += source.total_bill || 0
    })

    const dateSourceMap = new Map<string, Record<string, any>>()

    const allDates = new Set<string>()
    if (dateRange) {
      const currentDate = new Date(dateRange.from)
      const endDate = new Date(dateRange.to)

      while (currentDate <= endDate) {
        const dateStr = currentDate.toISOString().split('T')[0]
        allDates.add(dateStr)
        currentDate.setDate(currentDate.getDate() + 1)
      }
    }

    allDates.forEach(date => {
      dateSourceMap.set(date, { date })
      filteredSources.forEach(source => {
        const sourceName = source.source_name
        dateSourceMap.get(date)![sourceName] = 0
      })
    })

    filteredSources.forEach(source => {
      const sourceName = source.source_name
      if (source.list_data && source.list_data.length > 0) {
        source.list_data.forEach((day: any) => {
          const dateData = dateSourceMap.get(day.date)
          if (dateData) {
            dateData[sourceName] = (dateData[sourceName] || 0) + day.revenue_gross
          }
        })
      }
    })

    const dailyData = Array.from(dateSourceMap.values()).sort((a, b) =>
      a.date.localeCompare(b.date)
    )

    // Filter sourceData to match selected sources
    const filteredSourceData =
      selectedSources[0] === 'all-sources'
        ? processedSources || []
        : (processedSources || []).filter(source => selectedSources.includes(source.sourceId))

    if (filterType === 'monthly') {
      const monthlyDataMap = new Map<string, Record<string, any>>()

      // Filter dailyData to only include dates within the selected date range
      const filteredDailyData = dateRange
        ? dailyData.filter(dayData => {
            const dayDate = new Date(dayData.date)
            return dayDate >= dateRange.from && dayDate <= dateRange.to
          })
        : dailyData

      filteredDailyData.forEach(dayData => {
        const dateParts = dayData.date.split('-')
        const monthKey = `${dateParts[0]}-${dateParts[1]}`

        const existing = monthlyDataMap.get(monthKey)
        if (existing) {
          filteredSources.forEach(source => {
            const sourceName = source.source_name
            if (dayData[sourceName]) {
              existing[sourceName] = (existing[sourceName] || 0) + dayData[sourceName]
            }
          })
        } else {
          const monthData: Record<string, any> = { date: monthKey }
          filteredSources.forEach(source => {
            const sourceName = source.source_name
            monthData[sourceName] = dayData[sourceName] || 0
          })
          monthlyDataMap.set(monthKey, monthData)
        }
      })

      const monthlyData = Array.from(monthlyDataMap.values()).sort((a, b) =>
        a.date.localeCompare(b.date)
      )

      return {
        dailyData: monthlyData,
        sourceData: filteredSourceData,
        totalRevenue: filteredTotalRevenue,
        totalBills: filteredTotalBills
      }
    } else {
      return {
        dailyData,
        sourceData: filteredSourceData,
        totalRevenue: filteredTotalRevenue,
        totalBills: filteredTotalBills
      }
    }
  }, [sources, processedSources, filterType, selectedSources, dateRange])

  return {
    sources,
    processedSources,

    chartData,

    isLoading,
    error: error?.message || null,

    totalRevenue,
    totalBills,
    sourceCount,

    refetch: () => {
      refetch()
    },

    selectedBrand,
    currentBrandStores
  }
}

/**
 * Convenience hook to get sources for current date range
 * Similar to how usePosStores provides currentBrandStores
 */
export function useCurrentBrandSources(dateRange?: { from: Date; to: Date }) {
  return useSources({
    dateRange,
    autoFetch: true
  })
}

/**
 * Hook to get sources with specific filters
 * Provides more control over the data fetching
 */
export function useSourcesWithFilters(
  dateRange: { from: Date; to: Date },
  selectedStores: string[],
  filterType: 'monthly' | 'daily' = 'daily'
) {
  return useSources({
    dateRange,
    selectedStores,
    filterType,
    autoFetch: true
  })
}

export default useSources
