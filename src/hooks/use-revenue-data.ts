import { useState, useEffect, useMemo, useCallback } from 'react'
import {
  useCurrentBrand,
  useCurrentCompany,
  usePosStores,
} from '@/stores/posStore'
import {
  revenueApi,
  type RevenueSummary,
  type RevenueResponse,
} from '@/lib/revenue-api'

interface UseRevenueDataOptions {
  period?: string
  customDateRange?: {
    startDate: number
    endDate: number
  }
  storeIds?: string[]
  filterType?: 'monthly' | 'daily'
  autoFetch?: boolean
}

interface UseRevenueDataReturn {
  summary: RevenueSummary | null
  rawData: RevenueResponse | null
  isLoading: boolean
  error: string | null
  refetch: () => Promise<void>
}

export function useRevenueData(
  options: UseRevenueDataOptions = {}
): UseRevenueDataReturn {
  const {
    period = 'this-month',
    customDateRange,
    storeIds,
    filterType = 'daily',
    autoFetch = true,
  } = options

  const [summary, setSummary] = useState<RevenueSummary | null>(null)
  const [rawData, setRawData] = useState<RevenueResponse | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const { selectedBrand } = useCurrentBrand()
  const { company } = useCurrentCompany()
  const { currentBrandStores } = usePosStores()

  // Memoize active stores to prevent unnecessary re-renders
  const activeStores = useMemo(() => {
    return currentBrandStores.filter((store) => store.active === 1)
  }, [currentBrandStores])

  // Memoize the date range to prevent unnecessary re-renders
  const memoizedDateRange = useMemo(() => {
    return customDateRange
      ? {
          startDate: customDateRange.startDate,
          endDate: customDateRange.endDate,
        }
      : null
  }, [customDateRange?.startDate, customDateRange?.endDate])

  // Memoize store IDs to prevent unnecessary re-renders
  const memoizedStoreIds = useMemo(() => {
    if (!storeIds || storeIds.length === 0) return null
    return [...storeIds].sort() // Sort to ensure consistent ordering
  }, [storeIds ? storeIds.sort().join(',') : null])

  const fetchRevenue = useCallback(async () => {
    if (!selectedBrand || !company) {
      setError('Brand or company not selected')
      return
    }

    setIsLoading(true)
    setError(null)

    try {
      // Get date range - use memoized custom range if provided, otherwise use period
      const { startDate, endDate } = memoizedDateRange
        ? memoizedDateRange
        : revenueApi.getDateRange(period)

      // Determine which stores to include
      let storeUids: string[] | undefined
      if (memoizedStoreIds && memoizedStoreIds.length > 0) {
        // Filter out "all-stores" and "no-stores" options
        storeUids = memoizedStoreIds.filter(
          (id) => id !== 'all-stores' && id !== 'no-stores'
        )
        if (storeUids.length === 0) {
          storeUids = undefined // This will include all stores
        }
      } else {
        // Use all active stores from current brand (memoized)
        storeUids =
          activeStores.length > 0
            ? activeStores.map((store) => store.id)
            : undefined
      }

      // // Fetch revenue data
      // console.log('🔄 Fetching revenue with params:', {
      //   companyUid: company.id,
      //   brandUid: selectedBrand.id,
      //   startDate,
      //   endDate,
      //   storeUids,
      //   byDays: 1,
      //   limit: 1000,
      // })

      const response = await revenueApi.getRevenueSummary({
        companyUid: company.id,
        brandUid: selectedBrand.id,
        startDate,
        endDate,
        storeUids,
        byDays: filterType === 'daily' ? 1 : 0, // 1 for daily breakdown, 0 for monthly
        limit: 1000,
      })

      // console.log('📊 API Response:', response)

      // Process the data
      const processedSummary = revenueApi.processRevenueSummary(response.data)
      // console.log('📈 Processed Summary:', processedSummary)

      setRawData(response)
      setSummary(processedSummary)
    } catch (err) {
      // console.error('❌ Revenue fetch error:', err)
      const errorMessage =
        err instanceof Error ? err.message : 'Failed to fetch revenue data'
      setError(errorMessage)
    } finally {
      setIsLoading(false)
    }
  }, [
    selectedBrand?.id,
    company?.id,
    memoizedDateRange?.startDate,
    memoizedDateRange?.endDate,
    period,
    memoizedStoreIds?.join(','),
    activeStores.length,
    filterType,
  ])

  // Auto-fetch when dependencies change
  useEffect(() => {
    if (autoFetch && selectedBrand && company) {
      fetchRevenue()
    }
  }, [
    autoFetch,
    selectedBrand?.id,
    company?.id,
    memoizedDateRange?.startDate,
    memoizedDateRange?.endDate,
    period,
    memoizedStoreIds?.join(','),
    filterType,
    // Removed fetchRevenue to prevent infinite loop
  ])

  return {
    summary,
    rawData,
    isLoading,
    error,
    refetch: fetchRevenue,
  }
}
