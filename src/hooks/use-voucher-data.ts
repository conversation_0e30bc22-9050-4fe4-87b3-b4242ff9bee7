import { useMemo } from 'react'
import { useQueries } from '@tanstack/react-query'
import { usePosStores, useCurrentCompany } from '@/stores/posStore'
import {
  promotionsApi,
  type VoucherSummary,
  type PromotionData,
} from '@/lib/promotions-api'
import { revenueApi } from '@/lib/revenue-api'

interface UseVoucherDataOptions {
  dateRange?: {
    from: Date
    to: Date
  }
  selectedStores?: string[]
  autoFetch?: boolean
}

interface UseVoucherDataReturn {
  data: VoucherSummary[]
  allPromotions: PromotionData[]
  totalPrice: number
  totalAmount: number
  totalTransactions: number
  isLoading: boolean
  error: string | null
  refetch: () => void
}

/**
 * Query keys for voucher-related queries
 */
export const voucherKeys = {
  all: ['voucher'] as const,
  lists: () => [...voucherKeys.all, 'list'] as const,
  list: (filters: {
    companyUid?: string
    brandUid?: string
    startDate?: number
    endDate?: number
    storeUid?: string
  }) => [...voucherKeys.lists(), filters] as const,
}

/**
 * Hook để fetch dữ liệu voucher từ từng cửa hàng và filter theo voucher_code
 */
export function useVoucherData({
  dateRange,
  selectedStores = ['all-stores'],
  autoFetch = true,
}: UseVoucherDataOptions): UseVoucherDataReturn {
  const { selectedBrand, currentBrandApiStores } = usePosStores()
  const { company } = useCurrentCompany()

  const fallbackCompanyUid = '269717a1-7bb6-4fa3-9150-dea2f709c081'
  const fallbackBrandUid = '8b8f15f9-6986-4c5a-86bc-f7dba8966659'

  const companyId = company?.id || fallbackCompanyUid
  const brandId = selectedBrand?.id || fallbackBrandUid

  const stableBrandId = useMemo(() => brandId, [brandId])
  const stableCompanyId = useMemo(() => companyId, [companyId])

  // Calculate date range
  const { startTime, endTime } = useMemo(() => {
    if (!dateRange?.from || !dateRange?.to) {
      const now = new Date()
      const sevenDaysAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
      return {
        startTime: sevenDaysAgo.getTime(),
        endTime: now.getTime(),
      }
    }

    return {
      startTime: dateRange.from.getTime(),
      endTime: dateRange.to.getTime(),
    }
  }, [dateRange])

  // Determine which stores to fetch
  const storesToFetch = useMemo(() => {
    if (!currentBrandApiStores || currentBrandApiStores.length === 0) {
      return []
    }

    if (selectedStores.includes('all-stores')) {
      return currentBrandApiStores
    }

    return currentBrandApiStores.filter((store) =>
      selectedStores.includes(store.id)
    )
  }, [currentBrandApiStores, selectedStores])

  // Step 1: Fetch promotions data to identify stores with member vouchers
  const promotionQueries = useQueries({
    queries: (storesToFetch || []).map((store) => ({
      queryKey: [
        'member-promotions',
        stableCompanyId,
        stableBrandId,
        store.id,
        startTime,
        endTime,
      ],
      queryFn: async () => {
        if (!stableBrandId || !stableCompanyId || !startTime || !endTime) {
          throw new Error('Missing required parameters')
        }

        // Fetch member voucher promotions for the store
        const memberPromotions = await promotionsApi.getMemberVoucherPromotions(
          {
            companyUid: stableCompanyId,
            brandUid: stableBrandId,
            startDate: startTime,
            endDate: endTime,
            storeUid: store.id,
          }
        )

        // Calculate totals from member promotions
        const totals = memberPromotions.reduce(
          (acc, promotion) => {
            acc.totalBills += promotion.total_bill
            acc.revenueGross += promotion.revenue_gross
            acc.discountAmount += promotion.discount_amount
            return acc
          },
          { totalBills: 0, revenueGross: 0, discountAmount: 0 }
        )

        return {
          storeId: store.id,
          storeName: store.store_name || `Store ${store.id}`,
          promotions: memberPromotions,
          hasVouchers: memberPromotions.length > 0,
          totals,
        }
      },
      enabled:
        autoFetch &&
        !!stableBrandId &&
        !!stableCompanyId &&
        !!startTime &&
        !!endTime,
      staleTime: 5 * 60 * 1000,
      gcTime: 10 * 60 * 1000,
      retry: 2,
    })),
  })

  // Step 2: Get stores that have member vouchers
  // Extract data and status arrays to avoid referential instability
  const promotionData = promotionQueries.map((q) => q.data)
  const promotionStatus = promotionQueries.map((q) => q.status)

  const storesWithVouchers = useMemo(() => {
    return promotionData
      .filter(
        (data, index) =>
          data?.hasVouchers && promotionStatus[index] === 'success'
      )
      .filter((data): data is NonNullable<typeof data> => Boolean(data))
  }, [promotionData, promotionStatus])

  // Step 3: Fetch total revenue for stores with vouchers to calculate correct percentage
  const storeRevenueQueries = useQueries({
    queries: storesWithVouchers.map((storeData) => ({
      queryKey: [
        'store-revenue',
        stableCompanyId,
        stableBrandId,
        storeData.storeId,
        startTime,
        endTime,
      ],
      queryFn: async () => {
        if (!stableBrandId || !stableCompanyId || !startTime || !endTime) {
          throw new Error('Missing required parameters')
        }

        // Fetch total revenue for this store
        const response = await revenueApi.getRevenueSummary({
          companyUid: stableCompanyId,
          brandUid: stableBrandId,
          startDate: startTime,
          endDate: endTime,
          storeUids: [storeData.storeId],
          byDays: 0, // Get aggregated data
          limit: 1000,
        })

        const storeRevenue = response.data[0]
        return {
          storeId: storeData.storeId,
          totalRevenueGross: storeRevenue?.revenue_gross || 0,
        }
      },
      enabled:
        autoFetch &&
        !!stableBrandId &&
        !!stableCompanyId &&
        !!startTime &&
        !!endTime,
      staleTime: 5 * 60 * 1000,
      gcTime: 10 * 60 * 1000,
      retry: 2,
    })),
  })

  // Extract revenue data and status arrays to avoid referential instability
  const revenueData = storeRevenueQueries.map((q) => q.data)
  const revenueStatus = storeRevenueQueries.map((q) => q.status)

  // Process and combine data from all stores
  const processedData = useMemo(() => {
    const allPromotions: PromotionData[] = []
    const voucherSummaries: VoucherSummary[] = []
    let totalPrice = 0
    let totalAmount = 0
    let totalTransactions = 0

    // Process promotions data with correct revenue calculation
    storesWithVouchers.forEach((storeData, index) => {
      const { totals, promotions } = storeData

      // Get corresponding revenue data for this store
      const storeRevenue = revenueData[index]
      const isRevenueLoaded = revenueStatus[index] === 'success' && storeRevenue

      if (totals.revenueGross > 0 && isRevenueLoaded) {
        // Add all promotions to the collection
        allPromotions.push(...promotions)

        // Calculate correct voucher percentage: (voucher discount_amount / total store revenue_gross) * 100
        const totalStoreRevenue = storeRevenue.totalRevenueGross
        const voucherPercentage =
          totalStoreRevenue > 0
            ? (totals.discountAmount / totalStoreRevenue) * 100
            : 0

        voucherSummaries.push({
          storeUid: storeData.storeId,
          storeName: storeData.storeName,
          totalTransactions: totals.totalBills,
          totalPrice: totalStoreRevenue, // Use total store revenue as total price
          totalAmount: totalStoreRevenue - totals.discountAmount, // Net amount after voucher discount
          revenueGross: totalStoreRevenue, // Total store revenue
          discountAmount: totals.discountAmount, // Voucher discount amount
          voucherPercentage, // Correct percentage calculation
          promotionData: promotions[0], // Store first promotion for reference
        })

        totalPrice += totalStoreRevenue
        totalAmount += totalStoreRevenue - totals.discountAmount
        totalTransactions += totals.totalBills
      }
    })

    return {
      data: voucherSummaries,
      allPromotions,
      totalPrice,
      totalAmount,
      totalTransactions,
    }
  }, [storesWithVouchers, revenueData, revenueStatus])

  // Check loading and error states
  const isLoading =
    promotionQueries.some((query) => query.isLoading) ||
    storeRevenueQueries.some((query) => query.isLoading)

  const error =
    promotionQueries.find((query) => query.error)?.error?.message ||
    storeRevenueQueries.find((query) => query.error)?.error?.message ||
    null

  // Refetch function
  const refetch = () => {
    promotionQueries.forEach((query) => query.refetch())
    storeRevenueQueries.forEach((query) => query.refetch())
  }

  return {
    ...processedData,
    isLoading,
    error,
    refetch,
  }
}

export default useVoucherData
