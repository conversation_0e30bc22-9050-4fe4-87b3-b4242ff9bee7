import { useMemo, useEffect, useState } from 'react'
import { useQueries } from '@tanstack/react-query'
import { usePosStores, useCurrentCompany } from '@/stores/posStore'
import { revenueApi } from '@/lib/revenue-api'
import { salesApi } from '@/lib/sales-api'
import { getStoredApiStores } from '@/lib/stores-api'

interface SalesVoucherSummary {
  storeUid: string
  storeName: string
  totalTransactions: number
  totalPrice: number // amount_origin total
  totalAmount: number // net amount after discount
  revenueGross: number // total store revenue
  discountAmount: number
  voucherPercentage: number // (amount_origin / revenue_gross) * 100
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  voucherSales: any[]
}

interface UseSalesVoucherDataOptions {
  dateRange?: {
    from: Date
    to: Date
  }
  selectedStores?: string[]
  voucherCodes?: string[]
  autoFetch?: boolean
}

interface UseSalesVoucherDataReturn {
  data: SalesVoucherSummary[]
  totalPrice: number
  totalAmount: number
  totalTransactions: number
  isLoading: boolean
  error: string | null
  refetch: () => void
}

// Default voucher codes for FB100SK, FBGTVM, FBUPXL5
export const DEFAULT_VOUCHER_CODES = ['FB100SK', 'FBGTVM', 'FBUPXL5']

// Generate cache key with date range
const generateCacheKey = (startTime: number, endTime: number) => {
  return `sales-voucher-data-${startTime}-${endTime}`
}

// Cache data structure
interface CachedVoucherData {
  data: SalesVoucherSummary[]
  totalPrice: number
  totalAmount: number
  totalTransactions: number
  timestamp: number
}

// Save data to localStorage
const saveToCache = (key: string, data: CachedVoucherData) => {
  try {
    localStorage.setItem(key, JSON.stringify(data))
  } catch (_error) {
    // Ignore localStorage errors
  }
}

// Load data from localStorage
const loadFromCache = (key: string): CachedVoucherData | null => {
  try {
    const cached = localStorage.getItem(key)
    if (cached) {
      const parsed = JSON.parse(cached) as CachedVoucherData
      // Check if cache is not too old (24 hours)
      const maxAge = 24 * 60 * 60 * 1000 // 24 hours
      if (Date.now() - parsed.timestamp < maxAge) {
        return parsed
      }
    }
  } catch (_error) {
    // Ignore localStorage errors
  }
  return null
}

/**
 * Hook to fetch sales voucher data using sales-api with voucher code filtering
 * Implements stale-while-revalidate pattern with localStorage persistence
 */
export function useSalesVoucherData({
  dateRange,
  selectedStores = ['all-stores'],
  voucherCodes = DEFAULT_VOUCHER_CODES,
  autoFetch = true,
}: UseSalesVoucherDataOptions): UseSalesVoucherDataReturn {
  const { selectedBrand, currentBrandApiStores } = usePosStores()
  const { company } = useCurrentCompany()

  const fallbackCompanyUid = '269717a1-7bb6-4fa3-9150-dea2f709c081'
  const fallbackBrandUid = '5ed8968a-e4ed-4a04-870d-b53b7758fdc7'

  const companyId = company?.id || fallbackCompanyUid
  const brandId = selectedBrand?.id || fallbackBrandUid

  const stableBrandId = useMemo(() => brandId, [brandId])
  const stableCompanyId = useMemo(() => companyId, [companyId])

  const [cachedData, setCachedData] = useState<CachedVoucherData | null>(null)
  const [isLoadingFromCache, setIsLoadingFromCache] = useState(true)

  const { startTime, endTime } = useMemo(() => {
    if (!dateRange?.from || !dateRange?.to) {
      return {
        startTime: null,
        endTime: null,
      }
    }

    const startOfDay = new Date(dateRange.from)
    startOfDay.setHours(0, 0, 0, 0)

    const endOfDay = new Date(dateRange.to)
    endOfDay.setHours(23, 59, 59, 999)

    return {
      startTime: startOfDay.getTime(),
      endTime: endOfDay.getTime(),
    }
  }, [dateRange])

  const storeNameMap = useMemo(() => {
    const map = new Map<string, string>()
    currentBrandApiStores?.forEach((store) => {
      map.set(store.id, store.store_name)
    })
    return map
  }, [currentBrandApiStores])

  const storesToFetch = useMemo(() => {
    if (!currentBrandApiStores || currentBrandApiStores.length === 0) {
      return []
    }

    if (selectedStores.includes('all-stores')) {
      return currentBrandApiStores.map((store) => store.id)
    }

    return selectedStores.filter((storeId) =>
      currentBrandApiStores.some((store) => store.id === storeId)
    )
  }, [currentBrandApiStores, selectedStores])

  // Generate cache key with date range
  const cacheKey = useMemo(() => {
    if (!startTime || !endTime) {
      return null
    }
    const key = generateCacheKey(startTime, endTime)
    return key
  }, [startTime, endTime])

  // Load from cache when cache key changes
  useEffect(() => {
    if (cacheKey) {
      const cached = loadFromCache(cacheKey)
      if (cached) {
        setCachedData(cached)
      }
    }
    setIsLoadingFromCache(false)
  }, [cacheKey])

  // Step 1: Fetch voucher sales data for each store
  const voucherQueries = useQueries({
    queries: storesToFetch.map((storeUid) => ({
      queryKey: [
        'sales-voucher-data',
        stableCompanyId,
        stableBrandId,
        storeUid,
        startTime,
        endTime,
        voucherCodes,
      ],
      queryFn: async () => {
        if (!stableBrandId || !stableCompanyId || !startTime || !endTime) {
          throw new Error('Missing required parameters')
        }

        // Get voucher summary for this store
        const voucherSummary = await salesApi.getVoucherSummary({
          companyUid: stableCompanyId,
          brandUid: stableBrandId,
          listStoreUid: storeUid,
          startDate: startTime,
          endDate: endTime,
          voucherCodes,
          sourceId: 10000172,
        })

        // Get store name using multiple fallback methods
        const storeNameFromMap = storeNameMap.get(storeUid)
        const storeInfo = currentBrandApiStores?.find(
          (store) => store.id === storeUid
        )

        // Fallback: get from localStorage
        const storedStores = getStoredApiStores()
        const storedStoreInfo = storedStores.find(
          (store) => store.id === storeUid
        )

        const storeName =
          storeNameFromMap ||
          storeInfo?.store_name ||
          storedStoreInfo?.store_name ||
          voucherSummary.storeName ||
          `Store ${storeUid}`

        return {
          storeId: storeUid,
          storeName,
          voucherSummary,
          hasVouchers: voucherSummary.totalAmountOrigin > 0,
        }
      },
      enabled:
        autoFetch &&
        !!stableBrandId &&
        !!stableCompanyId &&
        !!startTime &&
        !!endTime &&
        voucherCodes.length > 0,
      staleTime: 5 * 60 * 1000, // 5 minutes
      gcTime: 10 * 60 * 1000, // 10 minutes
      refetchInterval: 15 * 60 * 1000, // Auto-refetch every 15 minutes
      refetchIntervalInBackground: true, // Continue refetching in background
      retry: 2,
    })),
  })

  // Step 2: Get stores that have vouchers
  const voucherData = voucherQueries.map((q) => q.data)
  const voucherStatus = voucherQueries.map((q) => q.status)

  const storesWithVouchers = useMemo(() => {
    return voucherData
      .filter(
        (data, index) => data?.hasVouchers && voucherStatus[index] === 'success'
      )
      .filter((data): data is NonNullable<typeof data> => Boolean(data))
  }, [voucherData, voucherStatus])

  // Step 3: Fetch total revenue for stores with vouchers to calculate correct percentage
  const storeRevenueQueries = useQueries({
    queries: storesWithVouchers.map((storeData) => ({
      queryKey: [
        'store-revenue',
        stableCompanyId,
        stableBrandId,
        storeData.storeId,
        startTime,
        endTime,
      ],
      queryFn: async () => {
        if (!stableBrandId || !stableCompanyId || !startTime || !endTime) {
          throw new Error('Missing required parameters')
        }

        // Fetch total revenue for this store
        const response = await revenueApi.getRevenueSummary({
          companyUid: stableCompanyId,
          brandUid: stableBrandId,
          startDate: startTime,
          endDate: endTime,
          storeUids: [storeData.storeId],
          byDays: 0, // Get aggregated data
          limit: 1000,
        })

        const storeRevenue = response.data[0]
        return {
          storeId: storeData.storeId,
          totalRevenueGross: storeRevenue?.revenue_gross || 0,
        }
      },
      enabled:
        autoFetch &&
        !!stableBrandId &&
        !!stableCompanyId &&
        !!startTime &&
        !!endTime,
      staleTime: 5 * 60 * 1000, // 5 minutes
      gcTime: 10 * 60 * 1000, // 10 minutes
      refetchInterval: 15 * 60 * 1000, // Auto-refetch every 15 minutes
      refetchIntervalInBackground: true, // Continue refetching in background
      retry: 2,
    })),
  })

  // Extract revenue data and status arrays
  const revenueData = storeRevenueQueries.map((q) => q.data)
  const revenueStatus = storeRevenueQueries.map((q) => q.status)

  // Process and combine data from all stores
  const processedData = useMemo(() => {
    const voucherSummaries: SalesVoucherSummary[] = []
    let totalPrice = 0
    let totalAmount = 0
    let totalTransactions = 0

    // Process voucher data with correct revenue calculation
    storesWithVouchers.forEach((storeData, index) => {
      const { voucherSummary } = storeData

      // Get corresponding revenue data for this store
      const storeRevenue = revenueData[index]
      const isRevenueLoaded = revenueStatus[index] === 'success' && storeRevenue

      if (voucherSummary.totalAmountOrigin > 0 && isRevenueLoaded) {
        // Calculate correct voucher percentage: (voucher amount_origin / total store revenue_gross) * 100
        const totalStoreRevenue = storeRevenue.totalRevenueGross
        const voucherPercentage =
          totalStoreRevenue > 0
            ? (voucherSummary.totalAmountOrigin / totalStoreRevenue) * 100
            : 0

        voucherSummaries.push({
          storeUid: storeData.storeId,
          storeName: storeData.storeName,
          totalTransactions: voucherSummary.transactionCount,
          totalPrice: voucherSummary.totalAmountOrigin, // Use amount_origin as totalPrice
          totalAmount: voucherSummary.totalNetAmount, // Net amount after discount
          revenueGross: totalStoreRevenue, // Total store revenue
          discountAmount: voucherSummary.totalDiscountAmount, // Voucher discount amount
          voucherPercentage, // Correct percentage calculation
          voucherSales: voucherSummary.voucherSales, // Store voucher sales for reference
        })

        totalPrice += voucherSummary.totalAmountOrigin
        totalAmount += voucherSummary.totalNetAmount
        totalTransactions += voucherSummary.transactionCount
      }
    })

    const result = {
      data: voucherSummaries,
      totalPrice,
      totalAmount,
      totalTransactions,
    }

    // Save to cache when data is successfully processed
    if (cacheKey && voucherSummaries.length > 0) {
      const cacheData: CachedVoucherData = {
        ...result,
        timestamp: Date.now(),
      }
      saveToCache(cacheKey, cacheData)
    }

    return result
  }, [storesWithVouchers, revenueData, revenueStatus, cacheKey])

  // Check loading and error states
  const isApiLoading =
    voucherQueries.some((query) => query.isLoading) ||
    storeRevenueQueries.some((query) => query.isLoading)

  const error =
    voucherQueries.find((query) => query.error)?.error?.message ||
    storeRevenueQueries.find((query) => query.error)?.error?.message ||
    null

  // Refetch function
  const refetch = () => {
    voucherQueries.forEach((query) => query.refetch())
    storeRevenueQueries.forEach((query) => query.refetch())
  }

  const hasFreshData = processedData.data.length > 0

  const finalData = hasFreshData ? processedData : cachedData || processedData

  // Always show loading when date range changes or API is loading
  const isLoading = isLoadingFromCache || isApiLoading

  return {
    ...finalData,
    isLoading,
    error,
    refetch,
  }
}

export default useSalesVoucherData
