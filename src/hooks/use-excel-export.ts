/* eslint-disable @typescript-eslint/no-explicit-any */
import { useState } from 'react'
import { exportToExcel, exportMultipleSheetsToExcel } from '@/lib/excel-export'

interface UseExcelExportOptions<T> {
  data: T[]
  filename?: string
  sheetName?: string
  columnMapping?: Record<keyof T, string>
  onExportStart?: () => void
  onExportComplete?: () => void
  onExportError?: (error: Error) => void
}

export function useExcelExport<T extends Record<string, any>>(
  options: UseExcelExportOptions<T>
) {
  const [isExporting, setIsExporting] = useState(false)

  const exportData = async () => {
    const {
      data,
      filename,
      sheetName,
      columnMapping,
      onExportStart,
      onExportComplete,
      onExportError,
    } = options

    if (data.length === 0) {
      alert('Không có dữ liệu để xuất')
      return
    }

    try {
      setIsExporting(true)
      onExportStart?.()

      // Add a small delay to show loading state
      await new Promise((resolve) => setTimeout(resolve, 100))

      exportToExcel(data, {
        filename,
        sheetName,
        columnMapping,
      })

      onExportComplete?.()
    } catch (error) {
      onExportError?.(error as Error)
      alert('Có lỗi xảy ra khi xuất file Excel')
    } finally {
      setIsExporting(false)
    }
  }

  return {
    exportData,
    isExporting,
  }
}

// Hook for exporting multiple sheets
interface UseMultiSheetExportOptions {
  sheets: Array<{
    data: Record<string, any>[]
    sheetName: string
    columnMapping?: Record<string, string>
  }>
  filename?: string
  onExportStart?: () => void
  onExportComplete?: () => void
  onExportError?: (error: Error) => void
}

export function useMultiSheetExcelExport(options: UseMultiSheetExportOptions) {
  const [isExporting, setIsExporting] = useState(false)

  const exportData = async () => {
    const { sheets, filename, onExportStart, onExportComplete, onExportError } =
      options

    if (
      sheets.length === 0 ||
      sheets.every((sheet) => sheet.data.length === 0)
    ) {
      alert('Không có dữ liệu để xuất')
      return
    }

    try {
      setIsExporting(true)
      onExportStart?.()

      // Add a small delay to show loading state
      await new Promise((resolve) => setTimeout(resolve, 100))

      exportMultipleSheetsToExcel(sheets, filename)

      onExportComplete?.()
    } catch (error) {
      onExportError?.(error as Error)
      alert('Có lỗi xảy ra khi xuất file Excel')
    } finally {
      setIsExporting(false)
    }
  }

  return {
    exportData,
    isExporting,
  }
}

// Utility function to generate filename with date range
export function generateFilenameWithDateRange(
  baseName: string,
  dateRange?: { from: Date; to: Date },
  extension: string = 'xlsx'
): string {
  const today = new Date().toISOString().split('T')[0]

  if (dateRange?.from && dateRange?.to) {
    const fromDate = dateRange.from.toISOString().split('T')[0]
    const toDate = dateRange.to.toISOString().split('T')[0]
    return `${baseName}-${fromDate}-${toDate}.${extension}`
  }

  return `${baseName}-${today}.${extension}`
}

// Utility function to format data for Excel export
export function formatDataForExcel<T extends Record<string, any>>(
  data: T[],
  formatters: Partial<Record<keyof T, (value: any) => any>> = {}
): T[] {
  return data.map((row) => {
    const formattedRow = { ...row }

    Object.entries(formatters).forEach(([key, formatter]) => {
      const typedKey = key as keyof T
      if (formattedRow[typedKey] !== undefined && formatter) {
        formattedRow[typedKey] = formatter(formattedRow[typedKey])
      }
    })

    return formattedRow
  })
}

// Common formatters
export const commonFormatters = {
  currency: (amount: number) => amount, // Keep as number for Excel
  currencyText: (amount: number) => `${amount.toLocaleString('vi-VN')} VNĐ`,
  percentage: (value: number) => `${value.toFixed(1)}%`,
  date: (timestamp: number) => new Date(timestamp).toLocaleDateString('vi-VN'),
  dateTime: (timestamp: number) => new Date(timestamp).toLocaleString('vi-VN'),
  boolean: (value: boolean) => (value ? 'Có' : 'Không'),
  emptyToText: (value: any, defaultText: string = '-') => value || defaultText,
}

// Example usage for different data types
export const exampleColumnMappings = {
  saleNotSyncVat: {
    tran_no: 'Mã giao dịch',
    tran_date: 'Thời gian',
    storeName: 'Cửa hàng',
    employee_name: 'Nhân viên',
    table_name: 'Loại bàn',
    payment_method_name: 'Phương thức thanh toán',
    voucher_code: 'Mã voucher',
    amount_origin: 'Số tiền gốc (VNĐ)',
    total_amount: 'Thành tiền (VNĐ)',
  },
  revenue: {
    store_name: 'Cửa hàng',
    revenue_gross: 'Doanh thu (VNĐ)',
    total_sales: 'Số lượng bán',
    peo_count: 'Số hóa đơn',
  },
  topStores: {
    storeName: 'Tên cửa hàng',
    totalRevenue: 'Tổng doanh thu (VNĐ)',
    totalSales: 'Tổng số lượng bán',
    totalBills: 'Tổng hóa đơn',
  },
}
