import { useCurrentBrand as usePosCurrentBrand } from '@/stores/posStore'
import { usePosData } from './use-pos-data'

/**
 * Hook to get the currently selected brand and related information
 * @deprecated Use useCurrentBrand from @/stores/posStore directly
 */
export const useCurrentBrand = () => {
  // Redirect to the new unified store
  const posCurrentBrand = usePosCurrentBrand()
  const { getBrandById, getStoresByBrand, getActiveStoresByBrand } = usePosData()

  // Get additional brand information
  const brandStores = posCurrentBrand.selectedBrand?.id ? getStoresByBrand(posCurrentBrand.selectedBrand.id) : []
  const activeBrandStores = posCurrentBrand.selectedBrand?.id ? getActiveStoresByBrand(posCurrentBrand.selectedBrand.id) : []

  // Get full brand data from POS system if available
  const fullBrandData = posCurrentBrand.selectedBrand?.id ? getBrandById(posCurrentBrand.selectedBrand.id) : null

  return {
    // Current brand info
    selectedBrand: posCurrentBrand.selectedBrand,
    setSelectedBrand: posCurrentBrand.setSelectedBrand,
    isLoading: posCurrentBrand.isLoading,

    // Related data
    brandStores,
    activeBrandStores,
    fullBrandData,

    // Computed values
    hasStores: brandStores.length > 0,
    hasActiveStores: activeBrandStores.length > 0,
    storeCount: brandStores.length,
    activeStoreCount: activeBrandStores.length,

    // Brand info
    brandName: posCurrentBrand.selectedBrand?.name || 'No Brand Selected',
    brandCurrency: posCurrentBrand.selectedBrand?.currency || 'VND',
    brandId: posCurrentBrand.selectedBrand?.brandId || posCurrentBrand.selectedBrand?.id || '',
  }
}

/**
 * Hook to check if a specific brand is currently selected
 */
export const useIsBrandSelected = (brandId: string) => {
  const { selectedBrand } = usePosCurrentBrand()
  return selectedBrand?.id === brandId || selectedBrand?.brandId === brandId
}

/**
 * Hook to switch to a specific brand by ID
 */
export const useSwitchToBrand = () => {
  const { setSelectedBrand } = usePosCurrentBrand()
  const { getBrandById } = usePosData()

  return (brandId: string) => {
    const brand = getBrandById(brandId)
    if (brand) {
      // Convert POS brand to Brand format
      const brandForSwitcher = {
        id: brand.id,
        name: brand.brand_name,
        logo: () => null, // Will be set by useBrandsData
        plan: `${brand.currency} • ${brand.brand_id}`,
        brandId: brand.brand_id,
        currency: brand.currency,
        active: brand.active === 1,
      }
      setSelectedBrand(brandForSwitcher)
    }
  }
}
