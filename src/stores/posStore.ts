import React from 'react'
import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import type {
  User,
  Company,
  Brand,
  Store,
  City,
  UserRole,
  UserPermissions,
} from '@/lib/auth-api'
import type { ApiStore } from '@/lib/stores-api'

// Brand display type for UI components
export interface BrandDisplay {
  id: string
  name: string
  logo: React.ElementType | null
  plan: string
  brandId: string
  currency: string
  active: boolean
}

// Unified POS State Interface
interface PosState {
  // Authentication
  isAuthenticated: boolean
  user: User | null
  userRole: UserRole | null
  userPermissions: UserPermissions | null
  jwtToken: string | null

  // Company & Brand
  company: Company | null
  brands: Brand[]
  selectedBrand: BrandDisplay | null

  // Stores & Cities
  stores: Store[]
  cities: City[]
  apiStores: ApiStore[]

  // UI State
  isLoading: boolean
  error: string | null

  // Actions
  setAuth: (data: {
    user: User
    userRole: UserRole | null
    userPermissions: UserPermissions | null
    company: Company
    brands: Brand[]
    stores: Store[]
    cities: City[]
    jwtToken: string
  }) => void

  setSelectedBrand: (brand: BrandDisplay) => void
  setApiStores: (stores: ApiStore[]) => void
  setLoading: (loading: boolean) => void
  setError: (error: string | null) => void
  clearAuth: () => void

  // Computed getters
  getActiveBrands: () => Brand[]
  getActiveStores: () => Store[]
  getBrandById: (id: string) => Brand | undefined
  getStoreById: (id: string) => Store | undefined
  getStoresByBrand: (brandId: string) => Store[]
  getApiStoresByBrand: (brandId: string) => ApiStore[]
}

// Create the store
export const usePosStore = create<PosState>()(
  persist(
    (set, get) => ({
      // Initial state
      isAuthenticated: false,
      user: null,
      userRole: null,
      userPermissions: null,
      jwtToken: null,
      company: null,
      brands: [],
      selectedBrand: null,
      stores: [],
      cities: [],
      apiStores: [],
      isLoading: false,
      error: null,

      // Actions
      setAuth: (data) => {
        // Convert first brand to the format expected by the brand switcher
        let selectedBrand = null
        if (data.brands && data.brands.length > 0) {
          const firstBrand = data.brands[0]
          selectedBrand = {
            id: firstBrand.id,
            name: firstBrand.brand_name,
            logo: null, // Will be set by useBrandsData
            plan: `${firstBrand.currency} • ${firstBrand.brand_id}`,
            brandId: firstBrand.brand_id,
            currency: firstBrand.currency,
            active: firstBrand.active === 1,
          }
        }

        set({
          isAuthenticated: true,
          user: data.user,
          userRole: data.userRole,
          userPermissions: data.userPermissions,
          company: data.company,
          brands: data.brands,
          stores: data.stores,
          cities: data.cities,
          jwtToken: data.jwtToken,
          selectedBrand: selectedBrand,
          error: null,
        })

        // Dispatch event for other components
        window.dispatchEvent(
          new CustomEvent('posAuthChanged', { detail: data })
        )

        // POS store initialized with selected brand
      },

      setSelectedBrand: (brand) => {
        set({ selectedBrand: brand })

        // Dispatch event for stores to refetch
        window.dispatchEvent(new CustomEvent('brandChanged', { detail: brand }))

        // Brand switched to: brand.name
      },

      setApiStores: (stores) => {
        set({ apiStores: stores })
      },

      setLoading: (loading) => {
        set({ isLoading: loading })
      },

      setError: (error) => {
        set({ error })
      },

      clearAuth: () => {
        set({
          isAuthenticated: false,
          user: null,
          userRole: null,
          userPermissions: null,
          jwtToken: null,
          company: null,
          brands: [],
          selectedBrand: null,
          stores: [],
          cities: [],
          apiStores: [],
          error: null,
        })

        // Clear localStorage
        localStorage.removeItem('pos_user_data')
        localStorage.removeItem('pos_jwt_token')
        localStorage.removeItem('pos_selected_brand')
        localStorage.removeItem('pos_stores_data')

        window.dispatchEvent(new CustomEvent('posAuthCleared'))
      },

      // Computed getters
      getActiveBrands: () => {
        const { brands } = get()
        return brands.filter((brand) => brand.active === 1)
      },

      getActiveStores: () => {
        const { stores } = get()
        return stores.filter((store) => store.active === 1)
      },

      getBrandById: (id) => {
        const { brands } = get()
        return brands.find((brand) => brand.id === id || brand.brand_id === id)
      },

      getStoreById: (id) => {
        const { stores } = get()
        return stores.find((store) => store.id === id || store.store_id === id)
      },

      getStoresByBrand: (brandId) => {
        const { stores } = get()
        return stores.filter((store) => store.brand_uid === brandId)
      },

      getApiStoresByBrand: (brandId) => {
        const { apiStores } = get()
        return apiStores.filter((store) => store.brand_uid === brandId)
      },
    }),
    {
      name: 'pos-store',
      partialize: (state) => ({
        // Only persist essential data
        isAuthenticated: state.isAuthenticated,
        user: state.user,
        userRole: state.userRole,
        userPermissions: state.userPermissions,
        jwtToken: state.jwtToken,
        company: state.company,
        brands: state.brands,
        selectedBrand: state.selectedBrand,
        stores: state.stores,
        cities: state.cities,
        apiStores: state.apiStores,
      }),
    }
  )
)

// Convenience hooks
export const useCurrentUser = () => {
  const { user, isAuthenticated, userRole, userPermissions, company } =
    usePosStore()
  return { user, isAuthenticated, userRole, userPermissions, company }
}

export const useCurrentBrand = () => {
  const { selectedBrand, setSelectedBrand, brands, getActiveBrands } =
    usePosStore()

  // Auto-select first brand if none is selected and brands are available
  React.useEffect(() => {
    if (!selectedBrand && brands.length > 0) {
      const firstBrand = brands[0]
      const brandForSwitcher = {
        id: firstBrand.id,
        name: firstBrand.brand_name,
        logo: null, // Will be set by useBrandsData
        plan: `${firstBrand.currency} • ${firstBrand.brand_id}`,
        brandId: firstBrand.brand_id,
        currency: firstBrand.currency,
        active: firstBrand.active === 1,
      }
      // Auto-selecting first brand
      setSelectedBrand(brandForSwitcher)
    }
  }, [selectedBrand, brands, setSelectedBrand])

  return {
    selectedBrand,
    setSelectedBrand,
    brands,
    activeBrands: getActiveBrands(),
    isLoading: false, // We'll handle loading at the component level
  }
}

export const useCurrentCompany = () => {
  const { company, user } = usePosStore()
  return {
    company,
    companyUid: company?.id || '',
    companyName: company?.company_name || '',
    userId: user?.id || '',
  }
}

export const usePosStores = () => {
  const {
    stores,
    apiStores,
    selectedBrand,
    getStoresByBrand,
    getApiStoresByBrand,
    setApiStores,
  } = usePosStore()

  return {
    stores,
    apiStores,
    selectedBrand,
    getStoresByBrand,
    getApiStoresByBrand,
    setApiStores,
    // Get stores for current brand
    currentBrandStores: selectedBrand ? getStoresByBrand(selectedBrand.id) : [],
    currentBrandApiStores: selectedBrand
      ? getApiStoresByBrand(selectedBrand.id)
      : [],
  }
}

// Helper function to initialize store from login response
export const initializePosStore = (loginResponse: {
  user: User
  user_role: UserRole
  user_permissions: UserPermissions
  company: Company
  brands: Brand[]
  stores: Store[]
  cities: City[]
  token: string
}) => {
  const { setAuth } = usePosStore.getState()

  setAuth({
    user: loginResponse.user,
    userRole: loginResponse.user_role,
    userPermissions: loginResponse.user_permissions,
    company: loginResponse.company,
    brands: loginResponse.brands,
    stores: loginResponse.stores,
    cities: loginResponse.cities,
    jwtToken: loginResponse.token,
  })
}

// Helper function to initialize store from existing localStorage data
export const initializePosStoreFromLocalStorage = () => {
  try {
    const userData = localStorage.getItem('pos_user_data')
    const jwtToken = localStorage.getItem('pos_jwt_token')
    const userRoleData = localStorage.getItem('pos_user_role_data')
    const companyData = localStorage.getItem('pos_company_data')
    const brandsData = localStorage.getItem('pos_brands_data')
    const storesData = localStorage.getItem('pos_stores_data')
    const citiesData = localStorage.getItem('pos_cities_data')

    if (userData && jwtToken && companyData && brandsData) {
      const { setAuth } = usePosStore.getState()

      setAuth({
        user: JSON.parse(userData),
        userRole: userRoleData ? JSON.parse(userRoleData) : null,
        userPermissions: null, // Not stored in old system
        company: JSON.parse(companyData),
        brands: JSON.parse(brandsData),
        stores: storesData ? JSON.parse(storesData) : [],
        cities: citiesData ? JSON.parse(citiesData) : [],
        jwtToken: jwtToken,
      })

      // POS store initialized from localStorage
      return true
    }
  } catch (_error) {
    // Silent error handling - failed to initialize POS store from localStorage
  }
  return false
}
