import * as React from 'react'
import { useCurrentBrand } from '@/stores/posStore'

// Legacy exports for backward compatibility
export const BrandProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  // The BrandProvider is now handled by the unified POS store
  // This is just a pass-through for backward compatibility
  return <>{children}</>
}

export const useSelectedBrand = () => {
  // Redirect to the new unified store
  return useCurrentBrand()
}
