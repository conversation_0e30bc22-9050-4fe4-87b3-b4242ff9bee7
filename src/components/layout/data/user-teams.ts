import { useMemo } from 'react'
import { AudioWaveform, Command, GalleryVerticalEnd, Store } from 'lucide-react'
import { usePosStore } from '@/stores/posStore'
import { usePosData } from '@/hooks/use-pos-data'

export const userData = {
  name: 'satnaing',
  email: '<EMAIL>',
  avatar: '/avatars/shadcn.jpg',
}

// Static fallback data for when user is not authenticated
export const fallbackTeamsData = [
  {
    name: 'POS TTM',
    logo: Command,
    plan: 'Work Hard. Have Fun. Make History',
  },
  {
    name: 'Acme Inc',
    logo: GalleryVerticalEnd,
    plan: 'Enterprise',
  },
  {
    name: 'Acme Corp.',
    logo: AudioWaveform,
    plan: 'Startup',
  },
]

// Hook to get brands data for the sidebar
export const useBrandsData = () => {
  const { isAuthenticated, brands, getActiveBrands } = usePosStore()
  const activeBrands = getActiveBrands()

  return useMemo(() => {
    if (!isAuthenticated || brands.length === 0) {
      // Convert fallback teams to brand format for consistency
      const fallbackData = fallbackTeamsData.map((team, index) => ({
        id: `fallback-${index}`,
        name: team.name,
        logo: team.logo,
        plan: team.plan,
        brandId: `fallback-${index}`,
        currency: 'VND',
        active: true,
      }))
      return fallbackData
    }

    // Convert brands to team format for the sidebar
    const brandsData = activeBrands.map((brand, index) => ({
      id: brand.id,
      name: brand.brand_name,
      logo: getBrandIcon(index),
      plan: `${brand.currency} • ${brand.brand_id}`,
      brandId: brand.brand_id,
      currency: brand.currency,
      active: brand.active === 1,
    }))

    return brandsData
  }, [brands, activeBrands, isAuthenticated])
}

// Get appropriate icon for each brand
const getBrandIcon = (index: number) => {
  const icons = [Store, Command, GalleryVerticalEnd, AudioWaveform]
  return icons[index % icons.length]
}

// Hook to get user data from POS system
export const usePosUserData = () => {
  const { user, isAuthenticated } = usePosData()

  return useMemo(() => {
    if (!isAuthenticated || !user) {
      return userData // fallback to static data
    }

    return {
      name: user.full_name || user.email,
      email: user.email,
      avatar: '/avatars/shadcn.jpg', // user.profile_image_path is not in AuthUser interface
    }
  }, [user, isAuthenticated])
}

// Legacy export for backward compatibility
export const teamsData = fallbackTeamsData
