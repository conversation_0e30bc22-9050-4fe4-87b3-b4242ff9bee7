import { IconMessages, IconUsers, IconDevices, IconReceiptDollar } from '@tabler/icons-react'

import { type NavItem } from '../types'

export const businessNavItems: NavItem[] = [
  {
    title: 'Chương Trình',
    icon: IconReceiptDollar,
    items: [
      {
        title: '<PERSON><PERSON><PERSON><PERSON><PERSON> mãi',
        url: '/sale/promotion'
      }
    ]
  },
  {
    title: 'Kênh Bán Hàng',
    icon: IconMessages,
    items: [
      {
        title: 'Kênh bán hàng',
        url: '/sales-channel/channel'
      },
      {
        title: 'Giảm giá',
        url: '/sale-channel/discount'
      }
    ]
  },
  {
    title: 'Thiết Bị',
    icon: IconDevices,
    items: [
      {
        title: 'Quản lý thiết bị',
        url: '/devices/list'
      },
      {
        title: '<PERSON>ại thiết bị',
        url: '/devices/types'
      }
    ]
  },
  {
    title: '<PERSON><PERSON><PERSON> Viên',
    icon: IconUsers,
    items: [
      {
        title: '<PERSON><PERSON> sách nhân viên',
        url: '/employee/list'
      },
      {
        title: '<PERSON><PERSON> s<PERSON><PERSON> chức vụ',
        url: '/employee/role'
      }
    ]
  },
  {
    title: 'Ứng Dụng',
    url: '/apps/store',
    badge: '3',
    icon: IconMessages
  }
]
