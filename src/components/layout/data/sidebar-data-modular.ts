import { type SidebarData } from '../types'
import { userData, fallbackTeamsData } from './user-teams'
import { generalNavItems } from './general-nav'
import { menuNavItems } from './menu-nav'
import { businessNavItems } from './business-nav'
import { reportsNavItems } from './reports-nav'
import { pagesNavItems } from './pages-nav'
import { settingsNavItems } from './settings-nav'

export const sidebarData: SidebarData = {
  user: userData,
  teams: fallbackTeamsData, // This is now just fallback data
  navGroups: [
    {
      title: 'General',
      items: [
        ...generalNavItems,
        ...menuNavItems,
        ...businessNavItems,
        ...reportsNavItems,
      ],
    },
    {
      title: 'Pages',
      items: pagesNavItems,
    },
    {
      title: 'Other',
      items: settingsNavItems,
    },
  ],
}
