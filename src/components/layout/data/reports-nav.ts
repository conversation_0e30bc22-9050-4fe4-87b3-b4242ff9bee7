import { IconMessages } from '@tabler/icons-react'
import { type NavItem } from '../types'

export const reportsNavItems: NavItem[] = [
  {
    title: 'Báo Cáo',
    icon: IconMessages,
    items: [
      {
        title: 'Báo C<PERSON>o <PERSON>h Thu',
        url: '/bao-cao/doanh-thu',
      },
      {
        title: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
        url: '/bao-cao/kiem-soat',
      },
      // {
      //   title: 'BC doanh thu - A',
      //   items: [
      //     {
      //       title: 'A01 - Tổng doanh thu (net)',
      //       url: '/bao-cao/doanh-thu',
      //     },
      //     {
      //       title: 'A02 - Doanh thu theo cửa hàng',
      //       url: '/bao-cao/doanh-thu/cua-hang',
      //     },
      //     {
      //       title: 'A03 - Doanh thu theo khu vực',
      //       url: '/bao-cao/doanh-thu/khu-vuc',
      //     },
      //   ],
      // },
      // {
      //   title: 'BC phân tích - B',
      //   items: [
      //     {
      //       title: 'B01 - <PERSON> hướng bán hàng (net)',
      //       url: '/report/revenue/sale-trend',
      //     },
      //     {
      //       title: 'B02 - Chương trình khuyến mãi',
      //       url: '/report/revenue/categories/promotion',
      //     },
      //     {
      //       title: 'B03 - Nguồn đơn hàng',
      //       url: '/report/revenue/categories/source',
      //     },
      //     {
      //       title: 'B04 - Mặt hàng bán chạy nhất',
      //       url: '/report/revenue/categories/item',
      //     },
      //     {
      //       title: 'B05 - Nhóm hàng bán chạy nhất',
      //       url: '/report/revenue/categories/category',
      //     },
      //     {
      //       title: 'B06 - Loại món bán chạy nhất',
      //       url: '/report/revenue/categories/item-class',
      //     },
      //     {
      //       title: 'B07 - Phương thức thanh toán',
      //       url: '/report/revenue/categories/payment-method',
      //     },
      //     {
      //       title: 'B08 - Báo cáo thời gian làm món trung bình',
      //       url: '/report/revenue/sale/cooking-item-time',
      //     },
      //     {
      //       title: 'B09 - Báo cáo thời gian hoàn thành đơn trung bình',
      //       url: '/report/revenue/sale/complete-sale-average-time',
      //     },
      //     {
      //       title: 'B10 - Báo cáo Combo (Beta)',
      //       url: '/report/revenue/categories/combo',
      //     },
      //     {
      //       title: 'B11 - Báo cáo Buffet',
      //       url: '/report/revenue/categories/buffet',
      //     },
      //   ],
      // },
      // {
      //   title: 'BC kiểm soát - C',
      //   items: [
      //     {
      //       title: 'C01 - Báo cáo tổng quan theo cửa hàng',
      //       url: '/report/revenue/revenue/general',
      //     },
      //     {
      //       title: 'C02 - Báo cáo ca',
      //       url: '/report/revenue/revenue/shift',
      //     },
      //     {
      //       title: 'C03 - Báo cáo hoá đơn theo thời gian',
      //       url: '/report/revenue/invoices/sale-by-date',
      //     },
      //     {
      //       title: 'C04 - Nhật ký order',
      //       url: '/report/revenue/invoices/sale-change-log',
      //     },
      //     {
      //       title: 'C05 - Báo cáo hoá đơn sửa, huỷ',
      //       url: '/report/revenue/invoices/sale-edit-delete',
      //     },
      //     {
      //       title: 'C06 - Báo cáo sửa hoá đơn sau in tạm tính, chốt đồ',
      //       url: '/report/revenue/invoices/sale-change-after-print-check-list',
      //     },
      //     {
      //       title: 'C07 - Báo cáo theo dõi bán hàng Dine-in',
      //       url: '/report/revenue/sale/track-sale',
      //     },
      //     {
      //       title: 'C08 - Báo cáo bỏ món',
      //       url: '/report/revenue/sale/remove-item',
      //     },
      //     {
      //       title: 'C09 - Báo cáo lịch sử hết món',
      //       url: '/report/revenue/sale/item-out-of-stock-history',
      //     },
      //     {
      //       title: 'C10 - Báo cáo lượt order',
      //       url: '/report/revenue/sale/employee-turn-order',
      //     },
      //     {
      //       title: 'C11 - Báo cáo món thay đổi tại cửa hàng',
      //       url: '/report/revenue/sale/store-change-menu',
      //     },
      //     {
      //       title: 'C12 - Báo cáo đặt cọc',
      //       url: '/report/revenue/sale/deposit',
      //     },
      //     {
      //       title: 'C14 - Theo dõi đơn hàng Online',
      //       url: '/report/revenue/invoices/order-online-tracking',
      //     },
      //     {
      //       title: 'C15 - Đối soát thanh toán MoMo',
      //       url: '/report/revenue/invoices/momo-payments',
      //     },
      //   ],
      // },
      {
        title: 'BC kế toán - D',
        items: [
          // {
          //   title: 'D01 - Quản lý công nợ',
          //   url: '/report/accounting/debt-management',
          // },
          {
            title: 'D02 - Báo cáo đối soát hoá đơn VAT',
            url: '/bao-cao/ke-toan/hoa-don',
          },
          // {
          //   title: 'D03 - Báo cáo thu chi',
          //   url: '/report/accounting/revenue/cash-in-cash-out',
          // },
          // {
          //   title: 'D04 - Bảng kê tổng hợp PTTT',
          //   url: '/report/accounting/payment-method-audit',
          // },
          // {
          //   title: 'D05 - Báo cáo bán hàng',
          //   url: '/report/accounting/sale-audit',
          // },
          // {
          //   title: 'D06 - Báo cáo bán hàng theo ngày',
          //   url: '/report/accounting/sale-group-by-day',
          // },
          {
            title: 'D07 - Bảng kê chi tiết hoá đơn bán hàng',
            url: '/bao-cao/ke-toan/bang-ke-hoa-don',
          },
          // {
          //   title: 'D08 - Báo cáo tổng hợp món ăn',
          //   url: '/report/accounting/general-item',
          // },
        ],
      },
    ],
  },
]
