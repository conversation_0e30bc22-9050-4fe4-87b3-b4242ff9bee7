import {
  IconBarrierBlock,
  IconBug,
  IconError404,
  IconLock,
  IconLockAccess,
  IconServerOff,
  IconUserOff,
} from '@tabler/icons-react'
import { ClerkLogo } from '@/assets/clerk-logo'
import { type NavItem } from '../types'

export const pagesNavItems: NavItem[] = [
  {
    title: 'Secured by Clerk',
    icon: Clerk<PERSON><PERSON>,
    items: [
      {
        title: 'Sign In',
        url: '/clerk/sign-in',
      },
      {
        title: 'Sign Up',
        url: '/clerk/sign-up',
      },
      {
        title: 'User Management',
        url: '/clerk/user-management',
      },
    ],
  },
  {
    title: 'Auth',
    icon: IconLockAccess,
    items: [
      {
        title: 'Sign In',
        url: '/sign-in',
      },
      {
        title: 'Sign In (2 Col)',
        url: '/sign-in-2',
      },
      {
        title: 'Sign Up',
        url: '/sign-up',
      },
      {
        title: 'Forgot Password',
        url: '/forgot-password',
      },
      {
        title: 'OTP',
        url: '/otp',
      },
    ],
  },
  {
    title: 'Errors',
    icon: IconBug,
    items: [
      {
        title: 'Unauthorized',
        url: '/401',
        icon: IconLock,
      },
      {
        title: 'Forbidden',
        url: '/403',
        icon: IconUserOff,
      },
      {
        title: 'Not Found',
        url: '/404',
        icon: IconError404,
      },
      {
        title: 'Internal Server Error',
        url: '/500',
        icon: IconServerOff,
      },
      {
        title: 'Maintenance Error',
        url: '/503',
        icon: IconBarrierBlock,
      },
    ],
  },
]
