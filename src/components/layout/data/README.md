# Sidebar Data Structure

This directory contains the modular sidebar navigation data structure for the application.

## 📁 File Structure

```
src/components/layout/data/
├── index.ts                    # Main export file
├── sidebar-data-modular.ts     # Main sidebar data composition
├── user-teams.ts              # User and teams data
├── general-nav.ts             # General navigation items
├── menu-nav.ts                # Menu/Thực <PERSON>n navigation
├── business-nav.ts            # Business operations navigation
├── reports-nav.ts             # Reports/Báo Cáo navigation
├── pages-nav.ts               # Pages navigation (Auth, Errors)
├── settings-nav.ts            # Settings navigation

└── README.md                  # This documentation
```

## 🎯 Purpose

The modular structure provides:

- **Better Maintainability**: Each navigation section is in its own file
- **Easier Collaboration**: Multiple developers can work on different sections
- **Cleaner Code**: Smaller, focused files instead of one large file
- **Reusability**: Individual navigation sections can be imported separately
- **Type Safety**: Full TypeScript support maintained

## 📋 Module Descriptions

### `user-teams.ts`
Contains user profile data and team information for the sidebar header.

### `general-nav.ts`
Main application navigation including:
- <PERSON><PERSON> (Home)
- <PERSON><PERSON><PERSON> (Store) with nested sub-menus

### `menu-nav.ts`
Menu management navigation including:
- Món ăn (Items)
- Nhóm món (Categories)
- Customization
- And more menu-related features

### `business-nav.ts`
Business operations navigation:
- Chương Trình (Programs)
- Kênh Bán Hàng (Sales Channels)
- Thiết Bị (Devices)
- Nhân Viên (Staff)
- Ứng Dụng (Applications)

### `reports-nav.ts`
Comprehensive reporting navigation with 4 main categories:
- Báo cáo doanh thu - A (Revenue Reports)
- Báo cáo phân tích - B (Analytics Reports)
- Báo cáo kiểm soát - C (Control Reports)
- Báo cáo kế toán - D (Accounting Reports)

### `pages-nav.ts`
System pages navigation:
- Authentication pages
- Error pages
- Clerk integration

### `settings-nav.ts`
Application settings and help:
- User settings
- System preferences
- Help center

## 🚀 Usage

### Import the complete sidebar data:
```typescript
import { sidebarData } from '@/components/layout/data'
```

### Import individual modules:
```typescript
import { 
  generalNavItems, 
  menuNavItems, 
  reportsNavItems 
} from '@/components/layout/data'
```

### Custom composition:
```typescript
import { userData, teamsData } from '@/components/layout/data'
import { generalNavItems } from '@/components/layout/data'

const customSidebarData = {
  user: userData,
  teams: teamsData,
  navGroups: [
    {
      title: 'Main',
      items: generalNavItems,
    },
  ],
}
```

## 🔧 Adding New Navigation Items

1. **For existing sections**: Edit the appropriate module file
2. **For new sections**: Create a new module file and add it to `sidebar-data-modular.ts`
3. **Update exports**: Add new exports to `index.ts`

## 📝 Notes

- All modules maintain full TypeScript support
- Navigation structure supports unlimited nesting levels
- All URLs are properly typed and validated
- The old monolithic structure has been completely replaced with this modular approach
