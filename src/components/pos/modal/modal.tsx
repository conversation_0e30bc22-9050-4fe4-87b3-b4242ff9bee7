import React, { ReactNode } from 'react'

import { Button } from '@/components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog'

interface PosModalProps {
  title: string
  open: boolean
  onOpenChange: (open: boolean) => void
  onCancel: () => void
  onConfirm: () => void
  children: ReactNode
  cancelText?: string
  confirmText?: string
  isLoading?: boolean
  confirmDisabled?: boolean
  hideButtons?: boolean
  centerTitle?: boolean
  maxWidth?: string
  disableCancelButton?: boolean
}

export const PosModal: React.FC<PosModalProps> = ({
  title,
  open,
  onOpenChange,
  onCancel,
  onConfirm,
  children,
  cancelText = 'Hủy',
  confirmText = 'Lưu',
  isLoading = false,
  confirmDisabled = false,
  hideButtons = false,
  centerTitle = false,
  maxWidth = 'sm:max-w-[400px]',
  disableCancelButton = false
}) => {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className={maxWidth}>
        <DialogHeader>
          <DialogTitle className={`text-lg font-medium ${centerTitle ? 'text-center' : ''}`}>
            {title}
          </DialogTitle>
        </DialogHeader>

        <div className='py-4'>{children}</div>

        {!hideButtons && (
          <DialogFooter>
            <div
              className={`flex w-full ${disableCancelButton ? 'justify-end' : 'justify-between'}`}
            >
              {!disableCancelButton && (
                <Button type='button' variant='outline' onClick={onCancel} disabled={isLoading}>
                  {cancelText}
                </Button>
              )}
              <Button onClick={onConfirm} disabled={isLoading || confirmDisabled}>
                {isLoading ? 'Đang xử lý...' : confirmText}
              </Button>
            </div>
          </DialogFooter>
        )}
      </DialogContent>
    </Dialog>
  )
}
