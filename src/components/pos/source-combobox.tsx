import * as React from 'react'

import { useSourcesForAutocomplete } from '@/hooks/api/use-sources'

import { Input } from '@/components/ui'

interface SourceComboboxProps {
  value?: string
  onValueChange?: (value: string) => void
  onSelectItem?: (item: { source_id: string; source_name: string }) => void
  placeholder?: string
  className?: string
  disabled?: boolean
}

export function SourceCombobox({
  value,
  onValueChange,
  onSelectItem,
  placeholder = 'Nhập tên nguồn đơn hàng',
  className,
  disabled = false
}: SourceComboboxProps) {
  const [showDropdown, setShowDropdown] = React.useState(false)
  const dropdownRef = React.useRef<HTMLDivElement>(null)
  const inputRef = React.useRef<HTMLInputElement>(null)

  // Fetch sources for autocomplete
  const { data: sources = [], isLoading } = useSourcesForAutocomplete()

  // Filter sources based on current input value
  const filteredOptions = sources.filter(source =>
    source.source_name.toLowerCase().includes((value || '').toLowerCase())
  )

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const inputValue = e.target.value
    onValueChange?.(inputValue) // Manual input - no sourceId
    // Show dropdown when user starts typing or focuses
    setShowDropdown(true)
  }

  const handleInputFocus = () => {
    setShowDropdown(true)
  }

  const handleInputKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === 'Escape') {
      setShowDropdown(false)
    }
  }

  const handleOptionClick = (source: { source_id: string; source_name: string }) => {
    // Update input value
    onValueChange?.(source.source_name)
    // Call onSelectItem callback with full source object
    onSelectItem?.(source)
    setShowDropdown(false)
    inputRef.current?.focus()
  }

  // Close dropdown when clicking outside
  React.useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setShowDropdown(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  return (
    <div className='relative' ref={dropdownRef}>
      <Input
        ref={inputRef}
        value={value || ''}
        onChange={handleInputChange}
        onFocus={handleInputFocus}
        onKeyDown={handleInputKeyDown}
        placeholder={placeholder}
        className={className}
        disabled={disabled}
      />

      {showDropdown && (
        <div className='absolute z-50 mt-1 max-h-60 w-full overflow-auto rounded-md border border-gray-200 bg-white shadow-lg'>
          {isLoading && <div className='py-6 text-center text-sm text-gray-500'>Đang tải...</div>}

          {!isLoading && filteredOptions.length === 0 && (
            <div className='py-6 text-center text-sm text-gray-500'>
              {value && value.trim()
                ? `Nhấn Enter để sử dụng "${value.trim()}"`
                : 'Không tìm thấy nguồn nào.'}
            </div>
          )}

          {!isLoading && filteredOptions.length > 0 && (
            <ul className='py-1'>
              {filteredOptions.map((source, index) => (
                <li
                  key={`${source.source_id}-${index}`}
                  className='cursor-pointer px-3 py-2 text-sm text-gray-900 hover:bg-gray-100'
                  onClick={() => handleOptionClick(source)}
                >
                  {source.source_name}
                </li>
              ))}
            </ul>
          )}
        </div>
      )}
    </div>
  )
}
