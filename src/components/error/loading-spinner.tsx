import { Skeleton } from '@/components/ui/skeleton'

export function LoadingSpinner() {
  return (
    <div className='container mx-auto px-4 py-8'>
      <div className='space-y-6'>
        {/* Header skeleton */}
        <div className='mb-8'>
          <div className='mb-4 flex items-center justify-between'>
            <Skeleton className='h-8 w-8' />
            <Skeleton className='h-10 w-24' />
          </div>
          <div className='text-center'>
            <Skeleton className='mx-auto h-8 w-48' />
          </div>
        </div>

        {/* Form skeleton */}
        <div className='mx-auto max-w-4xl'>
          <div className='p-6'>
            <div className='space-y-6'>
              {/* Form fields skeleton */}
              <div className='flex items-center gap-4'>
                <Skeleton className='h-4 w-48' />
                <Skeleton className='h-10 flex-1' />
              </div>

              <div className='flex items-center gap-4'>
                <Skeleton className='h-4 w-48' />
                <Skeleton className='h-10 flex-1' />
              </div>

              {/* Dish selection skeleton */}
              <div className='space-y-4 pt-6'>
                <Skeleton className='h-6 w-64' />
                <Skeleton className='h-16 w-full' />
              </div>

              {/* Create group button skeleton */}
              <div className='flex justify-center pt-4'>
                <Skeleton className='h-10 w-24' />
              </div>

              {/* Groups list skeleton */}
              <div className='space-y-6 pt-6'>
                <Skeleton className='h-6 w-48' />
                {Array.from({ length: 2 }).map((_, index) => (
                  <div key={index} className='space-y-3'>
                    <div className='flex items-center justify-between'>
                      <Skeleton className='h-5 w-64' />
                      <div className='flex gap-2'>
                        <Skeleton className='h-8 w-12' />
                        <Skeleton className='h-8 w-12' />
                      </div>
                    </div>
                    <div className='grid grid-cols-2 gap-3 md:grid-cols-3 lg:grid-cols-4'>
                      {Array.from({ length: 4 }).map((_, itemIndex) => (
                        <Skeleton key={itemIndex} className='h-20 w-full' />
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
