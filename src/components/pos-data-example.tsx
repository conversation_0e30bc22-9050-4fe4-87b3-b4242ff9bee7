import { usePosData } from '@/hooks/use-pos-data'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { useState } from 'react'

/**
 * Example component showing how to use POS data in your application
 */
export function PosDataExample() {
  const {
    user,
    company,
    activeBrands,
    getBrandById,
    getActiveStoresByBrand,
    getPrimaryBrand,
    getPrimaryStore,
    getStats,
    isAuthenticated
  } = usePosData()

  const [selectedBrandId, setSelectedBrandId] = useState<string>('')

  if (!isAuthenticated) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>POS Data Example</CardTitle>
          <CardDescription>Please log in to see your data</CardDescription>
        </CardHeader>
      </Card>
    )
  }

  const selectedBrand = selectedBrandId ? getBrandById(selectedBrandId) : null
  const brandStores = selectedBrandId ? getActiveStoresByBrand(selectedBrandId) : []
  const stats = getStats()
  const primaryBrand = getPrimaryBrand()
  const primaryStore = getPrimaryStore()

  return (
    <div className="space-y-6 max-w-4xl">
      <Card>
        <CardHeader>
          <CardTitle>Welcome, {user?.full_name}!</CardTitle>
          <CardDescription>
            You're logged into {company?.company_name} with {stats.activeBrands} active brands and {stats.activeStores} active stores.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">{stats.activeBrands}</div>
              <div className="text-sm text-muted-foreground">Active Brands</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{stats.activeStores}</div>
              <div className="text-sm text-muted-foreground">Active Stores</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">{primaryBrand?.currency || 'VND'}</div>
              <div className="text-sm text-muted-foreground">Currency</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">{stats.userRole}</div>
              <div className="text-sm text-muted-foreground">Your Role</div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Primary Business Info</CardTitle>
          <CardDescription>Your main brand and store information</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {primaryBrand && (
            <div>
              <h4 className="font-medium">Primary Brand</h4>
              <div className="flex items-center gap-2 mt-1">
                <span>{primaryBrand.brand_name}</span>
                <Badge variant="outline">{primaryBrand.brand_id}</Badge>
                <Badge>{primaryBrand.currency}</Badge>
              </div>
            </div>
          )}
          
          {primaryStore && (
            <div>
              <h4 className="font-medium">Primary Store</h4>
              <div className="mt-1">
                <div className="font-medium">{primaryStore.store_name}</div>
                <div className="text-sm text-muted-foreground">{primaryStore.address}</div>
                <div className="text-sm text-muted-foreground">Phone: {primaryStore.phone}</div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Brand & Store Explorer</CardTitle>
          <CardDescription>Select a brand to see its stores</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <label className="text-sm font-medium">Select Brand:</label>
            <Select value={selectedBrandId} onValueChange={setSelectedBrandId}>
              <SelectTrigger className="mt-1">
                <SelectValue placeholder="Choose a brand..." />
              </SelectTrigger>
              <SelectContent>
                {activeBrands.map((brand) => (
                  <SelectItem key={brand.id} value={brand.id}>
                    {brand.brand_name} ({brand.brand_id})
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {selectedBrand && (
            <div>
              <h4 className="font-medium">Brand Details</h4>
              <div className="bg-muted p-3 rounded mt-2">
                <div><strong>Name:</strong> {selectedBrand.brand_name}</div>
                <div><strong>ID:</strong> {selectedBrand.brand_id}</div>
                <div><strong>Currency:</strong> {selectedBrand.currency}</div>
                <div><strong>Created:</strong> {new Date(parseInt(selectedBrand.created_at) * 1000).toLocaleDateString()}</div>
              </div>
            </div>
          )}

          {brandStores.length > 0 && (
            <div>
              <h4 className="font-medium">Stores in this Brand ({brandStores.length})</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3 mt-2">
                {brandStores.map((store) => (
                  <div key={store.id} className="border rounded p-3">
                    <div className="font-medium">{store.store_name}</div>
                    <div className="text-sm text-muted-foreground">{store.address}</div>
                    <div className="text-sm text-muted-foreground">Phone: {store.phone}</div>
                    <div className="flex gap-2 mt-2">
                      <Badge variant="outline">{store.store_id}</Badge>
                      {store.is_franchise === 1 && <Badge variant="secondary">Franchise</Badge>}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
          <CardDescription>Common operations with your data</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4 border rounded">
              <div className="text-lg font-medium">Total Revenue</div>
              <div className="text-sm text-muted-foreground">Across all stores</div>
              <div className="text-2xl font-bold text-green-600 mt-2">Coming Soon</div>
            </div>
            <div className="text-center p-4 border rounded">
              <div className="text-lg font-medium">Orders Today</div>
              <div className="text-sm text-muted-foreground">Real-time count</div>
              <div className="text-2xl font-bold text-blue-600 mt-2">Coming Soon</div>
            </div>
            <div className="text-center p-4 border rounded">
              <div className="text-lg font-medium">Top Products</div>
              <div className="text-sm text-muted-foreground">Best sellers</div>
              <div className="text-2xl font-bold text-purple-600 mt-2">Coming Soon</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
