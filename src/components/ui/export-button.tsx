import { Download, Loader2 } from 'lucide-react'
import { cn } from '@/lib/utils'
import {
  useExcelExport,
  useMultiSheetExcelExport,
  generateFilenameWithDateRange,
} from '@/hooks/use-excel-export'
import { Button } from '@/components/ui/button'

interface ExportButtonProps {
  onClick: () => void | Promise<void>
  disabled?: boolean
  isLoading?: boolean
  children?: React.ReactNode
  className?: string
  size?: 'default' | 'sm' | 'lg' | 'icon'
  variant?:
    | 'default'
    | 'destructive'
    | 'outline'
    | 'secondary'
    | 'ghost'
    | 'link'
  icon?: React.ReactNode
  loadingText?: string
}

export function ExportButton({
  onClick,
  disabled = false,
  isLoading = false,
  children = 'Xuất Excel',
  className,
  size = 'sm',
  variant = 'outline',
  icon,
  loadingText = 'Đang xuất...',
}: ExportButtonProps) {
  const handleClick = async () => {
    try {
      await onClick()
    } catch (error) {
      console.error('Export button error:', error)
    }
  }

  return (
    <Button
      onClick={handleClick}
      disabled={disabled || isLoading}
      size={size}
      variant={variant}
      className={cn('gap-2', className)}
    >
      {isLoading ? (
        <>
          <Loader2 className='h-4 w-4 animate-spin' />
          {loadingText}
        </>
      ) : (
        <>
          {icon || <Download className='h-4 w-4' />}
          {children}
        </>
      )}
    </Button>
  )
}

// Specialized export buttons for common use cases
interface DataExportButtonProps<T> {
  data: T[]
  filename?: string
  sheetName?: string
  columnMapping?: Record<keyof T, string>
  className?: string
  size?: 'default' | 'sm' | 'lg' | 'icon'
  variant?:
    | 'default'
    | 'destructive'
    | 'outline'
    | 'secondary'
    | 'ghost'
    | 'link'
  children?: React.ReactNode
  onExportStart?: () => void
  onExportComplete?: () => void
  onExportError?: (error: Error) => void
  disabled?: boolean
}

export function DataExportButton<T extends Record<string, any>>({
  data,
  filename,
  sheetName,
  columnMapping,
  className,
  size = 'sm',
  variant = 'outline',
  children = 'Xuất Excel',
  onExportStart,
  onExportComplete,
  onExportError,
  disabled = false,
}: DataExportButtonProps<T>) {
  const { exportData, isExporting } = useExcelExport({
    data,
    filename,
    sheetName,
    columnMapping,
    onExportStart,
    onExportComplete,
    onExportError,
  })

  return (
    <ExportButton
      onClick={exportData}
      disabled={disabled || data.length === 0}
      isLoading={isExporting}
      className={className}
      size={size}
      variant={variant}
    >
      {children}
    </ExportButton>
  )
}

// Multi-sheet export button
interface MultiSheetExportButtonProps {
  sheets: Array<{
    data: Record<string, any>[]
    sheetName: string
    columnMapping?: Record<string, string>
  }>
  filename?: string
  className?: string
  size?: 'default' | 'sm' | 'lg' | 'icon'
  variant?:
    | 'default'
    | 'destructive'
    | 'outline'
    | 'secondary'
    | 'ghost'
    | 'link'
  children?: React.ReactNode
  onExportStart?: () => void
  onExportComplete?: () => void
  onExportError?: (error: Error) => void
}

export function MultiSheetExportButton({
  sheets,
  filename,
  className,
  size = 'sm',
  variant = 'outline',
  children = 'Xuất Excel (Nhiều sheet)',
  onExportStart,
  onExportComplete,
  onExportError,
}: MultiSheetExportButtonProps) {
  const { exportData, isExporting } = useMultiSheetExcelExport({
    sheets,
    filename,
    onExportStart,
    onExportComplete,
    onExportError,
  })

  const hasData = sheets.some((sheet) => sheet.data.length > 0)

  return (
    <ExportButton
      onClick={exportData}
      disabled={!hasData}
      isLoading={isExporting}
      className={className}
      size={size}
      variant={variant}
    >
      {children}
    </ExportButton>
  )
}

// Quick export button for common data types
interface QuickExportButtonProps {
  type: 'sale-not-sync-vat' | 'revenue' | 'top-stores' | 'custom'
  data: Record<string, any>[]
  filename?: string
  dateRange?: { from: Date; to: Date }
  className?: string
  customColumnMapping?: Record<string, string>
  disabled?: boolean
}

export function QuickExportButton({
  type,
  data,
  filename,
  dateRange,
  className,
  customColumnMapping,
  disabled = false,
}: QuickExportButtonProps) {
  // Import column mappings directly
  const exampleColumnMappings = {
    saleNotSyncVat: {
      tran_no: 'Mã giao dịch',
      tran_date: 'Thời gian',
      storeName: 'Cửa hàng',
      employee_name: 'Nhân viên',
      table_name: 'Loại bàn',
      payment_method_name: 'Phương thức thanh toán',
      voucher_code: 'Mã voucher',
      amount_origin: 'Số tiền gốc (VNĐ)',
      total_amount: 'Thành tiền (VNĐ)',
    },
    revenue: {
      store_name: 'Cửa hàng',
      revenue_gross: 'Doanh thu (VNĐ)',
      total_sales: 'Số lượng bán',
      peo_count: 'Số hóa đơn',
    },
    topStores: {
      storeName: 'Tên cửa hàng',
      totalRevenue: 'Tổng doanh thu (VNĐ)',
      totalSales: 'Tổng số lượng bán',
      totalBills: 'Tổng hóa đơn',
    },
  }

  const getConfig = () => {
    switch (type) {
      case 'sale-not-sync-vat':
        return {
          columnMapping: exampleColumnMappings.saleNotSyncVat,
          sheetName: 'Giao dịch chưa đồng bộ VAT',
          baseName: 'giao-dich-chua-dong-bo-vat',
        }
      case 'revenue':
        return {
          columnMapping: exampleColumnMappings.revenue,
          sheetName: 'Doanh thu',
          baseName: 'doanh-thu',
        }
      case 'top-stores':
        return {
          columnMapping: exampleColumnMappings.topStores,
          sheetName: 'Top cửa hàng',
          baseName: 'top-cua-hang',
        }
      case 'custom':
        return {
          columnMapping: customColumnMapping || {},
          sheetName: 'Dữ liệu',
          baseName: 'du-lieu',
        }
      default:
        return {
          columnMapping: {},
          sheetName: 'Dữ liệu',
          baseName: 'export',
        }
    }
  }

  const config = getConfig()
  const finalFilename =
    filename || generateFilenameWithDateRange(config.baseName, dateRange)

  return (
    <DataExportButton
      data={data}
      filename={finalFilename}
      sheetName={config.sheetName}
      columnMapping={config.columnMapping}
      className={className}
      disabled={disabled}
    />
  )
}
