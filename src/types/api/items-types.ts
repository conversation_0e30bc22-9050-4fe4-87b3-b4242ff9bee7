// Create Item Types
export interface CreateItemExtraData {
  price_by_source: Array<{
    source_id: string
    price: number
  }>
  is_virtual_item: number
  is_item_service: number
  no_update_quantity_toping: number
  enable_edit_price: number
  is_buffet_item: number
  exclude_items_buffet: string[]
  up_size_buffet: string[]
}

export interface CreateItemPayload {
  unit_uid: string
  ta_price: number
  ots_tax: number
  ta_tax: number
  extra_data: CreateItemExtraData
  item_type_uid: string
  item_name: string
  city_uid: string
  company_uid: string
  brand_uid: string
  item_id: string
  ots_price: number
  sort: number
  item_class_uid?: string
  unit_secondary_uid?: string
  description?: string
  barcode?: string
  sku?: string
  vat_percent?: number
  cooking_time?: number
  order_limit?: number
  selected_days?: string[]
  selected_hours?: string[]
}

export interface CreateItemResponse {
  data: {
    id: string
    item_id: string
    item_name: string
    created_at: number
    updated_at: number
  }
  message: string
  track_id: string
}
