export interface SourceExtraData {
  commission?: number
  exclude_ship?: number
  payment_type?: string
  deduct_tax_rate?: number
  require_tran_no?: number
  use_order_online?: number
  payment_method_id?: string
  payment_method_name?: string
  voucher_run_partner?: string | null
  not_show_partner_bill?: number
  marketing_partner_cost?: number
  marketing_partner_cost_type?: string
  marketing_partner_cost_to_date?: number
  marketing_partner_cost_hour_day?: number
  marketing_partner_cost_date_week?: number
  marketing_partner_cost_from_date?: number
}

export interface Source {
  source_id: string
  source_name: string
  id: string
  created_at: number
  created_by: string | null
  updated_at: number
  updated_by: string | null
  deleted: boolean
  deleted_at: number | null
  deleted_by: string | null
  source_type: string[]
  description: string | null
  extra_data: SourceExtraData | null
  is_fb: number
  active: number
  revision: string | null
  brand_uid: string
  company_uid: string
  sort: number
  is_fabi: number
  store_uid: string
  partner_config: number
  row: number
  stores: number
}

export interface SourcesParams {
  skip_limit?: boolean
  company_uid: string
  brand_uid: string
  city_uid: string
}

export type SourcesResponse = Source[]

// Create Item Types
export interface CreateItemExtraData {
  price_by_source: Array<{
    source_id: string
    price: number
  }>
  is_virtual_item: number
  is_item_service: number
  no_update_quantity_toping: number
  enable_edit_price: number
  is_buffet_item: number
  exclude_items_buffet: string[]
  up_size_buffet: string[]
}

export interface CreateItemPayload {
  unit_uid: string
  ta_price: number
  ots_tax: number
  ta_tax: number
  extra_data: CreateItemExtraData
  item_type_uid: string
  item_name: string
  city_uid: string
  company_uid: string
  brand_uid: string
  item_id: string
  ots_price: number
  sort: number
  item_class_uid?: string
  unit_secondary_uid?: string
  description?: string
  barcode?: string
  sku?: string
  vat_percent?: number
  cooking_time?: number
  order_limit?: number
  selected_days?: string[]
  selected_hours?: string[]
}

export interface CreateItemResponse {
  data: {
    id: string
    item_id: string
    item_name: string
    created_at: number
    updated_at: number
  }
  message: string
  track_id: string
}
