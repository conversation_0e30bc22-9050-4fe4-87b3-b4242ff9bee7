export interface PaginationState {
  currentPage: number
  pageSize: number
  totalItems: number
  totalPages: number
}

export interface PaginationInfo extends PaginationState {
  startIndex: number
  endIndex: number
  hasNextPage: boolean
  hasPreviousPage: boolean
}

export interface PaginationCallbacks {
  onPageChange?: (page: number) => void
  onPageSizeChange?: (pageSize: number) => void
}

export interface PaginationOptions {
  showPageSizeSelector?: boolean
  showItemCount?: boolean
  showFirstLastButtons?: boolean
  pageSizeOptions?: number[]
  disabled?: boolean
}

export interface PaginationProps
  extends PaginationState,
    PaginationCallbacks,
    PaginationOptions {
  className?: string
}

export interface TablePaginationProps<T> extends PaginationOptions {
  data: T[]
  pageSize?: number
  className?: string
  children: (paginatedData: T[], pagination: PaginationInfo) => React.ReactNode
}

// URL Search params for pagination
export interface PaginationSearchParams {
  page?: number
  pageSize?: number
}

// Hook return types
export interface UsePaginationReturn {
  currentPage: number
  pageSize: number
  setPage: (page: number) => void
  setPageSize: (pageSize: number) => void
}

export interface UsePaginatedDataReturn<T> {
  paginatedData: T[]
  currentPage: number
  totalPages: number
  pageSize: number
  totalItems: number
  startIndex: number
  endIndex: number
}
