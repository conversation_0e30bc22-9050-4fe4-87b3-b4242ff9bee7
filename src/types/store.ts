import { z } from 'zod'

const storeStatusSchema = z.union([z.literal('active'), z.literal('inactive')])
export type StoreStatus = z.infer<typeof storeStatusSchema>

const _apiStoreSchema = z.object({
  id: z.string(),
  active: z.number(),
  address: z.string(),
  city_uid: z.string(),
  created_at: z.number(),
  expiry_date: z.number(),
  email: z.string(),
  fb_store_id: z.number(),
  is_fabi: z.number(),
  updated_by: z.string(),
  updated_at: z.number(),
  brand_uid: z.string(),
  company_uid: z.string(),
  latitude: z.number(),
  longitude: z.number(),
  phone: z.string(),
  store_id: z.string(),
  store_name: z.string(),
  sort: z.number(),
  is_ahamove_active: z.number(),
  phone_manager: z.string(),
  delivery_services: z.string(),
  extra_data: z.record(z.any()),
  email_delivery_service: z.string(),
  city_name: z.string(),
  city_id: z.string(),
})
export type ApiStore = z.infer<typeof _apiStoreSchema>

const storeSchema = z.object({
  id: z.string(),
  name: z.string(),
  code: z.string(),
  status: storeStatusSchema,
  address: z.string(),
  phone: z.string(),
  email: z.string(),
  managerPhone: z.string().optional(),
  companyId: z.string(),
  brandId: z.string(),
  cityId: z.string(),
  cityName: z.string(),
  isActive: z.boolean(),
  isFabi: z.boolean(),
  isAhamoveActive: z.boolean(),
  latitude: z.number(),
  longitude: z.number(),
  deliveryServices: z.string(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
  expiryDate: z.coerce.date(),
  extraData: z.record(z.any()).optional(),
  sort: z.number().optional(),
})
export type Store = z.infer<typeof storeSchema>

export const storeListSchema = z.array(storeSchema)

export type StoreApiResponse = {
  data: ApiStore[]
  track_id?: string
}

export const storeStatuses = [
  { value: 'active', label: 'Hoạt động' },
  { value: 'inactive', label: 'Không hoạt động' },
]

export const convertApiStoreToStore = (apiStore: ApiStore): Store => {
  return {
    id: apiStore.id,
    name: apiStore.store_name,
    code: apiStore.store_id,
    status: apiStore.active === 1 ? 'active' : 'inactive',
    address: apiStore.address,
    phone: apiStore.phone,
    email: apiStore.email,
    managerPhone: apiStore.phone_manager || undefined,
    companyId: apiStore.company_uid,
    brandId: apiStore.brand_uid,
    cityId: apiStore.city_id,
    cityName: apiStore.city_name,
    isActive: apiStore.active === 1,
    isFabi: apiStore.is_fabi === 1,
    isAhamoveActive: apiStore.is_ahamove_active === 1,
    latitude: apiStore.latitude,
    longitude: apiStore.longitude,
    deliveryServices: apiStore.delivery_services,
    createdAt: new Date(apiStore.created_at * 1000),
    updatedAt: new Date(apiStore.updated_at * 1000),
    expiryDate: new Date(apiStore.expiry_date * 1000),
    extraData: apiStore.extra_data,
    sort: apiStore.sort,
  }
}
