import { z } from 'zod'

// Item Category schema based on API response
const itemCategorySchema = z.object({
  id: z.string(),
  item_type_id: z.string(),
  item_type_name: z.string(),
  item_type_parent_id: z.string().nullable(),
  item_type_color: z.string().nullable(),
  list_order: z.number().nullable(),
  is_material: z.number().nullable(),
  print_name_menu: z.string().nullable(),
  image_path: z.string().nullable(),
  description: z.string().nullable(),
  sort: z.number(),
  sort_online: z.number(),
  extra_data: z.any().nullable(),
  active: z.number(),
  revision: z.number(),
  store_uid: z.string().nullable(),
  brand_uid: z.string(),
  company_uid: z.string(),
  is_fabi: z.number(),
  created_by: z.string().nullable(),
  updated_by: z.string().nullable(),
  deleted_by: z.string().nullable(),
  created_at: z.number(),
  updated_at: z.number(),
  deleted_at: z.number().nullable(),
  deleted: z.boolean(),
  list_item: z.array(z.string()).optional()
})

// API response schema
const itemCategoriesApiResponseSchema = z.object({
  data: z.array(itemCategorySchema),
  track_id: z.string()
})

// Export types
export type ItemCategory = z.infer<typeof itemCategorySchema>
export type ItemCategoriesApiResponse = z.infer<typeof itemCategoriesApiResponseSchema>

// Export schemas
export { itemCategorySchema, itemCategoriesApiResponseSchema }

// API data interface (raw from API)
export interface ItemCategoryApiData {
  id: string
  item_type_id: string
  item_type_name: string
  item_type_parent_id: string | null
  item_type_color: string | null
  list_order: number | null
  is_material: number | null
  print_name_menu: string | null
  image_path: string | null
  description: string | null
  sort: number
  sort_online: number
  extra_data: Record<string, unknown> | null
  active: number
  revision: number
  store_uid: string | null
  brand_uid: string
  company_uid: string
  is_fabi: number
  created_by: string | null
  updated_by: string | null
  deleted_by: string | null
  created_at: number
  updated_at: number
  deleted_at: number | null
  deleted: boolean
  list_item?: string[]
}

// Conversion function from API data to ItemCategory
export const convertApiItemCategoryToItemCategory = (
  apiItemCategory: ItemCategoryApiData
): ItemCategory => {
  return {
    ...apiItemCategory
  }
}
