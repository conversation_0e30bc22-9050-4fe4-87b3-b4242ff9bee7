// Discount API data structure
export interface DiscountApiData {
  id: string
  created_at: number
  created_by: string
  updated_at: number
  updated_by: string | null
  deleted: boolean
  deleted_at: number | null
  deleted_by: string | null
  ta_discount: number
  ots_discount: number
  is_all: number
  is_type: number
  is_item: number
  type_id: string
  item_id: string
  discount_type: string
  from_date: number
  to_date: number
  time_sale_hour_day: number
  time_sale_date_week: number
  description: string | null
  extra_data: {
    combo_id: string
    is_combo: number
  }
  active: number
  revision: number | null
  promotion_uid: string
  brand_uid: string
  company_uid: string
  sort: number
  store_uid: string
  discount_clone_id: string | null
  source_uid: string
  promotion: {
    id: string
    sort: number
    active: number
    deleted: boolean
    is_fabi: number
    revision: number
    brand_uid: string
    store_uid: string
    created_at: number
    created_by: string
    deleted_at: number | null
    deleted_by: string | null
    extra_data: Record<string, unknown>
    source_uid: string
    updated_at: number
    updated_by: string
    company_uid: string
    description: string | null
    promotion_id: string
    promotion_name: string
    partner_auto_gen: number
  }
  promotion_id: string
  partner_auto_gen: number
  store_name: string
  source: {
    id: string
    sort: number
    is_fb: number
    active: number
    deleted: boolean
    is_fabi: number
    revision: number | null
    brand_uid: string
    source_id: string
    store_uid: string
    created_at: number
    created_by: string
    deleted_at: number | null
    deleted_by: string | null
    extra_data: Record<string, unknown>
    updated_at: number
    updated_by: string
    company_uid: string
    description: string | null
    source_name: string
    source_type: string[]
    partner_config: number
  }
}

// Converted discount data structure
export interface Discount {
  id: string
  taDiscount: number
  otsDiscount: number
  isAll: number
  isType: number
  isItem: number
  typeId: string
  itemId: string
  discountType: string
  fromDate: number
  toDate: number
  active: number
  storeUid: string
  storeName: string
  sourceUid: string
  promotionUid: string
  promotionId: string
  promotionName: string
  partnerAutoGen: number
  source: {
    id: string
    sourceId: string
    sourceName: string
    sourceType: string[]
    active: number
  }
  createdAt: number
  createdBy: string
  updatedAt: number
  updatedBy: string | null
  deleted?: boolean
  deletedAt?: number | null
  deletedBy?: string | null
}

// API response structure
export interface DiscountsApiResponse {
  data: DiscountApiData[]
  track_id: string
}

// Parameters for fetching discounts
export interface GetDiscountsParams {
  companyUid?: string
  brandUid?: string
  page?: number
  listStoreUid?: string[]
  promotionPartnerAutoGen?: number
  status?: 'expired' | 'unexpired'
  active?: number
  searchTerm?: string
}

// Convert API data to internal format
export function convertApiDiscountToDiscount(apiDiscount: DiscountApiData): Discount {
  return {
    id: apiDiscount.id,
    taDiscount: apiDiscount.ta_discount,
    otsDiscount: apiDiscount.ots_discount,
    isAll: apiDiscount.is_all,
    isType: apiDiscount.is_type,
    isItem: apiDiscount.is_item,
    typeId: apiDiscount.type_id,
    itemId: apiDiscount.item_id,
    discountType: apiDiscount.discount_type,
    fromDate: apiDiscount.from_date,
    toDate: apiDiscount.to_date,
    active: apiDiscount.active,
    storeUid: apiDiscount.store_uid,
    storeName: apiDiscount.store_name,
    sourceUid: apiDiscount.source_uid,
    promotionUid: apiDiscount.promotion_uid,
    promotionId: apiDiscount.promotion_id,
    promotionName: apiDiscount.promotion?.promotion_name || '',
    partnerAutoGen: apiDiscount.partner_auto_gen,
    source: {
      id: apiDiscount.source.id,
      sourceId: apiDiscount.source.source_id,
      sourceName: apiDiscount.source.source_name,
      sourceType: apiDiscount.source.source_type,
      active: apiDiscount.source.active
    },
    createdAt: apiDiscount.created_at,
    createdBy: apiDiscount.created_by,
    updatedAt: apiDiscount.updated_at,
    updatedBy: apiDiscount.updated_by,
    deleted: apiDiscount.deleted,
    deletedAt: apiDiscount.deleted_at,
    deletedBy: apiDiscount.deleted_by
  }
}
