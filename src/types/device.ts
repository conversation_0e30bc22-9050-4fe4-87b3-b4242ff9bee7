import { z } from 'zod'

const deviceStatusSchema = z.union([
  z.literal('active'),
  z.literal('inactive'),
  z.literal('maintenance'),
  z.literal('error'),
])
export type DeviceStatus = z.infer<typeof deviceStatusSchema>

const deviceTypeSchema = z.union([
  z.literal('POS'),
  z.literal('POS_MINI'),
  z.literal('PDA'),
])
export type DeviceType = z.infer<typeof deviceTypeSchema>

const deviceSchema = z.object({
  id: z.string(),
  name: z.string(),
  type: deviceTypeSchema,
  version: z.string(),
  storeId: z.string(),
  storeName: z.string(),
  status: deviceStatusSchema,
  lastUpdate: z.coerce.date(),
  serialNumber: z.string(),
  manufacturer: z.string(),
  model: z.string(),
  ipAddress: z.string().optional(),
  macAddress: z.string().optional(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
  isActive: z.boolean(),
  device_code: z.string().optional(),
})
export type Device = z.infer<typeof deviceSchema>

export const deviceListSchema = z.array(deviceSchema)

// Device type options for UI components
export const deviceTypes = [
  { value: 'POS', label: 'POS' },
  { value: 'POS_MINI', label: 'POS MINI' },
  { value: 'PDA', label: 'PDA' },
]

export const deviceStatuses = [
  { value: 'active', label: 'Hoạt động' },
  { value: 'inactive', label: 'Không hoạt động' },
  { value: 'maintenance', label: 'Bảo trì' },
  { value: 'error', label: 'Lỗi' },
]
