import type { ApiPaymentMethod } from '@/lib/payment-methods-api'

// Frontend Payment Method interface (converted from API response)
export interface PaymentMethod {
  id: string
  code: string // payment_method_id
  name: string // payment_method_name
  cardProcessingFee: number // payment_fee_extra as percentage
  feeType: number // payment_fee_type
  description: string
  imagePath: string
  isActive: boolean // active === 1
  storeCount: number // stores
  storeUids: string[] // list_payment_method_uid
  createdAt: Date
  updatedAt: Date
  createdBy: string
  updatedBy: string
  brandUid: string
  companyUid: string
  sort: number
  extraData?: {
    requireTraceno?: boolean
  }
}

// Form data for creating/updating payment methods
export interface PaymentMethodFormData {
  name: string
  code?: string
  cardProcessingFee: number
  description?: string
  imagePath?: string
  isActive: boolean
  storeUids: string[]
  requireTraceno?: boolean
}

// Filter options for payment methods list
export interface PaymentMethodFilters {
  searchTerm?: string
  storeUid?: string
  isActive?: boolean
}

// Utility function to convert API response to frontend interface
export const convertApiPaymentMethodToPaymentMethod = (
  apiPaymentMethod: ApiPaymentMethod
): PaymentMethod => {
  return {
    id: apiPaymentMethod.id,
    code: apiPaymentMethod.payment_method_id,
    name: apiPaymentMethod.payment_method_name,
    cardProcessingFee: apiPaymentMethod.payment_fee_extra,
    feeType: apiPaymentMethod.payment_fee_type,
    description: apiPaymentMethod.description,
    imagePath: apiPaymentMethod.image_path,
    isActive: apiPaymentMethod.active === 1,
    storeCount: apiPaymentMethod.stores,
    storeUids: apiPaymentMethod.list_payment_method_uid || [],
    createdAt: new Date(apiPaymentMethod.created_at * 1000),
    updatedAt: new Date(apiPaymentMethod.updated_at * 1000),
    createdBy: apiPaymentMethod.created_by,
    updatedBy: apiPaymentMethod.updated_by,
    brandUid: apiPaymentMethod.brand_uid,
    companyUid: apiPaymentMethod.company_uid,
    sort: apiPaymentMethod.sort,
    extraData: apiPaymentMethod.extra_data
      ? {
          requireTraceno: apiPaymentMethod.extra_data.require_traceno === 1,
        }
      : undefined,
  }
}

// Utility function to convert form data to API request
export const convertPaymentMethodFormToApiRequest = (
  formData: PaymentMethodFormData,
  companyUid: string,
  brandUid: string,
  storeUid: string
) => {
  return {
    payment_method_name: formData.name,
    payment_method_id: formData.code || `PAYMENT_METHOD-${Date.now()}`,
    payment_fee_extra: formData.cardProcessingFee,
    payment_fee_type: 0, // Default to percentage
    description: formData.description || '',
    image_path: formData.imagePath || '',
    active: formData.isActive ? 1 : 0,
    sort: 20, // Default sort value
    company_uid: companyUid,
    brand_uid: brandUid,
    store_uid: storeUid,
    extra_data: formData.requireTraceno
      ? { require_traceno: 1 }
      : null,
  }
}

export type { ApiPaymentMethod }
