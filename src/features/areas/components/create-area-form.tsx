import { useState, useMemo, useEffect, useRef } from 'react'

import { useNavigate } from '@tanstack/react-router'

import { X } from 'lucide-react'

import { areasApi } from '@/lib/areas-api'

import { useCreateArea, useUpdateArea, useAreaDetail } from '@/hooks/api/use-areas'

import {
  Button,
  Input,
  Label,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui'

interface Store {
  id: string
  store_name: string
  active: number
}

interface CreateAreaFormProps {
  areaId?: string
  storeUid?: string
}

export function CreateAreaForm({ areaId, storeUid: initialStoreUid }: CreateAreaFormProps = {}) {
  const navigate = useNavigate()
  const { createArea, isCreating } = useCreateArea()
  const { updateArea, isUpdating } = useUpdateArea()
  const fileInputRef = useRef<HTMLInputElement>(null)

  const isEditMode = !!areaId && !!initialStoreUid

  const {
    data: areaData,
    isLoading: isLoadingArea
  } = useAreaDetail(areaId || '', initialStoreUid || '')

  const stores = useMemo(() => {
    try {
      const posStoresData = localStorage.getItem('pos_stores_data')
      if (posStoresData) {
        const storesData = JSON.parse(posStoresData)
        return Array.isArray(storesData)
          ? storesData.filter((store: Store) => store.active === 1)
          : []
      }
      return []
    } catch (error) {
      return []
    }
  }, [])

  const [formData, setFormData] = useState({
    areaName: '',
    storeUid: initialStoreUid || stores[0]?.id || '',
    description: '',
    sort: '',
    backgroundFile: null as File | null,
    backgroundPreview: '' as string
  })

  // Update form data when area data is loaded (edit mode)
  useEffect(() => {
    if (areaData && isEditMode && stores.length > 0) {
      // Find matching store in stores list
      const matchingStore = stores.find(store => store.id === areaData.store_uid)

      const newFormData = {
        areaName: areaData.area_name || '',
        storeUid: matchingStore ? matchingStore.id : stores[0]?.id || '',
        description: areaData.description || '',
        sort: areaData.sort?.toString() || '',
        backgroundFile: null,
        backgroundPreview: areaData.extra_data?.background || ''
      }
      setFormData(newFormData)
    }
  }, [areaData, isEditMode, stores])

  const handleBack = () => {
    navigate({ to: '/setting/area' })
  }

  const handleSave = async () => {
    if (!isFormValid) return

    if (isEditMode && areaData) {
      // Update existing area
      let backgroundUrl = areaData.extra_data?.background || ''

      // Use backgroundPreview as the URL (either existing or newly uploaded)
      backgroundUrl = formData.backgroundPreview || areaData.extra_data?.background || ''

      const updatedAreaData = {
        ...areaData, // Keep all original fields
        // Override with form data
        area_name: formData.areaName,
        description: formData.description || undefined,
        sort: formData.sort ? parseInt(formData.sort) : areaData.sort,
        store_uid: formData.storeUid,
        extra_data: {
          ...areaData.extra_data,
          background: backgroundUrl
        }
      }

      updateArea(updatedAreaData, {
        onSuccess: () => {
          navigate({ to: '/setting/area' })
        }
      })
    } else {
      // Create new area
      const newAreaData = {
        area_name: formData.areaName,
        description: formData.description || undefined,
        store_uid: formData.storeUid,
        sort: formData.sort ? parseInt(formData.sort) : undefined,
        backgroundFile: formData.backgroundFile
      }

      createArea(newAreaData, {
        onSuccess: () => {
          navigate({ to: '/setting/area' })
        }
      })
    }
  }

  const isFormValid = formData.areaName.trim() !== '' && formData.storeUid !== ''
  const isLoading = isCreating || isUpdating || isLoadingArea

  const handleBackgroundUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]

    if (file) {
      // Show local preview first for immediate feedback
      const reader = new FileReader()
      reader.onload = event => {
        const localPreview = event.target?.result as string

        setFormData(prev => ({
          ...prev,
          backgroundFile: file,
          backgroundPreview: localPreview
        }))
      }
      reader.readAsDataURL(file)

      // Upload file to server immediately and get URL
      try {
        const uploadResponse = await areasApi.uploadImage(file)
        const serverImageUrl = uploadResponse.data.image_url

        // Update preview with server URL and clear backgroundFile since it's uploaded
        setFormData(prev => ({
          ...prev,
          backgroundFile: null, // Clear file since it's uploaded
          backgroundPreview: serverImageUrl
        }))
      } catch (error) {
        // Keep local preview if upload fails
      }
    }
  }

  const handleImageClick = () => {
    // Trigger file input click using ref
    if (fileInputRef.current) {
      fileInputRef.current.click()
    }
  }

  return (
    <div className='container mx-auto px-4 py-8'>
      {/* Header */}
      <div className='mb-8'>
        <div className='mb-4 flex items-center justify-between'>
          <Button variant='ghost' size='sm' onClick={handleBack} className='flex items-center'>
            <X className='h-4 w-4' />
          </Button>
          <Button
            type='button'
            disabled={isLoading || !isFormValid}
            className='min-w-[100px]'
            onClick={handleSave}
          >
            {isLoading
              ? isEditMode
                ? 'Đang cập nhật...'
                : 'Đang tạo...'
              : isEditMode
                ? 'Cập nhật'
                : 'Lưu'}
          </Button>
        </div>

        <div className='text-center'>
          <h1 className='mb-2 text-3xl font-bold'>
            {isEditMode ? 'Chỉnh sửa khu vực' : 'Tạo khu vực'}
          </h1>
        </div>
      </div>

      {/* Form Content */}
      <div className='mx-auto max-w-4xl'>
        {isEditMode && isLoadingArea ? (
          <div className='p-6 text-center'>
            <p className='text-muted-foreground'>Đang tải dữ liệu khu vực...</p>
          </div>
        ) : (
          <div className='p-6'>
            <div className='space-y-6'>
              {/* Tên khu vực */}
              <div className='flex items-center gap-4'>
                <Label htmlFor='area-name' className='min-w-[200px] text-sm font-medium'>
                  Tên khu vực <span className='text-red-500'>*</span>
                </Label>
                <Input
                  id='area-name'
                  value={formData.areaName}
                  onChange={e => setFormData({ ...formData, areaName: e.target.value })}
                  placeholder='Nhập tên khu vực'
                  className='flex-1'
                />
              </div>

              {/* Cửa hàng */}
              <div className='flex items-center gap-4'>
                <Label className='min-w-[200px] text-sm font-medium'>
                  Cửa hàng <span className='text-red-500'>*</span>
                </Label>
                <Select
                  value={formData.storeUid}
                  onValueChange={value => setFormData({ ...formData, storeUid: value })}
                  disabled={isEditMode}
                >
                  <SelectTrigger className='flex-1'>
                    <SelectValue placeholder='Chọn cửa hàng' />
                  </SelectTrigger>
                  <SelectContent>
                    {stores.map(store => (
                      <SelectItem key={store.id} value={store.id}>
                        {store.store_name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Mô tả */}
              <div className='flex items-center gap-4'>
                <Label htmlFor='area-description' className='min-w-[200px] text-sm font-medium'>
                  Mô tả
                </Label>
                <Input
                  id='area-description'
                  value={formData.description}
                  onChange={e => setFormData({ ...formData, description: e.target.value })}
                  placeholder='Mô tả'
                  className='flex-1'
                />
              </div>

              {/* Thứ tự hiển thị trong thiết bị bán hàng */}
              <div className='space-y-4'>
                <h2 className='text-lg font-medium text-gray-900'>
                  Thứ tự hiển thị trong thiết bị bán hàng
                </h2>

                <div className='space-y-2'>
                  <div className='text-sm text-gray-600'>
                    Khu vực có số nhỏ hơn sẽ được sắp xếp lên trên trong thiết bị bán hàng
                  </div>
                  <div className='flex items-center gap-4'>
                    <Label htmlFor='area-sort' className='min-w-[200px] text-sm font-medium'>
                      Thứ tự hiển thị
                    </Label>
                    <Input
                      id='area-sort'
                      type='number'
                      value={formData.sort}
                      onChange={e => setFormData({ ...formData, sort: e.target.value })}
                      placeholder='Nhập số thứ tự hiển thị'
                      className='flex-1'
                    />
                  </div>
                </div>
              </div>

              {/* Hình nền khu vực Section */}
              <div className='space-y-4'>
                <h2 className='text-lg font-medium text-gray-900'>Hình nền khu vực</h2>

                {/* Hidden file input - always present */}
                <Input
                  ref={fileInputRef}
                  type='file'
                  accept='image/*'
                  onChange={handleBackgroundUpload}
                  className='hidden'
                />

                <div className='flex-1'>
                  {/* Background Preview */}
                  {formData.backgroundPreview ? (
                    <div>
                      <img
                        src={formData.backgroundPreview}
                        alt='Background preview'
                        className='h-24 w-24 cursor-pointer rounded-md border object-cover transition-opacity hover:opacity-80'
                        onClick={handleImageClick}
                        title='Click để thay đổi ảnh'
                      />
                    </div>
                  ) : (
                    /* Upload Area */
                    <div className='rounded-md border-2 border-dashed border-gray-300 p-6 text-center transition-colors hover:border-gray-400'>
                      <div className='space-y-2'>
                        <div className='mx-auto h-12 w-12 text-gray-400'>
                          <svg fill='none' viewBox='0 0 24 24' stroke='currentColor'>
                            <path
                              strokeLinecap='round'
                              strokeLinejoin='round'
                              strokeWidth={1}
                              d='M12 6v6m0 0v6m0-6h6m-6 0H6'
                            />
                          </svg>
                        </div>
                        <div>
                          <button
                            type='button'
                            onClick={handleImageClick}
                            className='cursor-pointer text-sm font-medium text-blue-600 hover:text-blue-500'
                          >
                            Tải ảnh lên
                          </button>
                        </div>
                        <p className='text-xs text-gray-500'>PNG, JPG, GIF up to 10MB</p>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
