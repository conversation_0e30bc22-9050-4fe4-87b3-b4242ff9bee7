import { useState } from 'react'

import { useNavigate } from '@tanstack/react-router'

import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  useReactTable,
  RowSelectionState
} from '@tanstack/react-table'

import { Trash2 } from 'lucide-react'

import type { Area } from '@/lib/areas-api'

import { useDeleteAreas } from '@/hooks/api/use-areas'

import { ConfirmModal } from '@/components/pos'
import {
  Button,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui'

interface AreasDataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[]
  data: TData[]
  storeUid: string
}

export function AreasDataTable<TData, TValue>({
  columns,
  data,
  storeUid
}: AreasDataTableProps<TData, TValue>) {
  const navigate = useNavigate()
  const [rowSelection, setRowSelection] = useState<RowSelectionState>({})
  const [confirmModalOpen, setConfirmModalOpen] = useState(false)
  const { deleteAreas, isDeleting } = useDeleteAreas()

  const table = useReactTable({
    data,
    columns,
    state: {
      rowSelection
    },
    enableRowSelection: true,
    onRowSelectionChange: setRowSelection,
    getCoreRowModel: getCoreRowModel()
  })

  const selectedRows = table.getFilteredSelectedRowModel().rows
  const selectedCount = selectedRows.length

  const handleDeleteClick = () => {
    setConfirmModalOpen(true)
  }

  const handleConfirmDelete = () => {
    const areaIds = selectedRows.map(row => {
      const area = row.original as Area
      return area.id
    })

    deleteAreas({ areaIds, storeUid })

    // Clear selection after delete
    setRowSelection({})
    setConfirmModalOpen(false)
  }

  const handleCancelDelete = () => {
    setConfirmModalOpen(false)
  }

  const handleRowClick = (area: Area, event: React.MouseEvent) => {
    // Don't navigate if clicking on checkbox, buttons, or other interactive elements
    const target = event.target as HTMLElement
    if (
      target.closest('input[type="checkbox"]') ||
      target.closest('button') ||
      target.closest('[role="button"]')
    ) {
      return
    }

    navigate({
      to: '/setting/area/detail/$areaId',
      params: { areaId: area.id },
      search: { store_uid: storeUid }
    })
  }

  return (
    <div className='space-y-4'>
      {/* Delete Button - Show when rows are selected */}
      {selectedCount > 0 && (
        <div className='bg-muted/50 flex items-center justify-start rounded-md border p-3'>
          <Button
            variant='destructive'
            size='sm'
            onClick={handleDeleteClick}
            disabled={isDeleting}
            className='h-8'
          >
            <Trash2 className='mr-2 h-4 w-4' />
            {isDeleting ? 'Đang xóa...' : 'Xóa khu vực'}
          </Button>
        </div>
      )}

      {/* Data Table */}
      <div className='rounded-md border'>
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map(headerGroup => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map(header => {
                  return (
                    <TableHead key={header.id} style={{ width: header.getSize() }}>
                      {header.isPlaceholder
                        ? null
                        : flexRender(header.column.columnDef.header, header.getContext())}
                    </TableHead>
                  )
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map(row => {
                const area = row.original as Area
                return (
                  <TableRow
                    key={row.id}
                    data-state={row.getIsSelected() && 'selected'}
                    className='cursor-pointer hover:bg-gray-50'
                    onClick={event => handleRowClick(area, event)}
                  >
                    {row.getVisibleCells().map(cell => (
                      <TableCell key={cell.id} style={{ width: cell.column.getSize() }}>
                        {flexRender(cell.column.columnDef.cell, cell.getContext())}
                      </TableCell>
                    ))}
                  </TableRow>
                )
              })
            ) : (
              <TableRow>
                <TableCell colSpan={columns.length} className='h-24 text-center'>
                  <div className='flex flex-col items-center justify-center space-y-2'>
                    <p className='text-muted-foreground'>Không có khu vực nào</p>
                    <p className='text-muted-foreground text-sm'>
                      Hãy tạo khu vực mới để bắt đầu quản lý
                    </p>
                  </div>
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      {/* Confirm Delete Modal */}
      <ConfirmModal
        open={confirmModalOpen}
        onOpenChange={setConfirmModalOpen}
        title='Lưu ý: Xóa khu vực có thể ảnh hưởng đến dữ liệu đang vận hành tại POS'
        content={`Bạn có muốn xoá ${selectedCount} khu vực đã chọn ?`}
        confirmText='Xóa'
        cancelText='Hủy'
        onConfirm={handleConfirmDelete}
        onCancel={handleCancelDelete}
        isLoading={isDeleting}
      />
    </div>
  )
}
