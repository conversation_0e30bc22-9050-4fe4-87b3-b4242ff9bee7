import { useState, useMemo, useEffect } from 'react'

import type { Table } from '@/types'
import { ChevronDown, ChevronRight } from 'lucide-react'

import { useCurrentBrand, useCurrentCompany } from '@/stores/posStore'

import type { Area } from '@/lib/areas-api'

import { useTablesData } from '@/hooks/api/use-tables'

import { Checkbox } from '@/components/ui/checkbox'
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible'
import { Input } from '@/components/ui/input'

import { PosModal } from '@/components/pos'

interface AddTablesModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  area: Area
  storeUid: string
}

export function AddTablesModal({ open, onOpenChange, area, storeUid }: AddTablesModalProps) {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedTableIds, setSelectedTableIds] = useState<string[]>([])
  const [selectedSectionOpen, setSelectedSectionOpen] = useState(true)
  const [remainingSectionOpen, setRemainingSectionOpen] = useState(true)

  // const stores = usePosStores()
  const currentBrand = useCurrentBrand()
  const currentCompany = useCurrentCompany()

  // Get tables data
  const { data: tables = [], isLoading } = useTablesData({
    skip_limit: true,
    company_uid: currentCompany?.companyUid || '',
    brand_uid: currentBrand?.selectedBrand?.id || '',
    store_uid: storeUid,
    enabled: open
  })

  // Initialize selectedTableIds with tables already assigned to current area
  useEffect(() => {
    if (open && tables.length > 0) {
      // Find tables that are already assigned to this area by matching table_id with list_table_id
      const preSelectedTableIds = tables
        .filter(table => area.list_table_id?.includes(table.table_id))
        .map(table => table.id)

      setSelectedTableIds(preSelectedTableIds)
    }
  }, [open, tables, area.list_table_id, area.area_name])

  // Filter tables based on search term
  const filteredTables = useMemo(() => {
    if (!searchTerm) return tables
    return tables.filter(table => table.table_name.toLowerCase().includes(searchTerm.toLowerCase()))
  }, [tables, searchTerm])

  // Split tables into selected and remaining based on user selection
  const { selectedTables, remainingTables } = useMemo(() => {
    // Selected tables (should show in "Đã chọn" section)
    const selected = filteredTables.filter(table => selectedTableIds.includes(table.id))

    // Remaining tables (should show in "Còn lại" section)
    const remaining = filteredTables.filter(table => !selectedTableIds.includes(table.id))

    return { selectedTables: selected, remainingTables: remaining }
  }, [filteredTables, selectedTableIds])

  const handleTableToggle = (tableId: string) => {
    setSelectedTableIds(prev =>
      prev.includes(tableId) ? prev.filter(id => id !== tableId) : [...prev, tableId]
    )
  }

  const handleCancel = () => {
    setSearchTerm('')
    onOpenChange(false)
    // Reset selectedTableIds will happen when modal reopens via useEffect
  }

  const handleConfirm = () => {
    // TODO: Implement API call to assign tables to area
    handleCancel()
  }

  const renderTableItem = (table: Table) => (
    <div key={table.id} className='flex items-center justify-between py-2'>
      <div className='flex items-center gap-3'>
        <Checkbox
          checked={selectedTableIds.includes(table.id)}
          onCheckedChange={() => handleTableToggle(table.id)}
        />
        <span className='text-sm'>{table.table_name}</span>
      </div>
      <span className='text-sm text-gray-500'>{table.area?.area_name || 'Chưa có khu vực'}</span>
    </div>
  )

  return (
    <PosModal
      title={`Thêm bàn vào khu vực ${area.area_name}`}
      open={open}
      onOpenChange={onOpenChange}
      onCancel={handleCancel}
      onConfirm={handleConfirm}
      confirmText='Xác nhận'
      cancelText='Hủy'
      maxWidth='sm:max-w-2xl'
      confirmDisabled={selectedTableIds.length === 0}
    >
      <div className='space-y-4'>
        {/* Description */}
        <p className='text-sm text-gray-600'>
          Bàn được chọn sẽ chuyển sang khu vực {area.area_name}
        </p>

        {/* Area info */}
        <p className='text-sm font-medium'>
          Khu vực {area.area_name} có {area.list_table_id?.length || 0} bàn
        </p>

        {/* Search input */}
        <Input
          placeholder='Tìm kiếm'
          value={searchTerm}
          onChange={e => setSearchTerm(e.target.value)}
          className='w-full'
        />

        {isLoading ? (
          <div className='py-8 text-center text-sm text-gray-500'>Đang tải danh sách bàn...</div>
        ) : (
          <div className='space-y-3'>
            {/* Selected tables section */}
            <Collapsible open={selectedSectionOpen} onOpenChange={setSelectedSectionOpen}>
              <CollapsibleTrigger className='flex w-full items-center justify-between rounded-md border p-3 text-left hover:bg-gray-50'>
                <span className='text-sm font-medium'>Đã chọn {selectedTables.length}</span>
                {selectedSectionOpen ? (
                  <ChevronDown className='h-4 w-4' />
                ) : (
                  <ChevronRight className='h-4 w-4' />
                )}
              </CollapsibleTrigger>
              <CollapsibleContent className='rounded-b-md border-r border-b border-l'>
                <div className='space-y-1 p-3'>
                  {selectedTables.length === 0 ? (
                    <p className='py-2 text-sm text-gray-500'>Chưa chọn bàn nào</p>
                  ) : (
                    selectedTables.map(renderTableItem)
                  )}
                </div>
              </CollapsibleContent>
            </Collapsible>

            {/* Remaining tables section */}
            <Collapsible open={remainingSectionOpen} onOpenChange={setRemainingSectionOpen}>
              <CollapsibleTrigger className='flex w-full items-center justify-between rounded-md border p-3 text-left hover:bg-gray-50'>
                <span className='text-sm font-medium'>Còn lại {remainingTables.length}</span>
                {remainingSectionOpen ? (
                  <ChevronDown className='h-4 w-4' />
                ) : (
                  <ChevronRight className='h-4 w-4' />
                )}
              </CollapsibleTrigger>
              <CollapsibleContent className='rounded-b-md border-r border-b border-l'>
                <div className='space-y-1 p-3'>
                  {remainingTables.length === 0 ? (
                    <p className='py-2 text-sm text-gray-500'>Không có bàn nào</p>
                  ) : (
                    remainingTables.map(renderTableItem)
                  )}
                </div>
              </CollapsibleContent>
            </Collapsible>
          </div>
        )}
      </div>
    </PosModal>
  )
}
