import { useState, useRef } from 'react'
import { toast } from 'sonner'

import { useAreasExcelParser } from './use-areas-excel-parser'
import { useBulkImportAreas } from '@/hooks/api/use-areas'

interface ParsedAreaData {
  area_name: string
  description?: string
  sort?: number
  active?: number
}

export function useAreasImport() {
  const [importSelectedFile, setImportSelectedFile] = useState<File | null>(null)
  const [importParsedData, setImportParsedData] = useState<ParsedAreaData[]>([])
  const [showImportParsedData, setShowImportParsedData] = useState(false)
  const importFileInputRef = useRef<HTMLInputElement>(null)

  const { parseExcelFile, downloadTemplate } = useAreasExcelParser()
  const bulkImportAreasMutation = useBulkImportAreas()

  const resetImportState = () => {
    setShowImportParsedData(false)
    setImportParsedData([])
    setImportSelectedFile(null)
  }

  const handleDownloadTemplate = () => {
    downloadTemplate()
  }

  const handleImportFileUpload = () => {
    importFileInputRef.current?.click()
  }

  const handleImportFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file) return

    setImportSelectedFile(file)

    try {
      const parsedAreas = await parseExcelFile(file)
      setImportParsedData(parsedAreas)
      setShowImportParsedData(true)
      toast.success(`Đã phân tích ${parsedAreas.length} khu vực từ file!`)
    } catch {
      // Error handling is done in parseExcelFile
    }
  }

  const handleSaveImportedAreas = async (storeUid: string) => {
    if (importParsedData.length === 0) {
      toast.error('Không có dữ liệu để lưu')
      return false
    }

    try {
      await bulkImportAreasMutation.mutateAsync({
        storeUid,
        areas: importParsedData
      })
      toast.success(`Đã tạo thành công ${importParsedData.length} khu vực!`)
      resetImportState()
      return true
    } catch {
      toast.error('Lỗi khi tạo khu vực. Vui lòng thử lại.')
      return false
    }
  }

  return {
    importSelectedFile,
    importParsedData,
    showImportParsedData,
    importFileInputRef,
    resetImportState,
    handleDownloadTemplate,
    handleImportFileUpload,
    handleImportFileChange,
    handleSaveImportedAreas,
    isLoading: bulkImportAreasMutation.isPending,
  }
}
