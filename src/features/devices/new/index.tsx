import { useState } from 'react'
import { z } from 'zod'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { useNavigate } from '@tanstack/react-router'
import { X } from 'lucide-react'
import { toast } from 'sonner'
import { getErrorMessage } from '@/utils/error-utils'
import { useStoresData, useCreateDevice } from '@/hooks/api'
import {
  Button,
  Input,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui'

// Device type constants
const DEVICE_TYPES = [
  { value: 'POS', label: 'POS' },
  { value: 'POS_MINI', label: 'POS MINI' },
  { value: 'PDA', label: 'PDA' },
  { value: 'KDS', label: 'KDS' },
  { value: 'KDS_ORDER_CONTROL', label: 'KDS ORDER CONTROL' },
  { value: 'KDS_MAKER', label: 'KDS MAKER' },
  { value: 'SELF_ORDER', label: 'SELF ORDER' },
] as const

// Form validation schema
const addDeviceSchema = z.object({
  deviceName: z.string().min(1, 'Tên thiết bị là bắt buộc'),
  storeId: z.string().min(1, 'Điểm bán hàng là bắt buộc'),
  deviceType: z.string().min(1, 'Loại thiết bị là bắt buộc'),
})

type AddDeviceFormData = z.infer<typeof addDeviceSchema>



export function AddDevicePage() {
  const navigate = useNavigate()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [deviceCode, setDeviceCode] = useState<string | null>(null)
  const [createdDevice, setCreatedDevice] = useState<unknown>(null)

  const { data: storesData, isLoading: storesLoading } = useStoresData()

  const createDeviceMutation = useCreateDevice()

  const form = useForm<AddDeviceFormData>({
    resolver: zodResolver(addDeviceSchema),
    defaultValues: {
      deviceName: '',
      storeId: '',
      deviceType: 'POS', // Default to POS
    },
  })

  const handleSubmit = async (data: AddDeviceFormData) => {
    setIsSubmitting(true)
    try {
      // Find the selected store to get its brand_uid
      const selectedStore = storesData?.find(
        (store) => store.id === data.storeId
      )
      if (!selectedStore) {
        throw new Error('Store not found')
      }

      const response = await createDeviceMutation.mutateAsync({
        deviceName: data.deviceName,
        storeId: data.storeId,
        deviceType: data.deviceType,
        brandUid: selectedStore.brandId,
      })

      // Extract device_code from response and show it
      if (response && 'device_code' in response) {
        setDeviceCode(response.device_code as string)
        setCreatedDevice(response)
        toast.success(`Thiết bị "${data.deviceName}" đã được tạo thành công!`)
      } else {
        toast.success(`Thiết bị "${data.deviceName}" đã được tạo thành công!`)
        navigate({ to: '/devices/list' })
      }
    } catch (error) {
      const errorMessage = getErrorMessage(error)
      toast.error(errorMessage)
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleBack = () => {
    navigate({ to: '/devices/list' })
  }

  const handleConfigureDevice = () => {
    if (
      createdDevice &&
      typeof createdDevice === 'object' &&
      'id' in createdDevice &&
      'storeId' in createdDevice
    ) {
      // Navigate to device detail page with device ID and store UID
      window.location.href = `https://fabi.ipos.vn/device/detail/${(createdDevice as { id: string }).id}?store_uid=${(createdDevice as { storeId: string }).storeId}`
    } else {
      // Fallback to devices list if data is missing
      navigate({ to: '/devices/list' })
    }
  }

  return (
    <div className='container mx-auto px-4 py-8'>
      {/* Header Section */}
      <div className='mb-8'>
        <div className='mb-4 flex items-center justify-between'>
          <Button
            variant='ghost'
            size='sm'
            onClick={handleBack}
            className='flex items-center'
          >
            <X className='h-4 w-4' />
          </Button>
          <Button
            type={deviceCode ? 'button' : 'submit'}
            disabled={isSubmitting}
            className='min-w-[100px]'
            onClick={
              deviceCode
                ? handleConfigureDevice
                : form.handleSubmit(handleSubmit)
            }
          >
            {deviceCode
              ? 'Cấu hình thiết bị'
              : isSubmitting
                ? 'Đang tạo...'
                : 'Lưu'}
          </Button>
        </div>

        <div className='text-center'>
          <h1 className='mb-2 text-3xl font-bold'>Tạo thiết bị</h1>
          <div className='text-muted-foreground text-sm'>
            Cài đặt thiết bị &gt; Xuất mã thiết bị
          </div>
        </div>
      </div>

      {/* Form Section */}
      <div className='mx-auto max-w-4xl'>
        <div className='p-6'>
          {deviceCode ? (
            <div className='text-center'>
              <p className='mb-4 text-gray-600'>
                Sử dụng mã thiết bị này nhập vào thiết bị của bạn để có thể khởi
                chạy ứng dụng!
              </p>
              <div className='mx-auto max-w-md rounded-lg border-2 border-blue-200 bg-blue-50 p-6'>
                <div className='text-center'>
                  <div className='rounded bg-white p-4 font-mono text-2xl font-bold text-blue-600 shadow-sm'>
                    {deviceCode}
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <>
              <h2 className='mb-6 text-xl font-semibold'>Chi tiết</h2>
              <Form {...form}>
                <form
                  onSubmit={form.handleSubmit(handleSubmit)}
                  className='space-y-6'
                >
                  {/* Device Name Field */}
                  <FormField
                    control={form.control}
                    name='deviceName'
                    render={({ field }) => (
                      <FormItem>
                        <div className='flex items-center gap-4'>
                          <FormLabel className='w-40 flex-shrink-0 text-sm font-medium'>
                            Tên thiết bị <span className='text-red-500'>*</span>
                          </FormLabel>
                          <div className='flex-1'>
                            <FormControl>
                              <Input
                                placeholder='Nhập tên thiết bị'
                                {...field}
                                className='w-full'
                              />
                            </FormControl>
                            <FormMessage />
                          </div>
                        </div>
                      </FormItem>
                    )}
                  />

                  {/* Store Selection Field */}
                  <FormField
                    control={form.control}
                    name='storeId'
                    render={({ field }) => (
                      <FormItem>
                        <div className='flex items-center gap-4'>
                          <FormLabel className='w-40 flex-shrink-0 text-sm font-medium'>
                            Điểm bán hàng{' '}
                            <span className='text-red-500'>*</span>
                          </FormLabel>
                          <div className='flex-1'>
                            <Select
                              onValueChange={field.onChange}
                              value={field.value}
                              disabled={storesLoading}
                            >
                              <FormControl>
                                <SelectTrigger className='w-full'>
                                  <SelectValue placeholder='Chọn điểm áp dụng' />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                {storesData
                                  ?.filter((store) => {
                                    return store.isActive
                                  })
                                  ?.map((store) => (
                                    <SelectItem key={store.id} value={store.id}>
                                      {store.name}
                                    </SelectItem>
                                  ))}
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </div>
                        </div>
                      </FormItem>
                    )}
                  />

                  {/* Device Type Field */}
                  <FormField
                    control={form.control}
                    name='deviceType'
                    render={({ field }) => (
                      <FormItem>
                        <div className='flex items-center gap-4'>
                          <FormLabel className='w-40 flex-shrink-0 text-sm font-medium'>
                            Loại thiết bị{' '}
                            <span className='text-red-500'>*</span>
                          </FormLabel>
                          <div className='flex-1'>
                            <Select
                              onValueChange={field.onChange}
                              value={field.value}
                            >
                              <FormControl>
                                <SelectTrigger className='w-full'>
                                  <SelectValue placeholder='Chọn loại thiết bị' />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                {DEVICE_TYPES.map((type) => (
                                  <SelectItem
                                    key={type.value}
                                    value={type.value}
                                  >
                                    {type.label}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </div>
                        </div>
                      </FormItem>
                    )}
                  />
                </form>
              </Form>
            </>
          )}
        </div>
      </div>
    </div>
  )
}
