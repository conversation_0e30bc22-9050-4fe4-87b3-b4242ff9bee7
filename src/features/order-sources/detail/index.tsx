import { useParams } from '@tanstack/react-router'

import { OrderSourceDetailForm } from '../components/order-source-detail-form'

export default function OrderSourceDetailPage() {
  // Try to get params from the parameterized route, but handle gracefully if not available
  let sourceId: string | undefined
  try {
    const params = useParams({
      from: '/_authenticated/setting/source/detail/$sourceId'
    })
    sourceId = params?.sourceId
  } catch {
    // If we're on the non-parameterized route, sourceId will be undefined
    sourceId = undefined
  }

  return <OrderSourceDetailForm sourceId={sourceId} />
}
