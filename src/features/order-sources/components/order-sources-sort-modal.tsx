import { useMemo, useState } from 'react'

import type { OrderSource } from '@/types/api/order-sources'

import { useOrderSourcesForSort, useUpdateOrderSourcesSort } from '@/hooks/api/use-order-sources'

import { Skeleton } from '@/components/ui/skeleton'

import { PosModal } from '@/components/pos'

interface OrderSourcesSortModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function OrderSourcesSortModal({ open, onOpenChange }: OrderSourcesSortModalProps) {
  const { data: orderSourcesResponse, isLoading, error } = useOrderSourcesForSort()
  const { updateSort, isUpdating } = useUpdateOrderSourcesSort()

  // Filter and sort order sources
  const initialOrderSources = useMemo(() => {
    if (!orderSourcesResponse?.data) return []

    const defaultSources = ['ZALO', 'FACEBOOK', 'CRM']

    return orderSourcesResponse.data
      .filter(
        (source: OrderSource) =>
          source.deleted_at === null && !defaultSources.includes(source.source_name)
      )
      .sort((a: OrderSource, b: OrderSource) => a.sort - b.sort)
  }, [orderSourcesResponse?.data])

  // Local state for drag & drop
  const [sortedOrderSources, setSortedOrderSources] = useState<OrderSource[]>([])

  // Update local state when data changes
  useMemo(() => {
    setSortedOrderSources(initialOrderSources)
  }, [initialOrderSources])

  // Drag & Drop handlers
  const handleDragStart = (e: React.DragEvent, index: number) => {
    e.dataTransfer.setData('text/plain', index.toString())
  }

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
  }

  const handleDrop = (e: React.DragEvent, dropIndex: number) => {
    e.preventDefault()
    const dragIndex = parseInt(e.dataTransfer.getData('text/plain'))

    if (dragIndex === dropIndex) return

    const newSources = [...sortedOrderSources]
    const draggedItem = newSources[dragIndex]

    // Remove dragged item
    newSources.splice(dragIndex, 1)
    // Insert at new position
    newSources.splice(dropIndex, 0, draggedItem)

    setSortedOrderSources(newSources)
  }

  const handleSave = () => {
    const sortData = sortedOrderSources.map((source, index) => ({
      source_id: source.source_id,
      sort: index
    }))

    updateSort(sortData, {
      onSuccess: () => {
        onOpenChange(false)
      }
    })
  }

  const handleCancel = () => {
    onOpenChange(false)
  }

  return (
    <PosModal
      open={open}
      onOpenChange={onOpenChange}
      title='Sắp xếp nguồn hàng'
      onCancel={handleCancel}
      onConfirm={handleSave}
      confirmText='Lưu'
      hideButtons={false}
      confirmDisabled={isLoading || isUpdating}
      isLoading={isUpdating}
      disableCancelButton={true}
      maxWidth='sm:max-w-4xl'
    >
      <div className='mb-4'>
        <p className='text-muted-foreground text-sm'>
          Thứ tự hiển thị các nguồn đơn sẽ được áp dụng tại thiết bị bán hàng
        </p>
      </div>

      <div>
        {isLoading ? (
          <div className='grid grid-cols-4 gap-3'>
            {Array.from({ length: 8 }).map((_, index) => (
              <div key={index} className='aspect-square bg-slate-300 p-2'>
                <Skeleton className='h-4 w-full' />
              </div>
            ))}
          </div>
        ) : error ? (
          <div className='py-8 text-center'>
            <p className='text-sm text-red-600'>Có lỗi xảy ra khi tải danh sách nguồn đơn hàng</p>
          </div>
        ) : sortedOrderSources.length === 0 ? (
          <div className='py-8 text-center'>
            <p className='text-muted-foreground text-sm'>Không có nguồn đơn hàng nào để sắp xếp</p>
          </div>
        ) : (
          <div className='grid grid-cols-4 gap-3'>
            {sortedOrderSources.map((source: OrderSource, index: number) => (
              <div
                key={source.id}
                draggable
                onDragStart={e => handleDragStart(e, index)}
                onDragOver={handleDragOver}
                onDrop={e => handleDrop(e, index)}
                className='flex aspect-square cursor-move items-center justify-center bg-slate-300 p-2 transition-colors select-none hover:bg-slate-400'
              >
                <div className='text-center text-sm font-medium'>{source.source_name}</div>
              </div>
            ))}
          </div>
        )}
      </div>
    </PosModal>
  )
}
