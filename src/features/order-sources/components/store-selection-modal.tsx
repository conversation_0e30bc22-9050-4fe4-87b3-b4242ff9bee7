import { useState, useMemo } from 'react'

import { ChevronDown, ChevronRight } from 'lucide-react'

import { Button } from '@/components/ui/button'
import { Checkbox } from '@/components/ui/checkbox'
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible'
import { <PERSON><PERSON>, DialogContent, DialogFooter } from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'

interface Store {
  id: string
  store_name: string
  active: number
}

interface StoreSelectionModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  selectedStoreIds: string[]
  onStoreSelectionChange: (storeIds: string[]) => void
}

export function StoreSelectionModal({
  open,
  onOpenChange,
  selectedStoreIds,
  onStoreSelectionChange
}: StoreSelectionModalProps) {
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedCollapsed, setSelectedCollapsed] = useState(false)
  const [remainingCollapsed, setRemainingCollapsed] = useState(false)

  // Debug logging
  console.log('StoreSelectionModal - selectedStoreIds:', selectedStoreIds)
  console.log('StoreSelectionModal - open:', open)

  // Get stores from localStorage
  const stores = useMemo(() => {
    try {
      const posStoresData = localStorage.getItem('pos_stores_data')
      if (posStoresData) {
        const storesData = JSON.parse(posStoresData)
        return Array.isArray(storesData)
          ? storesData.filter((store: Store) => store.active === 1)
          : []
      }
      return []
    } catch (error) {
      console.error('Error parsing pos_stores_data:', error)
      return []
    }
  }, [])

  // Filter stores based on search query
  const filteredStores = useMemo(() => {
    return stores.filter((store: Store) =>
      store.store_name.toLowerCase().includes(searchQuery.toLowerCase())
    )
  }, [stores, searchQuery])

  // Separate selected and remaining stores
  const selectedStores = filteredStores.filter((store: Store) =>
    selectedStoreIds.includes(store.id)
  )
  const remainingStores = filteredStores.filter(
    (store: Store) => !selectedStoreIds.includes(store.id)
  )

  const handleStoreToggle = (storeId: string) => {
    const newSelectedIds = selectedStoreIds.includes(storeId)
      ? selectedStoreIds.filter(id => id !== storeId)
      : [...selectedStoreIds, storeId]

    onStoreSelectionChange(newSelectedIds)
  }

  const handleSave = () => {
    onOpenChange(false)
  }

  const handleCancel = () => {
    onOpenChange(false)
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className='max-w-md'>
        <div className='space-y-4'>
          {/* Search Input */}
          <div>
            <Input
              placeholder='Tìm kiếm nhà hàng'
              value={searchQuery}
              onChange={e => setSearchQuery(e.target.value)}
              className='w-full'
            />
          </div>

          {/* Selected Stores Collapse */}
          <Collapsible open={!selectedCollapsed} onOpenChange={setSelectedCollapsed}>
            <CollapsibleTrigger asChild>
              <Button
                variant='ghost'
                className='flex w-full items-center justify-between p-2 text-left'
              >
                <span>Đã chọn ({selectedStores.length})</span>
                {selectedCollapsed ? (
                  <ChevronRight className='h-4 w-4' />
                ) : (
                  <ChevronDown className='h-4 w-4' />
                )}
              </Button>
            </CollapsibleTrigger>
            <CollapsibleContent className='space-y-2'>
              {selectedStores.map(store => (
                <div key={store.id} className='flex items-center space-x-2 p-2'>
                  <Checkbox checked={true} onCheckedChange={() => handleStoreToggle(store.id)} />
                  <span className='text-sm'>{store.store_name}</span>
                </div>
              ))}
              {selectedStores.length === 0 && (
                <div className='p-2 text-sm text-gray-500'>Chưa chọn cửa hàng nào</div>
              )}
            </CollapsibleContent>
          </Collapsible>

          {/* Remaining Stores Collapse */}
          <Collapsible open={!remainingCollapsed} onOpenChange={setRemainingCollapsed}>
            <CollapsibleTrigger asChild>
              <Button
                variant='ghost'
                className='flex w-full items-center justify-between p-2 text-left'
              >
                <span>Còn lại ({remainingStores.length})</span>
                {remainingCollapsed ? (
                  <ChevronRight className='h-4 w-4' />
                ) : (
                  <ChevronDown className='h-4 w-4' />
                )}
              </Button>
            </CollapsibleTrigger>
            <CollapsibleContent className='space-y-2'>
              {remainingStores.map(store => (
                <div key={store.id} className='flex items-center space-x-2 p-2'>
                  <Checkbox checked={false} onCheckedChange={() => handleStoreToggle(store.id)} />
                  <span className='text-sm'>{store.store_name}</span>
                </div>
              ))}
              {remainingStores.length === 0 && (
                <div className='p-2 text-sm text-gray-500'>Không có cửa hàng nào</div>
              )}
            </CollapsibleContent>
          </Collapsible>
        </div>

        <DialogFooter>
          <Button variant='outline' onClick={handleCancel}>
            Hủy
          </Button>
          <Button onClick={handleSave}>Lưu</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
