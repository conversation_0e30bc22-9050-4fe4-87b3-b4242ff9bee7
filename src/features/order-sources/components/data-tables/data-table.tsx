import type { OrderSource } from '@/types/api/order-sources'

import { Pagination } from '@/components/pagination'
import { Table, TableBody } from '@/components/ui'

import { TableEmpty } from './table-empty'
import { TableHeader } from './table-header'
import { TableRow } from './table-row'
import { TableSkeleton } from './table-skeleton'

interface DataTableProps {
  orderSources: OrderSource[]
  isLoading: boolean
  error: any
  currentPage: number
  onPageChange: (page: number) => void
}

export function DataTable({
  orderSources,
  isLoading,
  error,
  currentPage,
  onPageChange
}: DataTableProps) {
  return (
    <>
      <div className='rounded-md border'>
        <Table>
          <TableHeader />
          <TableBody>
            {isLoading && <TableSkeleton />}

            {!isLoading && error && (
              <tr>
                <td colSpan={5} className='h-24 text-center'>
                  <div className='text-muted-foreground'>Có lỗi xảy ra khi tải dữ liệu</div>
                </td>
              </tr>
            )}

            {!isLoading && !error && orderSources.length === 0 && <TableEmpty />}

            {!isLoading &&
              !error &&
              orderSources.length > 0 &&
              orderSources.map((source, index) => (
                <TableRow
                  key={source.id}
                  source={source}
                  index={index + 1 + (currentPage - 1) * 10}
                />
              ))}
          </TableBody>
        </Table>
      </div>

      {/* Pagination */}
      {orderSources.length > 0 && (
        <Pagination
          currentPage={currentPage}
          totalPages={Math.ceil(orderSources.length / 10)}
          pageSize={10}
          totalItems={orderSources.length}
          onPageChange={onPageChange}
        />
      )}
    </>
  )
}
