import { Skeleton } from '@/components/ui/skeleton'

import { TableCell, TableRow } from '@/components/ui'

export function TableSkeleton() {
  return (
    <>
      {Array.from({ length: 5 }).map((_, index) => (
        <TableRow key={index}>
          <TableCell className='text-center'>
            <Skeleton className='mx-auto h-4 w-6' />
          </TableCell>
          <TableCell>
            <Skeleton className='h-4 w-24' />
          </TableCell>
          <TableCell>
            <Skeleton className='h-4 w-32' />
          </TableCell>
          <TableCell>
            <div className='flex items-center gap-2'>
              <Skeleton className='h-4 w-20' />
              <Skeleton className='h-4 w-4' />
            </div>
          </TableCell>
          <TableCell>
            <Skeleton className='h-8 w-8' />
          </TableCell>
        </TableRow>
      ))}
    </>
  )
}
