import React from 'react'

import { useOrderSources } from '@/hooks/api/use-order-sources'

import { ActionBar, DataTable } from './components'

export default function OrderSourcesPage() {
  const [searchInput, setSearchInput] = React.useState('')
  const [searchQuery, setSearchQuery] = React.useState('')
  const [storeFilter, setStoreFilter] = React.useState<string>('all')
  const [currentPage, setCurrentPage] = React.useState(1)

  const {
    data: orderSourcesResponse,
    isLoading,
    error
  } = useOrderSources(currentPage, searchQuery, storeFilter)

  const orderSources = orderSourcesResponse?.data || []

  const handleSearchKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      setSearchQuery(searchInput.trim())
      setCurrentPage(1)
    }
  }

  const handleSearchInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchInput(e.target.value)
    if (e.target.value.trim() === '') {
      setSearchQuery('')
      setCurrentPage(1)
    }
  }

  const handleStoreFilterChange = (value: string) => {
    setStoreFilter(value)
    setCurrentPage(1) // Reset to first page when changing store filter
  }

  return (
    <div className='container mx-auto space-y-6 py-6'>
      <ActionBar
        searchInput={searchInput}
        storeFilter={storeFilter}
        onSearchInputChange={handleSearchInputChange}
        onSearchKeyPress={handleSearchKeyPress}
        onStoreFilterChange={handleStoreFilterChange}
      />

      <DataTable
        orderSources={orderSources}
        isLoading={isLoading}
        error={error}
        currentPage={currentPage}
        onPageChange={setCurrentPage}
      />
    </div>
  )
}
