import { useState } from 'react'

import type { Discount } from '@/types/discounts'
import { ChevronDown } from 'lucide-react'

import { usePosStores, useCurrentBrand, useCurrentCompany } from '@/stores/posStore'

import { useDiscounts, useDeleteDiscount, useUpdateDiscount, useSalesChannels } from '@/hooks/api'

import {
  Button,
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui'

import { CopyDiscountModal, DiscountDataTable } from './components'

export function DiscountPage() {
  const [selectedStoreId, setSelectedStoreId] = useState<string>('all')
  const [selectedChannelId, setSelectedChannelId] = useState<string>('all')
  const [selectedStatus, setSelectedStatus] = useState<string>('all')
  const [selectedExpiry, setSelectedExpiry] = useState<string>('all')
  const [isCopyModalOpen, setIsCopyModalOpen] = useState(false)

  const { currentBrandStores } = usePosStores()
  const { selectedBrand } = useCurrentBrand()
  const { company } = useCurrentCompany()

  const deleteDiscountMutation = useDeleteDiscount()

  const updateDiscountMutation = useUpdateDiscount()

  const storeUids =
    selectedStoreId === 'all' ? currentBrandStores.map(store => store.id) : [selectedStoreId]

  const { data: salesChannels = [], isLoading: isLoadingChannels } = useSalesChannels({
    companyUid: company?.id,
    brandUid: selectedBrand?.id,
    listStoreUid: storeUids,
    partnerConfig: 1,
    skipLimit: true
  })

  const { data: allDiscounts = [], isLoading: isLoadingDiscounts } = useDiscounts({
    companyUid: company?.id,
    brandUid: selectedBrand?.id,
    page: 1,
    listStoreUid: storeUids,
    promotionPartnerAutoGen: 1,
    status: selectedExpiry === 'all' ? undefined : (selectedExpiry as 'expired' | 'unexpired'),
    active: selectedStatus === 'all' ? undefined : parseInt(selectedStatus)
  })

  const discounts =
    selectedChannelId === 'all'
      ? allDiscounts
      : allDiscounts.filter(discount => discount.sourceUid === selectedChannelId)

  const isLoading = isLoadingChannels || isLoadingDiscounts

  const handleDeleteDiscount = (discountId: string) => {
    if (!company?.id || !selectedBrand?.id) return

    if (confirm('Bạn có chắc chắn muốn xóa chương trình giảm giá này?')) {
      deleteDiscountMutation.mutate({
        companyUid: company.id,
        brandUid: selectedBrand.id,
        id: discountId
      })
    }
  }

  const handleToggleActive = (discount: Discount) => {
    const discountApiData = {
      id: discount.id,
      created_at: discount.createdAt,
      created_by: discount.createdBy,
      updated_at: discount.updatedAt,
      updated_by: discount.updatedBy,
      deleted: discount.deleted || false,
      deleted_at: discount.deletedAt || null,
      deleted_by: discount.deletedBy || null,
      ta_discount: discount.taDiscount,
      ots_discount: discount.otsDiscount,
      is_all: discount.isAll,
      is_type: discount.isType,
      is_item: discount.isItem,
      type_id: discount.typeId,
      item_id: discount.itemId,
      discount_type: discount.discountType,
      from_date: discount.fromDate,
      to_date: discount.toDate,
      time_sale_hour_day: 0,
      time_sale_date_week: 0,
      description: null,
      extra_data: { combo_id: '', is_combo: 0 },
      active: discount.active === 1 ? 0 : 1, // Toggle active status
      revision: null,
      promotion_uid: discount.promotionUid,
      brand_uid: selectedBrand?.id || '',
      company_uid: company?.id || '',
      sort: 1000,
      store_uid: discount.storeUid,
      discount_clone_id: null,
      source_uid: discount.sourceUid,
      promotion: {
        id: discount.promotionUid,
        sort: 1000,
        active: 1,
        deleted: false,
        is_fabi: 1,
        revision: 0,
        brand_uid: selectedBrand?.id || '',
        store_uid: discount.storeUid,
        created_at: discount.createdAt || Date.now(),
        created_by: 'system',
        deleted_at: null,
        deleted_by: null,
        extra_data: {},
        source_uid: discount.sourceUid,
        updated_at: discount.updatedAt || Date.now(),
        updated_by: 'system',
        company_uid: company?.id || '',
        description: null,
        promotion_id: discount.promotionId,
        promotion_name: discount.promotionName,
        partner_auto_gen: discount.partnerAutoGen
      },
      promotion_id: discount.promotionId,
      partner_auto_gen: discount.partnerAutoGen,
      store_name: discount.storeName,
      source: {
        id: discount.source.id,
        sort: 1000,
        is_fb: 0,
        active: discount.source.active,
        deleted: false,
        is_fabi: 1,
        revision: null,
        brand_uid: selectedBrand?.id || '',
        source_id: discount.source.sourceId,
        store_uid: discount.storeUid,
        created_at: discount.createdAt || Date.now(),
        created_by: 'system',
        deleted_at: null,
        deleted_by: null,
        extra_data: {},
        updated_at: discount.updatedAt || Date.now(),
        updated_by: 'system',
        company_uid: company?.id || '',
        description: null,
        source_name: discount.source.sourceName,
        source_type: discount.source.sourceType,
        partner_config: 1
      }
    }

    updateDiscountMutation.mutate(discountApiData)
  }

  return (
    <div className='space-y-6'>
      <div className='space-y-4'>
        {/* Title, Filters and Actions - All on same line */}
        <div className='flex flex-wrap items-center gap-4'>
          {/* Title */}
          <h2 className='text-xl font-semibold whitespace-nowrap'>
            Chương trình giảm giá theo kênh
          </h2>

          {/* Store Dropdown */}
          <div className='min-w-[160px]'>
            <Select value={selectedStoreId} onValueChange={setSelectedStoreId}>
              <SelectTrigger>
                <SelectValue placeholder='Tất cả các điểm' />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value='all'>Tất cả các điểm</SelectItem>
                {currentBrandStores.map(store => (
                  <SelectItem key={store.id} value={store.id}>
                    {store.store_name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Channel Dropdown */}
          <div className='min-w-[160px]'>
            <Select value={selectedChannelId} onValueChange={setSelectedChannelId}>
              <SelectTrigger>
                <SelectValue placeholder='Tất cả kênh bán hàng' />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value='all'>Tất cả kênh bán hàng</SelectItem>
                {salesChannels.map(channel => (
                  <SelectItem key={channel.id} value={channel.id}>
                    {channel.sourceName}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Status Dropdown */}
          <div className='min-w-[130px]'>
            <Select value={selectedStatus} onValueChange={setSelectedStatus}>
              <SelectTrigger>
                <SelectValue placeholder='Tất cả trạng thái' />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value='all'>Tất cả trạng thái</SelectItem>
                <SelectItem value='1'>Active</SelectItem>
                <SelectItem value='0'>Deactive</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Expiry Dropdown */}
          <div className='min-w-[150px]'>
            <Select value={selectedExpiry} onValueChange={setSelectedExpiry}>
              <SelectTrigger>
                <SelectValue placeholder='Tất cả ngày áp dụng' />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value='all'>Tất cả ngày áp dụng</SelectItem>
                <SelectItem value='expired'>Hết hạn</SelectItem>
                <SelectItem value='unexpired'>Chưa hết hạn</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Actions - Push to right */}
          <div className='ml-auto flex items-center gap-2'>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant='outline' size='sm'>
                  Tiện ích <ChevronDown className='ml-2 h-4 w-4' />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuItem onClick={() => setIsCopyModalOpen(true)}>
                  Sao chép CTGG
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
            <Button size='sm'>Tạo giảm giá</Button>
          </div>
        </div>

        <DiscountDataTable
          discounts={discounts}
          isLoading={isLoading}
          onToggleActive={handleToggleActive}
          onDeleteDiscount={handleDeleteDiscount}
          isDeleting={deleteDiscountMutation.isPending}
        />
      </div>

      <CopyDiscountModal isOpen={isCopyModalOpen} onClose={() => setIsCopyModalOpen(false)} />
    </div>
  )
}
