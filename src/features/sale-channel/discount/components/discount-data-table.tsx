import type { Discount } from '@/types/discounts'
import { Trash2 } from 'lucide-react'

import { Bad<PERSON>, Button } from '@/components/ui'

interface DiscountDataTableProps {
  discounts: Discount[]
  isLoading: boolean
  onToggleActive: (discount: Discount) => void
  onDeleteDiscount: (discountId: string) => void
  isDeleting: boolean
}

export function DiscountDataTable({
  discounts,
  isLoading,
  onToggleActive,
  onDeleteDiscount,
  isDeleting
}: DiscountDataTableProps) {
  return (
    <div className='rounded-md border'>
      <table className='w-full'>
        <thead>
          <tr className='bg-muted/50 border-b'>
            <th className='text-muted-foreground h-12 px-4 text-left align-middle font-medium'>
              #
            </th>
            <th className='text-muted-foreground h-12 px-4 text-left align-middle font-medium'>
              Kênh
            </th>
            <th className='text-muted-foreground h-12 px-4 text-left align-middle font-medium'>
              <PERSON><PERSON><PERSON> hàng
            </th>
            <th className='text-muted-foreground h-12 px-4 text-left align-middle font-medium'>
              <PERSON><PERSON> chỉnh
            </th>
            <th className='text-muted-foreground h-12 px-4 text-left align-middle font-medium'>
              Giảm giá
            </th>
            <th className='text-muted-foreground h-12 px-4 text-left align-middle font-medium'>
              Thao tác
            </th>
            <th className='text-muted-foreground h-12 px-4 text-left align-middle font-medium'></th>
          </tr>
        </thead>
        <tbody>
          {isLoading ? (
            <tr>
              <td colSpan={7} className='h-24 text-center'>
                Đang tải dữ liệu...
              </td>
            </tr>
          ) : discounts.length === 0 ? (
            <tr>
              <td colSpan={7} className='text-muted-foreground h-24 text-center'>
                Không có dữ liệu
              </td>
            </tr>
          ) : (
            discounts.map((discount, index) => (
              <tr key={discount.id} className='border-b'>
                <td className='px-4 py-3'>{index + 1}</td>
                <td className='px-4 py-3'>{discount.source.sourceName}</td>
                <td className='px-4 py-3'>{discount.storeName}</td>
                <td className='px-4 py-3'>
                  <div className='space-y-1'>
                    {/* Check if expired */}
                    {discount.toDate < Date.now() ? (
                      <div className='font-medium text-red-600'>Hết hạn</div>
                    ) : (
                      <div className='text-sm'>
                        Từ {new Date(discount.fromDate).toLocaleDateString('vi-VN')} đến{' '}
                        {new Date(discount.toDate).toLocaleDateString('vi-VN')}
                      </div>
                    )}
                    {/* Application scope */}
                    <div className='text-muted-foreground text-xs'>
                      {discount.isAll === 1 && '(Áp dụng cho tất cả)'}
                      {discount.isItem === 1 && '(Áp dụng cho 1 món)'}
                      {discount.isType === 1 && '(Áp dụng cho 1 loại món)'}
                    </div>
                  </div>
                </td>
                <td className='px-4 py-3'>
                  {discount.discountType === 'AMOUNT'
                    ? `${discount.taDiscount.toLocaleString('vi-VN')}đ`
                    : `${discount.taDiscount * 100}%`}
                </td>
                <td className='px-4 py-3'>
                  {discount.active === 1 ? (
                    <Badge
                      variant='default'
                      className='cursor-pointer bg-green-100 text-green-800 hover:bg-green-200'
                      onClick={() => onToggleActive(discount)}
                    >
                      Active
                    </Badge>
                  ) : (
                    <Badge
                      variant='destructive'
                      className='cursor-pointer bg-red-100 text-red-800 hover:bg-red-200'
                      onClick={() => onToggleActive(discount)}
                    >
                      Deactive
                    </Badge>
                  )}
                </td>
                <td className='px-4 py-3'>
                  <Button
                    variant='ghost'
                    size='sm'
                    onClick={() => onDeleteDiscount(discount.id)}
                    disabled={isDeleting}
                  >
                    <Trash2 className='h-4 w-4' />
                  </Button>
                </td>
              </tr>
            ))
          )}
        </tbody>
      </table>
    </div>
  )
}
