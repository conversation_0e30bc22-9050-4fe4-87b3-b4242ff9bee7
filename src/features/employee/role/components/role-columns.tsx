import { ColumnDef } from '@tanstack/react-table'
import { IconCopy, IconTrash } from '@tabler/icons-react'

import { Role } from '@/types/role'

import { Button } from '@/components/ui/button'
import { Checkbox } from '@/components/ui/checkbox'

export const roleColumns: ColumnDef<Role>[] = [
  {
    id: 'select',
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && 'indeterminate')
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
    enableSorting: false,
    enableHiding: false
  },
  {
    accessorKey: 'id',
    header: '#',
    cell: ({ row }) => {
      const index = row.index + 1
      return <div className='w-[50px] font-medium'>{index}</div>
    },
    enableSorting: false
  },
  {
    accessorKey: 'role_name',
    header: 'Tên chức vụ',
    cell: ({ row }) => {
      const role = row.original
      return <span className='font-medium'>{role.role_name}</span>
    }
  },
  {
    accessorKey: 'description',
    header: 'Mô tả',
    cell: ({ row }) => {
      const role = row.original
      return (
        <span className='text-muted-foreground'>
          {role.description || '-'}
        </span>
      )
    }
  },
  {
    accessorKey: 'allow_access',
    header: 'Phạm vi đăng nhập',
    cell: ({ row }) => {
      const role = row.original
      const accessText = role.allow_access?.join(', ') || '-'
      return (
        <span className='text-sm'>
          {accessText}
        </span>
      )
    }
  },
  {
    id: 'copy',
    header: 'Sao chép',
    cell: ({ row, table }) => {
      const role = row.original
      const meta = table.options.meta as {
        onCopyRole?: (role: Role) => void
      }

      return (
        <Button
          variant='ghost'
          size='sm'
          onClick={() => meta?.onCopyRole?.(role)}
          className='h-8 w-8 p-0 text-blue-600 hover:text-blue-700'
        >
          <IconCopy className='h-4 w-4' />
          <span className='sr-only'>Sao chép quyền {role.role_name}</span>
        </Button>
      )
    }
  },
  {
    id: 'delete',
    header: '',
    cell: ({ row, table }) => {
      const role = row.original
      const meta = table.options.meta as {
        onDeleteRole?: (role: Role) => void
      }

      return (
        <Button
          variant='ghost'
          size='sm'
          onClick={() => meta?.onDeleteRole?.(role)}
          className='h-8 w-8 p-0 text-red-600 hover:text-red-700'
        >
          <IconTrash className='h-4 w-4' />
          <span className='sr-only'>Xóa chức vụ {role.role_name}</span>
        </Button>
      )
    }
  }
]