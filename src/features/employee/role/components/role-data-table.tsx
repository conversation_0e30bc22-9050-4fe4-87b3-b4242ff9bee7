import { useState, useEffect } from 'react'
import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  useReactTable,
  RowSelectionState
} from '@tanstack/react-table'

import { Role } from '@/types/role'

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table'
import { Button } from '@/components/ui/button'

interface RoleDataTableProps {
  columns: ColumnDef<Role>[]
  data: Role[]
  onCopyRole?: (role: Role) => void
  onDeleteRole?: (role: Role) => void
  onBulkDelete?: (selectedRoles: Role[]) => void
  clearSelection?: boolean
}

export function RoleDataTable({
  columns,
  data,
  onCopyRole,
  onDeleteRole,
  onBulkDelete,
  clearSelection
}: RoleDataTableProps) {
  const [rowSelection, setRowSelection] = useState<RowSelectionState>({})

  // Clear selection when clearSelection prop changes
  useEffect(() => {
    if (clearSelection) {
      setRowSelection({})
    }
  }, [clearSelection])
  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    state: {
      rowSelection
    },
    enableRowSelection: true,
    onRowSelectionChange: setRowSelection,
    meta: {
      onCopyRole,
      onDeleteRole
    }
  })

  const selectedRows = table.getFilteredSelectedRowModel().rows
  const selectedRoles = selectedRows.map(row => row.original)

  const handleBulkDelete = () => {
    if (onBulkDelete && selectedRoles.length > 0) {
      onBulkDelete(selectedRoles)
    }
  }

  return (
    <div className="space-y-4">
      {selectedRoles.length > 0 && (
        <div className="flex items-center justify-between p-4 bg-muted rounded-md">
          <span className="text-sm text-muted-foreground">
            Đã chọn {selectedRoles.length} chức vụ
          </span>
          <Button
            variant="destructive"
            size="sm"
            onClick={handleBulkDelete}
          >
            Xóa đã chọn
          </Button>
        </div>
      )}
      <div className='rounded-md border'>
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map(headerGroup => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map(header => {
                  return (
                    <TableHead key={header.id}>
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                    </TableHead>
                  )
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map(row => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && 'selected'}
                >
                  {row.getVisibleCells().map(cell => (
                    <TableCell key={cell.id}>
                      {flexRender(cell.column.columnDef.cell, cell.getContext())}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={columns.length} className='h-24 text-center'>
                  Không có dữ liệu chức vụ.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  )
}