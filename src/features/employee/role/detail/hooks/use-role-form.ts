import { useState } from 'react'

export const useRoleForm = () => {
  const [roleName, setRoleName] = useState('')
  const [description, setDescription] = useState('')

  const isFormValid = roleName.trim() !== '' && description.trim() !== ''

  const resetForm = () => {
    setRoleName('')
    setDescription('')
  }

  return {
    roleName,
    setRoleName,
    description,
    setDescription,
    isFormValid,
    resetForm
  }
}
