import { useState } from 'react'

import { getAllPermissions } from '../utils'

export const usePermissions = () => {
  const [permissions, setPermissions] = useState<string[]>(getAllPermissions())

  const handlePermissionChange = (permission: string, checked: boolean) => {
    if (checked) {
      setPermissions(prev => [...prev, permission])
    } else {
      setPermissions(prev => prev.filter(p => p !== permission))
    }
  }

  const handleParentPermissionChange = (
    _parentId: string,
    checked: boolean,
    childPermissions: string[]
  ) => {
    setPermissions(prev => {
      let newPermissions = [...prev]

      if (checked) {
        // Add all child permissions
        childPermissions.forEach(childPerm => {
          if (!newPermissions.includes(childPerm)) {
            newPermissions.push(childPerm)
          }
        })
      } else {
        // Remove all child permissions
        newPermissions = newPermissions.filter(p => !childPermissions.includes(p))
      }

      return newPermissions
    })
  }

  const handleChildPermissionChange = (
    childId: string,
    checked: boolean,
    _parentId: string,
    _allChildPermissions: string[]
  ) => {
    setPermissions(prev => {
      let newPermissions = [...prev]

      if (checked) {
        if (!newPermissions.includes(childId)) {
          newPermissions.push(childId)
        }
      } else {
        newPermissions = newPermissions.filter(p => p !== childId)
      }

      return newPermissions
    })
  }

  return {
    permissions,
    setPermissions,
    handlePermissionChange,
    handleParentPermissionChange,
    handleChildPermissionChange
  }
}
