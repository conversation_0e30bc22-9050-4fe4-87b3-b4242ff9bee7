import { MANAGER_PERMISSIONS, PERMISSION_DETAILS } from '@/constants'
import { ChevronDown } from 'lucide-react'

import {
  Label,
  Checkbox,
  Collapsible,
  CollapsibleTrigger,
  CollapsibleContent
} from '@/components/ui'

interface ManagerTabContentProps {
  permissions: string[]
  onParentPermissionChange: (parentId: string, checked: boolean, childPermissions: string[]) => void
  onChildPermissionChange: (
    childId: string,
    checked: boolean,
    parentId: string,
    allChildPermissions: string[]
  ) => void
}

export function ManagerTabContent({
  permissions,
  onParentPermissionChange,
  onChildPermissionChange
}: ManagerTabContentProps) {
  return (
    <div className='space-y-4'>
      <p className='text-muted-foreground text-sm'>Quyền quản lý và giám sát</p>

      <div className='space-y-3'>
        {MANAGER_PERMISSIONS.map(section => (
          <div key={section.id}>
            <h3 className='mb-3 text-base font-semibold'>{section.title}</h3>
            <div className='space-y-2'>
              {section.items.map(item => {
                const parentId = `manager-${item.id}`
                const childPermissions = item.permissions || []

                return (
                  <Collapsible key={item.id}>
                    <CollapsibleTrigger className='hover:bg-muted flex w-full items-center gap-3 rounded-md border p-3 text-left'>
                      <Checkbox
                        id={parentId}
                        checked={childPermissions.every(p => permissions.includes(p))}
                        onCheckedChange={checked =>
                          onParentPermissionChange(parentId, checked as boolean, childPermissions)
                        }
                        onClick={e => e.stopPropagation()}
                      />
                      <span className='flex-1 text-sm font-medium'>{item.label}</span>
                      <ChevronDown className='h-4 w-4 transition-transform duration-200 data-[state=open]:rotate-180' />
                    </CollapsibleTrigger>
                    <CollapsibleContent className='bg-slate-100 p-3'>
                      <div className='mx-6'>
                        {item.permissions && item.permissions.length > 0 ? (
                          <div className='flex flex-wrap gap-4'>
                            {item.permissions.map(permission => {
                              return (
                                <div key={permission} className='flex items-center space-x-2'>
                                  <Checkbox
                                    id={permission}
                                    checked={permissions.includes(permission)}
                                    onCheckedChange={checked =>
                                      onChildPermissionChange(
                                        permission,
                                        checked as boolean,
                                        parentId,
                                        childPermissions
                                      )
                                    }
                                  />
                                  <Label
                                    htmlFor={permission}
                                    className='cursor-pointer text-sm font-normal'
                                  >
                                    {PERMISSION_DETAILS[permission] || permission}
                                  </Label>
                                </div>
                              )
                            })}
                          </div>
                        ) : (
                          <p className='text-muted-foreground text-xs'>
                            Quyền chi tiết sẽ được thêm sau
                          </p>
                        )}
                      </div>
                    </CollapsibleContent>
                  </Collapsible>
                )
              })}
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}
