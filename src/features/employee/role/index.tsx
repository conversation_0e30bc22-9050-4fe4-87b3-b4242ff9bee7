import { useState } from 'react'

import { useNavigate } from '@tanstack/react-router'

import { IconPlus } from '@tabler/icons-react'

import { Role } from '@/types/role'
import { toast } from 'sonner'

import { getErrorMessage } from '@/utils/error-utils'

import { useRolesData, useDeleteRole, useBulkDeleteRoles } from '@/hooks/api'

import { ConfirmModal } from '@/components/pos'
import { Button } from '@/components/ui'

import { roleColumns, RoleDataTable } from './components'

export default function EmployeeRoleListPage() {
  const navigate = useNavigate()
  const [confirmModalOpen, setConfirmModalOpen] = useState(false)
  const [isBulkDeleteModalOpen, setIsBulkDeleteModalOpen] = useState(false)
  const [selectedRole, setSelectedRole] = useState<Role | null>(null)
  const [selectedRoles, setSelectedRoles] = useState<Role[]>([])
  const [clearSelection, setClearSelection] = useState(false)

  const deleteRoleMutation = useDeleteRole()
  const bulkDeleteRolesMutation = useBulkDeleteRoles()

  const { data: roles, isLoading: rolesLoading, error: rolesError } = useRolesData({})

  const isLoading = rolesLoading
  const error = rolesError

  const handleCopyRole = (role: Role) => {
    // TODO: Implement copy role functionality
    toast.info(`Sao chép quyền cho chức vụ: ${role.role_name}`)
  }

  const handleDeleteRole = (role: Role) => {
    setSelectedRole(role)
    setConfirmModalOpen(true)
  }

  const handleBulkDelete = (roles: Role[]) => {
    setSelectedRoles(roles)
    setIsBulkDeleteModalOpen(true)
  }

  const handleConfirmDelete = async () => {
    if (!selectedRole) return

    try {
      await deleteRoleMutation.mutateAsync(selectedRole.id)
      toast.success(`Chức vụ "${selectedRole.role_name}" đã được xóa thành công!`)
      setConfirmModalOpen(false)
      setSelectedRole(null)
    } catch (error) {
      const errorMessage = getErrorMessage(error)
      toast.error(errorMessage)
    }
  }

  const confirmBulkDelete = async () => {
    if (selectedRoles.length === 0) return

    try {
      const roleIds = selectedRoles.map(role => role.id)
      await bulkDeleteRolesMutation.mutateAsync(roleIds)
      toast.success(`Đã xóa ${selectedRoles.length} chức vụ thành công`)
      setIsBulkDeleteModalOpen(false)
      setSelectedRoles([])
      setClearSelection(true)
      // Reset clear selection flag after a short delay
      setTimeout(() => setClearSelection(false), 100)
    } catch (error) {
      const errorMessage = getErrorMessage(error)
      toast.error(`Lỗi khi xóa chức vụ: ${errorMessage}`)
    }
  }

  const handleCreateRole = () => {
    navigate({ to: '/employee/role/detail' })
  }

  return (
    <div className='container mx-auto px-4 py-8'>
      <div className='mb-2 flex items-center justify-between'>
        <h2 className='text-xl font-semibold'>Danh sách chức vụ</h2>
        <Button size='sm' onClick={handleCreateRole}>
          <IconPlus className='mr-2 h-4 w-4' />
          Tạo chức vụ
        </Button>
      </div>

      {error ? (
        <div className='py-8 text-center'>
          <p className='text-red-600'>{getErrorMessage(error)}</p>
        </div>
      ) : isLoading ? (
        <div className='py-8 text-center'>
          <p>Đang tải dữ liệu chức vụ...</p>
        </div>
      ) : (
        <RoleDataTable
          columns={roleColumns}
          data={roles || []}
          onCopyRole={handleCopyRole}
          onDeleteRole={handleDeleteRole}
          onBulkDelete={handleBulkDelete}
          clearSelection={clearSelection}
        />
      )}

      <ConfirmModal
        open={confirmModalOpen}
        onOpenChange={setConfirmModalOpen}
        content='Bạn có muốn xoá chức vụ này?'
        onConfirm={handleConfirmDelete}
        isLoading={deleteRoleMutation.isPending}
      />

      <ConfirmModal
        open={isBulkDeleteModalOpen}
        onOpenChange={setIsBulkDeleteModalOpen}
        content={`Bạn có chắc chắn muốn xóa ${selectedRoles.length} chức vụ đã chọn? Hành động này không thể hoàn tác.`}
        onConfirm={confirmBulkDelete}
        isLoading={bulkDeleteRolesMutation.isPending}
      />
    </div>
  )
}
