import { useState, useMemo } from 'react'
import { useNavigate } from '@tanstack/react-router'
import { toast } from 'sonner'
import { useAuthStore } from '@/stores/authStore'
import { useUsersData } from '@/hooks/api'
import { useExcelExport } from '@/hooks/use-excel-export'
import { getErrorMessage } from '@/utils/error-utils'
import {
  createEmployeesColumns,
  EmployeesDataTable,
  EmployeesHeader,
} from './components'



export default function EmployeeListPage() {
  const navigate = useNavigate()
  const [searchTerm, setSearchTerm] = useState<string>('')
  const [searchQuery, setSearchQuery] = useState<string>('')
  const [selectedRole, setSelectedRole] = useState<string>('all')
  const [selectedStatus, setSelectedStatus] = useState<string>('all')
  const [selectedBrand, setSelectedBrand] = useState<string>('all')
  const [selectedCity, setSelectedCity] = useState<string>('all')
  const [selectedStore, setSelectedStore] = useState<string>('all')

  // Get company from auth store
  const { company } = useAuthStore((state) => state.auth)

  const {
    data: employees,
    isLoading: employeesLoading,
    error: employeesError
  } = useUsersData({
    company_uid: company?.id,
    search: searchTerm || undefined,
    brand_uid: selectedBrand !== 'all' ? selectedBrand : undefined,
    city_uid: selectedCity !== 'all' ? selectedCity : undefined,
    store_uid: selectedStore !== 'all' ? selectedStore : undefined
  })

  const isLoading = employeesLoading
  const error = employeesError

  const filteredEmployees = useMemo(() => {
    if (!employees) return []

    let filtered = employees

    if (selectedRole !== 'all') {
      filtered = filtered.filter(employee => employee.role_id === selectedRole)
    }

    if (selectedStatus !== 'all') {
      const isActive = selectedStatus === 'active'
      filtered = filtered.filter(employee => employee.active === (isActive ? 1 : 0))
    }

    return filtered
  }, [employees, selectedRole, selectedStatus])

  const handleSearchChange = (value: string) => {
    setSearchQuery(value)
  }

  const handleSearchSubmit = () => {
    setSearchTerm(searchQuery)
  }

  const exportData = useMemo(() => {
    return filteredEmployees.map(employee => ({
      id: employee.id,
      full_name: employee.full_name,
      role_name: employee.role_name,
      email: employee.email,
      phone: employee.phone || '',
      status: employee.active === 1 ? 'Active' : 'Inactive',
      stores: '' // TODO: Format stores data if needed
    }))
  }, [filteredEmployees])

  const { exportData: exportToExcel, isExporting } = useExcelExport({
    data: exportData,
    filename: `danh-sach-nhan-vien-${new Date().toISOString().split('T')[0]}.xlsx`,
    sheetName: 'Danh sách nhân viên',
    columnMapping: {
      id: 'ID',
      full_name: 'Tên',
      role_name: 'Chức vụ',
      email: 'Email',
      phone: 'Số điện thoại',
      status: 'Trạng thái',
      stores: 'Cửa hàng'
    },
    onExportStart: () => {
      toast.info('Đang xuất danh sách nhân viên...')
    },
    onExportComplete: () => {
      toast.success('Xuất danh sách nhân viên thành công!')
    },
    onExportError: error => {
      console.error('Export error:', error)
      toast.error('Có lỗi xảy ra khi xuất danh sách nhân viên')
    }
  })

  const handleExportEmployees = () => {
    if (filteredEmployees.length === 0) {
      toast.warning('Không có dữ liệu nhân viên để xuất')
      return
    }
    exportToExcel()
  }

  const handleInviteEmployee = () => {
    navigate({ to: '/employee/detail' })
  }

  const handleEmployeeClick = (userId: string) => {
    navigate({ to: '/employee/detail/$userId', params: { userId } })
  }

  const employeesColumns = createEmployeesColumns(handleEmployeeClick)

  return (
    <div className='container mx-auto px-4 py-8'>
      <EmployeesHeader
        searchTerm={searchQuery}
        onSearchChange={handleSearchChange}
        onSearchSubmit={handleSearchSubmit}
        selectedRole={selectedRole}
        onRoleChange={setSelectedRole}
        selectedStatus={selectedStatus}
        onStatusChange={setSelectedStatus}
        selectedBrand={selectedBrand}
        onBrandChange={setSelectedBrand}
        selectedCity={selectedCity}
        onCityChange={setSelectedCity}
        selectedStore={selectedStore}
        onStoreChange={setSelectedStore}
        onExportEmployees={handleExportEmployees}
        onInviteEmployee={handleInviteEmployee}
        isExporting={isExporting}
      />

      {error ? (
        <div className='py-8 text-center'>
          <p className='text-red-600'>{getErrorMessage(error)}</p>
        </div>
      ) : isLoading ? (
        <div className='py-8 text-center'>
          <p>Đang tải dữ liệu nhân viên...</p>
        </div>
      ) : (
        <EmployeesDataTable columns={employeesColumns} data={filteredEmployees} />
      )}
    </div>
  )
}
