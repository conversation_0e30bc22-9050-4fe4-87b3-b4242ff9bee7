import { ColumnDef } from '@tanstack/react-table'

import type { User } from '@/types/user'

import { DataTableColumnHeader } from '@/components/data-table'
import { Badge } from '@/components/ui'

export const createEmployeesColumns = (
  onRowClick: (userId: string) => void
): ColumnDef<User>[] => [
  {
    id: 'index',
    header: '#',
    cell: ({ row }) => {
      return <div className='w-8 text-center'>{row.index + 1}</div>
    },
    enableSorting: false,
    enableHiding: false
  },
  {
    accessorKey: 'full_name',
    header: ({ column }) => <DataTableColumnHeader column={column} title='Tên' />,
    cell: ({ row }) => {
      const user = row.original
      return (
        <div
          className='cursor-pointer font-medium text-blue-600 hover:text-blue-800 hover:underline'
          onClick={() => onRowClick(user.id)}
        >
          {row.getValue('full_name')}
        </div>
      )
    },
  },
  {
    accessorKey: 'role_name',
    header: ({ column }) => <DataTableColumnHeader column={column} title='Chức vụ' />,
    cell: ({ row }) => {
      return <div>{row.getValue('role_name')}</div>
    }
  },
  {
    accessorKey: 'email',
    header: ({ column }) => <DataTableColumnHeader column={column} title='Email' />,
    cell: ({ row }) => {
      return <div className='text-muted-foreground'>{row.getValue('email')}</div>
    }
  },
  {
    accessorKey: 'phone',
    header: ({ column }) => <DataTableColumnHeader column={column} title='Số điện thoại' />,
    cell: ({ row }) => {
      return <div>{row.getValue('phone')}</div>
    }
  },
  {
    id: 'stores',
    header: ({ column }) => <DataTableColumnHeader column={column} title='Cửa hàng' />,
    cell: () => {
      return <div className='text-muted-foreground'>-</div>
    },
    enableSorting: false
  },
  {
    accessorKey: 'active',
    header: ({ column }) => <DataTableColumnHeader column={column} title='Thao tác' />,
    cell: ({ row }) => {
      const isActive = row.getValue('active') === 1
      return (
        <Badge
          variant={isActive ? 'default' : 'destructive'}
          className={isActive ? 'bg-green-500 hover:bg-green-600' : 'bg-red-500 hover:bg-red-600'}
        >
          {isActive ? 'Active' : 'Deactive'}
        </Badge>
      )
    },
    enableSorting: false
  }
]
