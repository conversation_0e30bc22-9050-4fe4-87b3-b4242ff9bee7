import { useState, useMemo } from 'react'
import { useHierarchicalData } from './use-hierarchical-data'

export function useBrandCityStoreSelector(selectedItems: string[]) {
  const [searchTerm, setSearchTerm] = useState('')
  const [expandedBrands, setExpandedBrands] = useState<Set<string>>(new Set())
  const [expandedCities, setExpandedCities] = useState<Set<string>>(new Set())
  const [localSelectedItems, setLocalSelectedItems] = useState<Set<string>>(
    new Set(selectedItems)
  )

  const { hierarchicalData, filterHierarchicalData } = useHierarchicalData()

  const filteredData = useMemo(() => {
    return filterHierarchicalData(searchTerm)
  }, [filterHierarchicalData, searchTerm])

  const toggleBrandExpansion = (brandId: string) => {
    const newExpanded = new Set(expandedBrands)
    if (newExpanded.has(brandId)) {
      newExpanded.delete(brandId)
    } else {
      newExpanded.add(brandId)
    }
    setExpandedBrands(newExpanded)
  }

  const toggleCityExpansion = (cityId: string) => {
    const newExpanded = new Set(expandedCities)
    if (newExpanded.has(cityId)) {
      newExpanded.delete(cityId)
    } else {
      newExpanded.add(cityId)
    }
    setExpandedCities(newExpanded)
  }

  const handleItemToggle = (
    itemId: string,
    type: 'brand' | 'city' | 'store'
  ) => {
    const newSelected = new Set(localSelectedItems)

    if (type === 'brand') {
      const brand = hierarchicalData.find((b) => b.id === itemId)
      if (!brand) return

      const brandSelected = newSelected.has(`brand:${itemId}`)

      if (brandSelected) {
        // Unselect brand and all its cities/stores
        newSelected.delete(`brand:${itemId}`)
        brand.cities.forEach((city) => {
          newSelected.delete(`city:${city.id}`)
          city.stores.forEach((store) => {
            newSelected.delete(`store:${store.id}`)
          })
        })
      } else {
        // Select brand and all its cities/stores
        newSelected.add(`brand:${itemId}`)
        brand.cities.forEach((city) => {
          newSelected.add(`city:${city.id}`)
          city.stores.forEach((store) => {
            newSelected.add(`store:${store.id}`)
          })
        })
      }
    } else if (type === 'city') {
      const citySelected = newSelected.has(`city:${itemId}`)
      const city = hierarchicalData
        .flatMap((b) => b.cities)
        .find((c) => c.id === itemId)

      if (!city) return

      if (citySelected) {
        // Unselect city and all its stores
        newSelected.delete(`city:${itemId}`)
        city.stores.forEach((store) => {
          newSelected.delete(`store:${store.id}`)
        })
      } else {
        // Select city and all its stores
        newSelected.add(`city:${itemId}`)
        city.stores.forEach((store) => {
          newSelected.add(`store:${store.id}`)
        })
      }
    } else if (type === 'store') {
      if (newSelected.has(`store:${itemId}`)) {
        newSelected.delete(`store:${itemId}`)
      } else {
        newSelected.add(`store:${itemId}`)
      }
    }

    setLocalSelectedItems(newSelected)
  }

  const isItemSelected = (itemId: string, type: 'brand' | 'city' | 'store') => {
    // Check if item is directly selected
    if (localSelectedItems.has(`${type}:${itemId}`)) {
      return true
    }

    // Check if parent is selected (which means this item is also selected)
    if (type === 'city') {
      // Check if brand containing this city is selected
      const brand = hierarchicalData.find((b) =>
        b.cities.some((c) => c.id === itemId)
      )
      if (brand && localSelectedItems.has(`brand:${brand.id}`)) {
        return true
      }
    } else if (type === 'store') {
      // Check if city containing this store is selected
      const city = hierarchicalData
        .flatMap((b) => b.cities)
        .find((c) => c.stores.some((s) => s.id === itemId))

      if (city && localSelectedItems.has(`city:${city.id}`)) {
        return true
      }

      // Check if brand containing this store is selected
      const brand = hierarchicalData.find((b) =>
        b.cities.some((c) => c.stores.some((s) => s.id === itemId))
      )
      if (brand && localSelectedItems.has(`brand:${brand.id}`)) {
        return true
      }
    }

    return false
  }

  const isItemIndeterminate = (itemId: string, type: 'brand' | 'city') => {
    // If item is fully selected, it's not indeterminate
    if (localSelectedItems.has(`${type}:${itemId}`)) {
      return false
    }

    if (type === 'brand') {
      const brand = hierarchicalData.find((b) => b.id === itemId)
      if (!brand) return false

      // Check if any children are selected (but not all)
      const hasSelectedChildren = brand.cities.some(
        (city) =>
          localSelectedItems.has(`city:${city.id}`) ||
          city.stores.some((store) =>
            localSelectedItems.has(`store:${store.id}`)
          )
      )

      return hasSelectedChildren
    } else if (type === 'city') {
      const city = hierarchicalData
        .flatMap((b) => b.cities)
        .find((c) => c.id === itemId)

      if (!city) return false

      // Check if parent brand is selected (then city is fully selected, not indeterminate)
      const brand = hierarchicalData.find((b) =>
        b.cities.some((c) => c.id === itemId)
      )
      if (brand && localSelectedItems.has(`brand:${brand.id}`)) {
        return false
      }

      // Check if some (but not all) stores are selected
      const hasSelectedStores = city.stores.some((store) =>
        localSelectedItems.has(`store:${store.id}`)
      )

      return hasSelectedStores
    }

    return false
  }

  return {
    searchTerm,
    setSearchTerm,
    expandedBrands,
    expandedCities,
    localSelectedItems,
    setLocalSelectedItems,
    hierarchicalData,
    filteredData,
    toggleBrandExpansion,
    toggleCityExpansion,
    handleItemToggle,
    isItemSelected,
    isItemIndeterminate,
  }
}
