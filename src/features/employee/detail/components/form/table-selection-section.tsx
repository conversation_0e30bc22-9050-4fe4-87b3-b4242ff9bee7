import { Button } from '@/components/ui'
import { SelectedTablesDisplay } from '../selected-tables-display'

interface TableSelectionSectionProps {
  selectedTables: string[]
  selectedStore: string
  brandId: string
  onShowTableSelector: () => void
}

export function TableSelectionSection({
  selectedTables,
  selectedStore,
  brandId,
  onShowTableSelector,
}: TableSelectionSectionProps) {
  return (
    <div className='space-y-6'>
      <h3 className='text-base font-medium'>Chọn khu vực, bàn</h3>

      <div className='space-y-4'>
        <SelectedTablesDisplay
          selectedTables={selectedTables}
          storeId={selectedStore.replace('store:', '')}
          brandId={brandId}
        />

        <Button
          type='button'
          variant='outline'
          onClick={onShowTableSelector}
          className='w-full justify-center text-blue-600'
        >
          Chọn khu vực, bàn
        </Button>
      </div>
    </div>
  )
}
