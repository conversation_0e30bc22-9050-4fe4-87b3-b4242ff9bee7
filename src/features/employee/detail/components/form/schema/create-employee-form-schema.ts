import { z } from 'zod'

export const createFormSchema = (isEditMode: boolean = false) =>
  z
    .object({
      email: z.string().email('<PERSON>ail không hợp lệ'),
      full_name: z.string().min(1, 'Tên nhân viên là bắt buộc'),
      phone: z.string().optional(),
      role_uid: z.string().min(1, 'Chức vụ là bắt buộc'),
      password: isEditMode
        ? z.string().optional()
        : z.string().min(6, 'Mật khẩu phải có ít nhất 6 ký tự'),
      confirmPassword: isEditMode
        ? z.string().optional()
        : z.string().min(6, '<PERSON><PERSON><PERSON> nhận mật khẩu phải có ít nhất 6 ký tự'),
      brand_access: z.array(z.string()).optional(),
    })
    .refine(
      (data) => {
        if (isEditMode) {
          if (data.password && data.password.length > 0) {
            return data.password === data.confirmPassword
          }
          return true
        }
        return data.password === data.confirmPassword
      },
      {
        message: '<PERSON><PERSON><PERSON> khẩu xác nhận không khớp',
        path: ['confirmPassword'],
      }
    )

export type CreateEmployeeFormData = z.infer<
  ReturnType<typeof createFormSchema>
>
