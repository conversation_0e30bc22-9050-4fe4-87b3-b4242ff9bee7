import { useMemo } from 'react'
import type { User } from '@/types'
import { useAuthStore } from '@/stores/authStore'
import { SelectedItemsSummary } from './selected-items-summary'

interface UserPermissionsSummaryProps {
  userData: User
}

export function UserPermissionsSummary({
  userData,
}: UserPermissionsSummaryProps) {
  const { stores, brands, cities } = useAuthStore((state) => state.auth)

  const brandAccessSummary = useMemo(() => {
    if (!userData.brand_access || userData.brand_access.length === 0) {
      return []
    }

    const summary: Array<{
      type: 'brand' | 'city' | 'store'
      brandName: string
      cityName?: string
      storeName?: string
      accessLevel: string
    }> = []

    userData.brand_access.forEach((access) => {
      if (access.startsWith('brand:')) {
        const brandId = access.replace('brand:', '')
        const brand = brands?.find((b) => b.id === brandId)
        if (brand) {
          summary.push({
            type: 'brand',
            brandName: brand.brand_name,
            accessLevel: 'Truy cập toàn thương hiệu',
          })
        }
      } else if (access.startsWith('city:')) {
        const cityId = access.replace('city:', '')
        const city = cities?.find((c) => c.id === cityId)
        if (city) {
          const relatedStores =
            stores?.filter((s) => s.city_uid === cityId) || []
          const brandIds = [...new Set(relatedStores.map((s) => s.brand_uid))]

          brandIds.forEach((brandId) => {
            const brand = brands?.find((b) => b.id === brandId)
            if (brand) {
              summary.push({
                type: 'city',
                brandName: brand.brand_name,
                cityName: city.city_name,
                accessLevel: `${city.city_name} - Truy cập toàn thành phố`,
              })
            }
          })
        }
      } else if (access.startsWith('store:')) {
        const storeId = access.replace('store:', '')
        const store = stores?.find((s) => s.id === storeId)
        if (store) {
          const brand = brands?.find((b) => b.id === store.brand_uid)
          const city = cities?.find((c) => c.id === store.city_uid)
          if (brand && city) {
            summary.push({
              type: 'store',
              brandName: brand.brand_name,
              cityName: city.city_name,
              storeName: store.store_name,
              accessLevel: `${city.city_name} - ${store.store_name}`,
            })
          }
        }
      }
    })

    return summary
  }, [userData.brand_access, brands, cities, stores])

  const tablesSummary = useMemo(() => {
    if (!userData.user_permissions?.tables) {
      return []
    }

    const summary: Array<{
      storeId: string
      storeName: string
      tableCount: number
    }> = []

    Object.entries(userData.user_permissions.tables).forEach(
      ([storeId, tableIds]) => {
        if (Array.isArray(tableIds) && tableIds.length > 0) {
          const store = stores?.find((s) => s.id === storeId)
          if (store) {
            summary.push({
              storeId,
              storeName: store.store_name,
              tableCount: tableIds.length,
            })
          }
        }
      }
    )

    return summary
  }, [userData.user_permissions?.tables, stores])

  if (brandAccessSummary.length === 0 && tablesSummary.length === 0) {
    return null
  }

  return (
    <div className='space-y-4'>
      <div className='border-b pb-2'>
        <h3 className='text-base font-medium text-gray-900'>
          Quyền truy cập hiện tại
        </h3>
        <p className='text-sm text-gray-500'>
          Thông tin thương hiệu và bàn mà nhân viên đã được phân quyền
        </p>
      </div>

      <SelectedItemsSummary userData={userData} />

      {/* Tables Summary */}
      {tablesSummary.length > 0 && (
        <div className='rounded-md border bg-blue-50'>
          <div className='border-b bg-blue-100 p-3'>
            <h4 className='text-sm font-semibold text-blue-800'>
              Bàn được phân công ({tablesSummary.length} cửa hàng)
            </h4>
          </div>
          <div className='space-y-0'>
            {tablesSummary.map((item, index) => (
              <div
                key={index}
                className='grid grid-cols-2 border-b bg-white p-3 last:border-b-0'
              >
                <div className='text-sm font-medium text-gray-700'>
                  {item.storeName}
                </div>
                <div className='text-sm text-gray-600'>
                  {item.tableCount} bàn
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}
