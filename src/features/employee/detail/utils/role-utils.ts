import type { Role } from '@/types/role'

export function isOwnerRole(role?: Role): boolean {
  if (!role) return false
  return role.role_id === 'OWNER'
}

export function needsStoreSelection(role?: Role): boolean {
  if (!role) return false
  // These roles need to select a specific store
  return ['CASHIER', 'ORDER'].includes(role.role_id)
}

export function needsTableSelection(role?: Role): boolean {
  if (!role) return false
  // Order staff needs to select tables
  return role.role_id === 'ORDER'
}

export function getBrandIdFromStore(
  storeAccess: string,
  stores?: Array<{ id: string; brand_uid: string }>
): string {
  if (!stores) return ''

  const storeId = storeAccess.replace('store:', '')
  const store = stores.find((s) => s.id === storeId)
  return store?.brand_uid || ''
}
