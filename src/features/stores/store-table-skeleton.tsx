import { Skeleton } from '@/components/ui/skeleton'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'

export function StoreTableHeader() {
  return (
    <TableHeader>
      <TableRow>
        <TableHead className='w-[50px]'>#</TableHead>
        <TableHead>Pos ID</TableHead>
        <TableHead>Tên</TableHead>
        <TableHead>Địa điểm</TableHead>
        <TableHead>Số điện thoại</TableHead>
        <TableHead>Email</TableHead>
        <TableHead>Thời hạn bản quyền</TableHead>
        <TableHead>Loại hình nhà hàng</TableHead>
        <TableHead className='text-right'>Hành động</TableHead>
      </TableRow>
    </TableHeader>
  )
}

export function StoreTableSkeleton() {
  return (
    <div className='rounded-md border'>
      <Table>
        <StoreTableHeader />
        <TableBody>
          {Array.from({ length: 5 }).map((_, index) => (
            <TableRow key={index}>
              <TableCell>
                <Skeleton className='h-4 w-8' />
              </TableCell>
              <TableCell>
                <Skeleton className='h-4 w-16' />
              </TableCell>
              <TableCell>
                <div className='space-y-1'>
                  <Skeleton className='h-4 w-32' />
                  <Skeleton className='h-3 w-24' />
                </div>
              </TableCell>
              <TableCell>
                <div className='space-y-1'>
                  <Skeleton className='h-3 w-20' />
                  <Skeleton className='h-3 w-40' />
                </div>
              </TableCell>
              <TableCell>
                <Skeleton className='h-4 w-24' />
              </TableCell>
              <TableCell>
                <Skeleton className='h-4 w-32' />
              </TableCell>
              <TableCell>
                <Skeleton className='h-4 w-28' />
              </TableCell>
              <TableCell>
                <Skeleton className='h-6 w-16' />
              </TableCell>
              <TableCell>
                <Skeleton className='h-8 w-20' />
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  )
}

export function StoreTableEmpty({
  searchQuery,
  cityFilter,
  statusFilter,
}: {
  searchQuery: string
  cityFilter: string
  statusFilter: string
}) {
  return (
    <div className='rounded-md border'>
      <Table>
        <StoreTableHeader />
        <TableBody>
          <TableRow>
            <TableCell colSpan={9} className='py-8 text-center'>
              <div className='text-muted-foreground'>
                {searchQuery || cityFilter !== 'all' || statusFilter !== 'all'
                  ? 'Không tìm thấy nhà hàng nào phù hợp với bộ lọc'
                  : 'Chưa có nhà hàng nào'}
              </div>
            </TableCell>
          </TableRow>
        </TableBody>
      </Table>
    </div>
  )
}
