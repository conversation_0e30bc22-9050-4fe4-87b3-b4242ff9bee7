import { MapPin, Phone, Mail, ExternalLink } from 'lucide-react'
import { ApiStore } from '@/lib/stores-api'
import { Button } from '@/components/ui/button'
import { TableCell, TableRow } from '@/components/ui/table'
import { formatDate, getStatusBadge } from './stores-utils'

interface StoreTableRowProps {
  store: ApiStore
  index: number
}

export function StoreTableRow({ store, index }: StoreTableRowProps) {
  return (
    <TableRow key={store.id}>
      <TableCell className='font-medium'>{index + 1}</TableCell>
      <TableCell className='font-mono'>{store.fb_store_id}</TableCell>
      <TableCell>
        <div className='flex flex-col'>
          <span className='font-medium'>{store.store_name}</span>
          <span className='text-muted-foreground text-sm'>
            {store.store_id}
          </span>
        </div>
      </TableCell>
      <TableCell>
        <div className='flex items-center gap-1'>
          <MapPin className='text-muted-foreground h-3 w-3' />
          <span className='text-sm'>{store.city_name}</span>
        </div>
        <div className='text-muted-foreground mt-1 max-w-[200px] truncate text-xs'>
          {store.address}
        </div>
      </TableCell>
      <TableCell>
        <div className='flex items-center gap-1'>
          <Phone className='text-muted-foreground h-3 w-3' />
          <span className='text-sm'>{store.phone || '-'}</span>
        </div>
      </TableCell>
      <TableCell>
        <div className='flex items-center gap-1'>
          <Mail className='text-muted-foreground h-3 w-3' />
          <span className='text-sm'>{store.email || '-'}</span>
        </div>
      </TableCell>
      <TableCell>
        <span className='text-sm'>{formatDate(store.expiry_date)}</span>
      </TableCell>
      <TableCell>{getStatusBadge(store)}</TableCell>
      <TableCell className='text-right'>
        <Button variant='outline' size='sm'>
          <ExternalLink className='mr-1 h-3 w-3' />
          Chi tiết
        </Button>
      </TableCell>
    </TableRow>
  )
}
