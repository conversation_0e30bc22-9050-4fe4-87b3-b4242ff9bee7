import type { ApiStore } from '@/lib/stores-api'
import { Badge } from '@/components/ui/badge'

/**
 * Format timestamp to Vietnamese date format
 */
export const formatDate = (timestamp: number) => {
  return new Date(timestamp * 1000).toLocaleDateString('vi-VN', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  })
}

/**
 * Get status badge for store
 */
export const getStatusBadge = (store: ApiStore) => {
  const isActive = store.active === 1
  return (
    <Badge variant={isActive ? 'default' : 'secondary'}>
      {isActive ? 'Active' : 'Inactive'}
    </Badge>
  )
}

/**
 * Search stores by query
 */
export const searchStores = (stores: ApiStore[], query: string) => {
  const searchTerm = query.toLowerCase()
  return stores.filter(
    (store) =>
      store.store_name.toLowerCase().includes(searchTerm) ||
      store.store_id.toLowerCase().includes(searchTerm) ||
      store.address.toLowerCase().includes(searchTerm) ||
      store.phone.includes(searchTerm)
  )
}

/**
 * Get store statistics
 */
export const getStoreStats = (stores: ApiStore[], cities: string[]) => {
  const activeStores = stores.filter((store) => store.active === 1)
  return {
    total: stores.length,
    active: activeStores.length,
    inactive: stores.length - activeStores.length,
    cities: cities.length,
  }
}
