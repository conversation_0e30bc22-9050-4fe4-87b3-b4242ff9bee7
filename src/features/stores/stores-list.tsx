import React from 'react'
import { Search, Plus, RefreshCw } from 'lucide-react'
import { useCurrentBrand, usePosStores } from '@/stores/posStore'
import { useAllStores } from '@/hooks/use-all-stores'
import { Button } from '@/components/ui/button'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Table, TableBody } from '@/components/ui/table'
import { TablePagination } from '@/components/table-pagination'
import { StoreTableRow } from './store-table-row'
import {
  StoreTableHeader,
  StoreTableSkeleton,
  StoreTableEmpty,
} from './store-table-skeleton'
import { searchStores, getStoreStats } from './stores-utils'

export function StoresList() {
  const { selectedBrand } = useCurrentBrand()
  const { currentBrandApiStores } = usePosStores()
  const {
    stores: allStoresFromAPI,
    isLoading: isLoadingAPI,
    error: apiError,
    refetch,
  } = useAllStores()

  const [searchQuery, setSearchQuery] = React.useState('')
  const [cityFilter, setCityFilter] = React.useState<string>('all')
  const [statusFilter, setStatusFilter] = React.useState<string>('all')

  const allStores =
    allStoresFromAPI.length > 0 ? allStoresFromAPI : currentBrandApiStores
  const isLoading = isLoadingAPI
  const error = apiError ? (apiError as Error).message : null

  // Filter stores based on search and filters
  const filteredStores = React.useMemo(() => {
    let stores = allStores

    // Search filter
    if (searchQuery.trim()) {
      stores = searchStores(allStores, searchQuery)
    }

    // City filter
    if (cityFilter !== 'all') {
      stores = stores.filter((store) => store.city_name === cityFilter)
    }

    // Status filter
    if (statusFilter !== 'all') {
      const isActive = statusFilter === 'active'
      stores = stores.filter((store) => (store.active === 1) === isActive)
    }

    return stores
  }, [allStores, searchQuery, cityFilter, statusFilter])

  // Get unique cities for filter
  const cities = React.useMemo(() => {
    return [...new Set(allStores.map((store) => store.city_name))].sort()
  }, [allStores])

  // Get statistics using utility
  const stats = React.useMemo(() => {
    return getStoreStats(allStores, cities)
  }, [allStores, cities])

  if (!selectedBrand) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Danh sách nhà hàng</CardTitle>
          <CardDescription>
            Vui lòng chọn thương hiệu để xem danh sách nhà hàng
          </CardDescription>
        </CardHeader>
      </Card>
    )
  }

  return (
    <div className='container mx-auto space-y-6 py-6'>
      {/* Header */}
      <div className='flex items-center justify-between'>
        <div>
          <h1 className='text-3xl font-bold tracking-tight'>
            Danh sách nhà hàng
          </h1>
          <p className='text-muted-foreground'>
            Quản lý danh sách nhà hàng của thương hiệu {selectedBrand.name}
          </p>
          <p className='text-muted-foreground mt-1 text-xs'>
            Hiển thị {allStores.length} nhà hàng
          </p>
          {error && <p className='mt-1 text-sm text-red-600'>{error}</p>}
        </div>
        <div className='flex gap-2'>
          <Button
            variant='outline'
            onClick={() => refetch()}
            disabled={isLoading}
          >
            <RefreshCw
              className={`mr-2 h-4 w-4 ${isLoading ? 'animate-spin' : ''}`}
            />
            Làm mới
          </Button>
          <Button>
            <Plus className='mr-2 h-4 w-4' />
            Tạo nhà hàng mới
          </Button>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className='grid gap-4 md:grid-cols-4'>
        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>
              Tổng số nhà hàng
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>{stats.total}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>
              Đang hoạt động
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold text-green-600'>
              {stats.active}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>
              Ngừng hoạt động
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold text-red-600'>
              {stats.inactive}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>Thành phố</CardTitle>
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>{stats.cities}</div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Tìm kiếm và lọc</CardTitle>
        </CardHeader>
        <CardContent>
          <div className='flex flex-col gap-4 md:flex-row'>
            <div className='flex-1'>
              <div className='relative'>
                <Search className='text-muted-foreground absolute top-3 left-3 h-4 w-4' />
                <Input
                  placeholder='Tìm kiếm theo tên, mã, địa chỉ, số điện thoại...'
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className='pl-10'
                />
              </div>
            </div>
            <Select value={cityFilter} onValueChange={setCityFilter}>
              <SelectTrigger className='w-[200px]'>
                <SelectValue placeholder='Tất cả thành phố' />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value='all'>Tất cả thành phố</SelectItem>
                {cities.map((city) => (
                  <SelectItem key={city} value={city}>
                    {city}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className='w-[200px]'>
                <SelectValue placeholder='Trạng thái' />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value='all'>Tất cả trạng thái</SelectItem>
                <SelectItem value='active'>Đang hoạt động</SelectItem>
                <SelectItem value='inactive'>Ngừng hoạt động</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Stores Table */}
      <Card>
        <CardHeader>
          <CardTitle>Danh sách nhà hàng ({filteredStores.length})</CardTitle>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <StoreTableSkeleton />
          ) : filteredStores.length === 0 ? (
            <StoreTableEmpty
              searchQuery={searchQuery}
              cityFilter={cityFilter}
              statusFilter={statusFilter}
            />
          ) : (
            <TablePagination data={filteredStores} pageSize={20}>
              {(paginatedStores, pagination) => (
                <div className='space-y-4'>
                  <div className='rounded-md border'>
                    <Table>
                      <StoreTableHeader />
                      <TableBody>
                        {paginatedStores.map((store, index) => (
                          <StoreTableRow
                            key={store.id}
                            store={store}
                            index={pagination.startIndex + index}
                          />
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                </div>
              )}
            </TablePagination>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
