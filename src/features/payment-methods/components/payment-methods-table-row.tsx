import { useState } from 'react'

import { useNavigate } from '@tanstack/react-router'

import type { PaymentMethod } from '@/types/payment-method'
import { Trash2, MoreHorizontal } from 'lucide-react'

import { useAuthStore } from '@/stores/authStore'

import { useDeletePaymentMethod } from '@/hooks/api/use-payment-methods'

import { Button } from '@/components/ui/button'
import { TableCell, TableRow } from '@/components/ui/table'

import { ConfirmModal } from '@/components/pos'

import { PaymentMethodStoresModal } from './payment-method-stores-modal'

interface PaymentMethodsTableRowProps {
  paymentMethod: PaymentMethod
  index: number
}

export function PaymentMethodsTableRow({ paymentMethod, index }: PaymentMethodsTableRowProps) {
  const navigate = useNavigate()
  const { company, brands } = useAuthStore(state => state.auth)
  const selectedBrand = brands?.[0]

  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [storesModalOpen, setStoresModalOpen] = useState(false)
  const { deletePaymentMethod, isDeleting } = useDeletePaymentMethod()

  const handleDelete = () => {
    deletePaymentMethod(paymentMethod.id)
    setDeleteDialogOpen(false)
  }

  const formatFeePercentage = (fee: number) => {
    return fee > 0 ? `${(fee * 100).toFixed(0)}` : '0'
  }

  const formatStoreCount = (count: number) => {
    return count > 0 ? `${count} cửa hàng` : 'Chưa áp dụng'
  }

  const handleStoresClick = () => {
    setStoresModalOpen(true)
  }

  const handleRowClick = () => {
    const companyUid = company?.id || ''
    const brandUid = selectedBrand?.id || ''

    console.log('Row click data:', {
      paymentMethodId: paymentMethod.id,
      paymentMethodCode: paymentMethod.code,
      companyUid,
      brandUid
    })

    if (companyUid && brandUid) {
      navigate({
        to: '/setting/payment-method/detail/$paymentMethodId',
        params: { paymentMethodId: paymentMethod.id },
        search: {
          company_uid: companyUid,
          brand_uid: brandUid,
          payment_method_id: paymentMethod.code.toString() // Ensure string
        }
      })
    }
  }

  return (
    <TableRow className='cursor-pointer hover:bg-gray-50' onClick={handleRowClick}>
      <TableCell className='font-medium'>{index + 1}</TableCell>
      <TableCell className='font-mono text-sm'>{paymentMethod.code}</TableCell>
      <TableCell>
        <div className='flex flex-col'>
          <span className='font-medium'>{paymentMethod.name}</span>
          {paymentMethod.description && (
            <span className='text-muted-foreground text-sm'>{paymentMethod.description}</span>
          )}
        </div>
      </TableCell>
      <TableCell className='text-center'>
        <span className='font-medium'>{formatFeePercentage(paymentMethod.cardProcessingFee)}</span>
      </TableCell>
      <TableCell>
        <div className='flex items-center gap-2'>
          <span className='text-sm'>{formatStoreCount(paymentMethod.storeCount)}</span>
          {paymentMethod.storeCount > 0 && (
            <Button
              variant='ghost'
              size='sm'
              className='h-6 w-6 p-0'
              onClick={e => {
                e.stopPropagation() // Prevent row click
                handleStoresClick()
              }}
            >
              <MoreHorizontal className='h-4 w-4' />
            </Button>
          )}
        </div>
      </TableCell>
      <TableCell className='text-right'>
        <Button
          variant='ghost'
          size='sm'
          className='h-8 w-8 p-0 text-red-600 hover:bg-red-50 hover:text-red-700'
          onClick={e => {
            e.stopPropagation() // Prevent row click
            setDeleteDialogOpen(true)
          }}
        >
          <Trash2 className='h-4 w-4' />
        </Button>

        {/* Confirm Delete Modal */}
        <ConfirmModal
          open={deleteDialogOpen}
          onOpenChange={setDeleteDialogOpen}
          title='Xác nhận xóa'
          content={`Bạn có chắc chắn muốn xóa phương thức thanh toán "${paymentMethod.name}"? Hành động này không thể hoàn tác.`}
          onConfirm={handleDelete}
          confirmText='Xóa'
          cancelText='Hủy'
          isLoading={isDeleting}
        />

        {/* Stores Modal */}
        <PaymentMethodStoresModal
          open={storesModalOpen}
          onOpenChange={setStoresModalOpen}
          paymentMethodId={paymentMethod.id}
          paymentMethodName={paymentMethod.name}
          storeUids={paymentMethod.storeUids}
        />
      </TableCell>
    </TableRow>
  )
}
