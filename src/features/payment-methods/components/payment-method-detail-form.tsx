import { useState, useEffect } from 'react'

import { useNavigate } from '@tanstack/react-router'

import { X } from 'lucide-react'

import { usePaymentMethodDetail, useUpdatePaymentMethod } from '@/hooks/api/use-payment-methods'

import { Button } from '@/components/ui/button'
import { Checkbox } from '@/components/ui/checkbox'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select'

import { StoreSelectionModal } from './store-selection-modal'

interface PaymentMethodDetailFormProps {
  paymentMethodId: string
  companyUid: string
  brandUid: string
  paymentMethodCode: string
}

export function PaymentMethodDetailForm({
  paymentMethodId,
  companyUid,
  brandUid,
  paymentMethodCode
}: PaymentMethodDetailFormProps) {
  const navigate = useNavigate()
  const { data: paymentMethodDetail, isLoading, error } = usePaymentMethodDetail(paymentMethodCode)
  const { updatePaymentMethod, isUpdating } = useUpdatePaymentMethod()

  // Debug logging
  console.log('PaymentMethodDetailForm props:', {
    paymentMethodId,
    companyUid,
    brandUid,
    paymentMethodCode
  })
  console.log('API Response:', { paymentMethodDetail, isLoading, error })

  const [formData, setFormData] = useState({
    name: '',
    code: '',
    autoGenerateCode: true,
    cardProcessingFee: '0',
    feeBearer: 'customer', // 'customer' | 'restaurant'
    requireTransactionCode: false,
    selectedStores: [] as string[],
    logoFile: null as File | null,
    logoPreview: '' as string
  })

  const [showStoreModal, setShowStoreModal] = useState(false)

  // Populate form data when API data is loaded
  useEffect(() => {
    if (paymentMethodDetail) {
      const data = paymentMethodDetail
      setFormData({
        name: data.payment_method_name || '',
        code: data.payment_method_id || '',
        autoGenerateCode: false, // Since we have existing code
        cardProcessingFee: ((data.payment_fee_extra || 0) * 100).toString(), // Convert decimal to percentage
        feeBearer: data.payment_fee_type === 0 ? 'customer' : 'restaurant',
        requireTransactionCode: data.extra_data?.require_traceno === 1,
        selectedStores: data.stores?.map((store: any) => store.id) || [],
        logoFile: null,
        logoPreview: data.image_path || ''
      })
    }
  }, [paymentMethodDetail])

  const handleBack = () => {
    navigate({ to: '/setting/payment-method' })
  }

  const isFormValid = formData.name.trim() !== '' && formData.selectedStores.length > 0

  const handleSave = async () => {
    if (!isFormValid) return

    const updateData = {
      id: paymentMethodId,
      payment_method_name: formData.name,
      payment_method_id: formData.code,
      payment_fee_extra: parseFloat(formData.cardProcessingFee) / 100, // Convert percentage to decimal
      payment_fee_type: formData.feeBearer === 'customer' ? 0 : 1,
      stores: formData.selectedStores,
      extra_data: formData.requireTransactionCode ? { require_traceno: 1 } : { require_traceno: 0 },
      logoFile: formData.logoFile
    }

    updatePaymentMethod(updateData, {
      onSuccess: () => {
        navigate({ to: '/setting/payment-method' })
      }
    })
  }

  const handleStoreSelection = () => {
    setShowStoreModal(true)
  }

  const handleStoreSelectionChange = (storeIds: string[]) => {
    setFormData({ ...formData, selectedStores: storeIds })
  }

  const handleLogoUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      const reader = new FileReader()
      reader.onload = event => {
        const preview = event.target?.result as string
        setFormData({
          ...formData,
          logoFile: file,
          logoPreview: preview
        })
      }
      reader.readAsDataURL(file)
    }
  }

  const handleRemoveLogo = () => {
    setFormData({
      ...formData,
      logoFile: null,
      logoPreview: ''
    })
  }

  if (isLoading) {
    return (
      <div className='container mx-auto px-4 py-8'>
        <div className='flex h-64 items-center justify-center'>
          <div className='text-lg'>Đang tải...</div>
        </div>
      </div>
    )
  }

  return (
    <div className='container mx-auto px-4 py-8'>
      {/* Header */}
      <div className='mb-8 flex items-center justify-between'>
        <Button
          variant='ghost'
          size='sm'
          onClick={handleBack}
          className='flex items-center gap-2 text-gray-600 hover:text-gray-900'
        >
          <X className='h-4 w-4' />
        </Button>

        <h1 className='text-2xl font-bold'>Chi tiết phương thức thanh toán</h1>

        <Button
          type='button'
          disabled={isUpdating || !isFormValid}
          className='min-w-[100px]'
          onClick={handleSave}
        >
          {isUpdating ? 'Đang cập nhật...' : 'Lưu'}
        </Button>
      </div>

      {/* Form Content */}
      <div className='mx-auto max-w-4xl'>
        <div className='rounded-lg border bg-white p-6 shadow-sm'>
          <div className='space-y-6'>
            {/* Section Title */}
            <div>
              <h2 className='text-lg font-medium text-gray-900'>Thông tin chi tiết</h2>
            </div>

            {/* Form Fields */}
            <div className='space-y-6'>
              {/* Tên phương thức */}
              <div className='flex items-center gap-4'>
                <Label htmlFor='payment-method-name' className='min-w-[200px] text-sm font-medium'>
                  Tên phương thức *
                </Label>
                <Input
                  id='payment-method-name'
                  value={formData.name}
                  onChange={e => setFormData({ ...formData, name: e.target.value })}
                  placeholder='Nhập tên phương thức thanh toán'
                  className='flex-1'
                />
              </div>

              {/* Cửa hàng áp dụng */}
              <div className='flex items-center gap-4'>
                <Label className='min-w-[200px] text-sm font-medium'>Cửa hàng áp dụng *</Label>
                <Button
                  type='button'
                  variant='outline'
                  onClick={handleStoreSelection}
                  className='flex-1 justify-start'
                >
                  {formData.selectedStores.length > 0
                    ? `${formData.selectedStores.length} điểm`
                    : '0 điểm'}
                </Button>
              </div>

              {/* Mã PTTT */}
              <div className='flex items-center gap-4'>
                <Label htmlFor='payment-method-code' className='min-w-[200px] text-sm font-medium'>
                  Mã PTTT
                </Label>
                <Input
                  id='payment-method-code'
                  value={formData.code}
                  onChange={e => setFormData({ ...formData, code: e.target.value })}
                  placeholder='Mã phương thức thanh toán'
                  disabled={true}
                  className='flex-1'
                />
              </div>

              {/* Phí cà thẻ */}
              <div className='flex items-center gap-4'>
                <Label htmlFor='card-processing-fee' className='min-w-[200px] text-sm font-medium'>
                  Phí cà thẻ
                </Label>
                <div className='relative flex-1'>
                  <Input
                    id='card-processing-fee'
                    type='number'
                    min='0'
                    max='100'
                    step='0.1'
                    value={formData.cardProcessingFee}
                    onChange={e => setFormData({ ...formData, cardProcessingFee: e.target.value })}
                    className='pr-8'
                  />
                  <span className='absolute top-1/2 right-3 -translate-y-1/2 text-sm text-gray-500'>
                    %
                  </span>
                </div>
              </div>

              {/* Bên chịu phí cà thẻ */}
              <div className='flex items-center gap-4'>
                <Label className='min-w-[200px] text-sm font-medium'>Bên chịu phí cà thẻ</Label>
                <Select
                  value={formData.feeBearer}
                  onValueChange={value => setFormData({ ...formData, feeBearer: value })}
                >
                  <SelectTrigger className='flex-1'>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value='customer'>Khách hàng</SelectItem>
                    <SelectItem value='restaurant'>Nhà hàng</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Yêu cầu nhập mã giao dịch */}
              <div className='flex items-center gap-4'>
                <Label className='min-w-[200px] text-sm font-medium'>
                  Yêu cầu nhập mã giao dịch khi thanh toán
                </Label>
                <div className='flex-1'>
                  <Checkbox
                    id='require-transaction-code'
                    checked={formData.requireTransactionCode}
                    onCheckedChange={checked =>
                      setFormData({ ...formData, requireTransactionCode: checked as boolean })
                    }
                  />
                </div>
              </div>

              {/* Logo Section */}
              <div className='space-y-4'>
                <h2 className='text-lg font-medium text-gray-900'>Logo</h2>

                <div className='flex items-start gap-4'>
                  <Label className='min-w-[200px] pt-2 text-sm font-medium'>Logo tải lên</Label>
                  <div className='flex-1'>
                    {/* Logo Preview */}
                    {formData.logoPreview ? (
                      <div className='space-y-2'>
                        <div className='relative inline-block'>
                          <img
                            src={formData.logoPreview}
                            alt='Logo preview'
                            className='h-24 w-24 rounded-md border object-cover'
                          />
                          <Button
                            type='button'
                            variant='destructive'
                            size='sm'
                            className='absolute -top-2 -right-2 h-6 w-6 rounded-full p-0'
                            onClick={handleRemoveLogo}
                          >
                            ×
                          </Button>
                        </div>
                        <p className='text-muted-foreground text-xs'>{formData.logoFile?.name}</p>
                      </div>
                    ) : (
                      /* Upload Area */
                      <div className='rounded-md border-2 border-dashed border-gray-300 p-6 text-center transition-colors hover:border-gray-400'>
                        <div className='space-y-2'>
                          <div className='mx-auto h-12 w-12 text-gray-400'>
                            <svg fill='none' viewBox='0 0 24 24' stroke='currentColor'>
                              <path
                                strokeLinecap='round'
                                strokeLinejoin='round'
                                strokeWidth={1}
                                d='M12 6v6m0 0v6m0-6h6m-6 0H6'
                              />
                            </svg>
                          </div>
                          <div>
                            <label htmlFor='payment-method-logo' className='cursor-pointer'>
                              <span className='text-sm font-medium text-blue-600 hover:text-blue-500'>
                                Tải ảnh lên
                              </span>
                              <Input
                                id='payment-method-logo'
                                type='file'
                                accept='image/*'
                                onChange={handleLogoUpload}
                                className='hidden'
                              />
                            </label>
                          </div>
                          <p className='text-xs text-gray-500'>PNG, JPG, GIF up to 10MB</p>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Store Selection Modal */}
      <StoreSelectionModal
        open={showStoreModal}
        onOpenChange={setShowStoreModal}
        selectedStoreIds={formData.selectedStores}
        onStoreSelectionChange={handleStoreSelectionChange}
      />
    </div>
  )
}
