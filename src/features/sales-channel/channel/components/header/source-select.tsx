import { FilterDropdown } from '@/components/filter-dropdown'

interface SourceOption {
  id: string
  value: string
  label: string
}

interface SourceSelectProps {
  value: string
  onValueChange: (value: string) => void
  sourceOptions?: SourceOption[]
  isLoading?: boolean
  placeholder?: string
  className?: string
}

export function SourceSelect({
  value,
  onValueChange,
  sourceOptions,
  isLoading,
  placeholder = 'Tất cả nguồn',
  className = 'w-48'
}: SourceSelectProps) {
  const options =
    sourceOptions?.map(source => ({
      value: source.value,
      label: source.label
    })) || []

  return (
    <FilterDropdown
      value={value}
      onValueChange={onValueChange}
      options={options}
      isLoading={isLoading}
      placeholder={placeholder}
      className={className}
      allOptionLabel='Tất cả nguồn'
      loadingText='Đang tải nguồn...'
      emptyText='Không có nguồn'
    />
  )
}
