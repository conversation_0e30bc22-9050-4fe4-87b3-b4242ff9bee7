import { Channel } from '@/types/channels'

import { DeleteChannelButton } from './delete-channel-button'

interface ChannelActionsCellProps {
  channel: Channel
}

export function ChannelActionsCell({ channel }: ChannelActionsCellProps) {
  const handleCellClick = (e: React.MouseEvent) => {
    e.stopPropagation() // Prevent row click when clicking in actions cell
  }

  return (
    <div className='flex items-center justify-end' onClick={handleCellClick}>
      <DeleteChannelButton channel={channel} />
    </div>
  )
}
