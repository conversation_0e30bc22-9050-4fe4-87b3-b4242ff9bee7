import { useState, useEffect } from 'react'

import { HelpCircle } from 'lucide-react'

import {
  Label,
  Input,
  Button,
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger
} from '@/components/ui'

import { ChannelFormData } from '../../data'
import { DAYS_OF_WEEK, TIME_SLOTS } from '../../utils'
import type { FormMode } from '../forms/channel-form'

interface MarketingConfigSectionProps {
  formData: ChannelFormData
  onFormDataChange: (updates: Partial<ChannelFormData>) => void
  mode: FormMode
  isLoading?: boolean
}

export function MarketingConfigSection({
  formData,
  onFormDataChange,
  isLoading = false
}: MarketingConfigSectionProps) {
  const [costType, setCostType] = useState<'amount' | 'percentage'>('amount')

  // Set costType based on formData.voucherCostType when data loads
  useEffect(() => {
    if (formData.voucherCostType) {
      const type = formData.voucherCostType === 'PERCENT' ? 'percentage' : 'amount'
      setCostType(type)
    }
  }, [formData.voucherCostType])

  // Handle percentage input validation
  const handleMarketingCostChange = (value: number) => {
    if (costType === 'percentage' && value > 100) {
      onFormDataChange({ marketingPartnerCost: 100 })
    } else {
      onFormDataChange({ marketingPartnerCost: value })
    }
  }

  // Handle cost type change
  const handleCostTypeChange = (type: 'amount' | 'percentage') => {
    setCostType(type)
    // Update the form data to track cost type
    onFormDataChange({ voucherCostType: type === 'percentage' ? 'PERCENTAGE' : 'AMOUNT' })
  }

  const handleDayToggle = (day: string) => {
    const currentDays = formData.marketingDays || []
    const updatedDays = currentDays.includes(day)
      ? currentDays.filter(d => d !== day)
      : [...currentDays, day]

    onFormDataChange({ marketingDays: updatedDays })
  }

  const handleTimeToggle = (time: string) => {
    const currentTimes = formData.marketingHours || []
    const updatedTimes = currentTimes.includes(time)
      ? currentTimes.filter(t => t !== time)
      : [...currentTimes, time]

    onFormDataChange({ marketingHours: updatedTimes })
  }

  return (
    <TooltipProvider>
      <div className='rounded-lg border p-6'>
        <h2 className='mb-6 text-xl font-semibold'>Cấu hình chi phí đối tác marketing</h2>

        <div className='space-y-6'>
          <div className='flex items-center gap-4'>
            <div className='flex w-[200px] items-center gap-2'>
              <Label htmlFor='marketing-cost' className='text-sm font-medium'>
                {costType === 'amount' ? 'Số tiền/đơn' : 'Phần trăm/đơn'}{' '}
                <span className='text-red-500'>*</span>
              </Label>
              {costType === 'percentage' && (
                <Tooltip>
                  <TooltipTrigger asChild>
                    <HelpCircle className='h-4 w-4 cursor-help text-gray-400' />
                  </TooltipTrigger>
                  <TooltipContent className='max-w-xs'>
                    <p className='text-sm'>Phần trăm chỉ áp dụng với chiết khấu toàn hóa đơn</p>
                  </TooltipContent>
                </Tooltip>
              )}
            </div>
            <div className='flex flex-1 items-center gap-2'>
              <Input
                id='marketing-cost'
                type='number'
                value={formData.marketingPartnerCost}
                onChange={e => handleMarketingCostChange(Number(e.target.value))}
                placeholder='0'
                className='flex-1'
                min='0'
                max={costType === 'percentage' ? 100 : undefined}
                disabled={isLoading}
              />
              <div className='flex rounded-md border'>
                <Button
                  type='button'
                  variant={costType === 'amount' ? 'default' : 'ghost'}
                  size='sm'
                  onClick={() => handleCostTypeChange('amount')}
                  disabled={isLoading}
                  className='rounded-r-none border-r px-3 py-1 text-sm'
                >
                  đ
                </Button>
                <Button
                  type='button'
                  variant={costType === 'percentage' ? 'default' : 'ghost'}
                  size='sm'
                  onClick={() => handleCostTypeChange('percentage')}
                  disabled={isLoading}
                  className='rounded-l-none px-3 py-1 text-sm'
                >
                  %
                </Button>
              </div>
            </div>
          </div>

          <div className='flex items-center gap-4'>
            <div className='flex w-[200px] items-center gap-2'>
              <Label className='text-sm font-medium'>Voucher cho chương trình</Label>
              <Tooltip>
                <TooltipTrigger asChild>
                  <HelpCircle className='h-4 w-4 cursor-help text-gray-400' />
                </TooltipTrigger>
                <TooltipContent className='max-w-xs'>
                  <p className='text-sm'>
                    Nếu có cấu hình voucher, POS sẽ yêu cầu phải nhập voucher mới được thanh toán!
                  </p>
                </TooltipContent>
              </Tooltip>
            </div>
            <Input
              value={formData.voucherRunPartner}
              onChange={e => onFormDataChange({ voucherRunPartner: e.target.value })}
              placeholder='Nhập voucher'
              className='flex-1'
              disabled={isLoading}
            />
          </div>

          <div className='space-y-4'>
            <Label className='text-sm font-medium'>Ngày áp dụng hỗ trợ marketing</Label>
            <div className='flex gap-4'>
              <div className='flex flex-1 items-center gap-2'>
                <Label className='w-[200px] rounded bg-gray-100 px-3 py-2 text-sm font-medium text-gray-600'>
                  Ngày bắt đầu
                </Label>
                <input
                  type='date'
                  value={formData.marketingStartDate}
                  onChange={e => onFormDataChange({ marketingStartDate: e.target.value })}
                  className='border-input bg-background ring-offset-background placeholder:text-muted-foreground focus-visible:ring-ring flex-1 rounded-md border px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50'
                  style={{ colorScheme: 'light' }}
                  disabled={isLoading}
                />
              </div>
              <div className='flex flex-1 items-center gap-2'>
                <Label className='w-[200px] rounded bg-gray-100 px-3 py-2 text-sm font-medium text-gray-600'>
                  Ngày kết thúc
                </Label>
                <input
                  type='date'
                  value={formData.marketingEndDate}
                  onChange={e => onFormDataChange({ marketingEndDate: e.target.value })}
                  className='border-input bg-background ring-offset-background placeholder:text-muted-foreground focus-visible:ring-ring flex-1 rounded-md border px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50'
                  style={{ colorScheme: 'light' }}
                  disabled={isLoading}
                />
              </div>
            </div>
          </div>

          <div className='space-y-4'>
            <Label className='text-sm font-medium'>Khung thời gian áp dụng hỗ trợ marketing</Label>

            <div className='space-y-4'>
              <div className='flex items-center gap-2'>
                <Label className='text-sm font-medium text-gray-500'>Chọn ngày</Label>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <HelpCircle className='h-4 w-4 cursor-help text-gray-400' />
                  </TooltipTrigger>
                  <TooltipContent className='max-w-xs'>
                    <p className='text-sm'>
                      Nếu để trống mặc định sẽ áp dụng tất cả các ngày trong tuần
                    </p>
                  </TooltipContent>
                </Tooltip>
              </div>

              <div className='flex gap-2'>
                {DAYS_OF_WEEK.map(day => (
                  <Button
                    key={day.value}
                    type='button'
                    variant={formData.marketingDays?.includes(day.value) ? 'default' : 'outline'}
                    size='sm'
                    onClick={() => handleDayToggle(day.value)}
                    disabled={isLoading}
                    className='flex-1'
                  >
                    {day.label}
                  </Button>
                ))}
              </div>
            </div>
          </div>

          <div className='space-y-4'>
            <div className='flex items-center gap-2'>
              <Label className='text-sm font-medium text-gray-500'>Chọn giờ</Label>
              <Tooltip>
                <TooltipTrigger asChild>
                  <HelpCircle className='h-4 w-4 cursor-help text-gray-400' />
                </TooltipTrigger>
                <TooltipContent className='max-w-xs'>
                  <p className='text-sm'>
                    Nếu để trống mặc định sẽ áp dụng tất cả các giờ trong ngày
                  </p>
                </TooltipContent>
              </Tooltip>
            </div>

            <div className='grid grid-cols-6 gap-2'>
              {TIME_SLOTS.map(time => (
                <Button
                  key={time.value}
                  type='button'
                  variant={formData.marketingHours?.includes(time.value) ? 'default' : 'outline'}
                  size='sm'
                  onClick={() => handleTimeToggle(time.value)}
                  disabled={isLoading}
                  className='text-xs'
                >
                  {time.value}
                </Button>
              ))}
            </div>
          </div>
        </div>
      </div>
    </TooltipProvider>
  )
}
