import { useEffect, useState } from 'react'

import { HelpCircle } from 'lucide-react'

import { useFilteredPaymentMethodsData } from '@/hooks/api/use-payment-methods'

import {
  Label,
  Input,
  Checkbox,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger
} from '@/components/ui'

import type { ChannelFormData } from '../../data'
import type { FormMode } from '../forms/channel-form'

interface ChannelConfigSectionProps {
  formData: ChannelFormData
  onFormDataChange: (updates: Partial<ChannelFormData>) => void
  mode: FormMode
  isLoading?: boolean
}

export function ChannelConfigSection({
  formData,
  onFormDataChange,
  mode,
  isLoading = false
}: ChannelConfigSectionProps) {
  const [prevStoreId, setPrevStoreId] = useState(formData.storeId)

  // Only fetch payment methods in add mode (edit mode uses channel data)
  const { data: paymentMethods, isLoading: paymentMethodsLoading } = useFilteredPaymentMethodsData({
    storeUid: mode === 'add' ? formData.storeId || undefined : undefined
  })

  // Reset selected payment method when store changes (only in add mode)
  useEffect(() => {
    // Only reset if storeId actually changed and not in edit mode
    if (prevStoreId !== formData.storeId && mode === 'add') {
      if (formData.selectedPaymentMethodId) {
        onFormDataChange({ selectedPaymentMethodId: '' })
      }
      setPrevStoreId(formData.storeId)
    } else if (prevStoreId !== formData.storeId) {
      // Just update the state in edit mode without resetting
      setPrevStoreId(formData.storeId)
    }
  }, [formData.storeId, formData.selectedPaymentMethodId, onFormDataChange, mode, prevStoreId])

  // Check if payment method fields should be shown
  const showPaymentFields =
    formData.paymentMethodId === 'prepaid' || formData.paymentMethodId === 'postpaid'
  const showPaymentMethodSelect = formData.paymentMethodId === 'postpaid'

  // In edit mode, show payment method select immediately if we have selectedPaymentMethodId
  const shouldShowPaymentMethodSelectImmediate =
    showPaymentMethodSelect &&
    mode === 'edit' &&
    formData.selectedPaymentMethodId &&
    formData.storeId

  // Handle percentage validation for commission rate
  const handleCommissionRateChange = (value: number) => {
    if (value > 100) {
      onFormDataChange({ commissionRate: 100 })
    } else {
      onFormDataChange({ commissionRate: value })
    }
  }

  // Handle percentage validation for deduct tax rate
  const handleDeductTaxRateChange = (value: number) => {
    if (value > 100) {
      onFormDataChange({ deductTaxRate: 100 })
    } else {
      onFormDataChange({ deductTaxRate: value })
    }
  }
  return (
    <TooltipProvider>
      <div className='rounded-lg border p-6'>
        <h2 className='mb-6 text-xl font-semibold'>Cấu hình kênh bán hàng</h2>

        <div className='space-y-6'>
          <div className='flex items-center gap-4'>
            <Label htmlFor='commission-rate' className='min-w-[200px] text-sm font-medium'>
              Phần trăm hoa hồng <span className='text-red-500'>*</span>
            </Label>
            <div className='flex flex-1 items-center gap-2'>
              <Input
                id='commission-rate'
                type='number'
                value={formData.commissionRate}
                onChange={e => handleCommissionRateChange(Number(e.target.value))}
                placeholder='0'
                className='flex-1'
                min='0'
                max='100'
                disabled={isLoading}
              />
              <span className='text-sm text-gray-500'>%</span>
            </div>
          </div>

          <div className='flex items-center gap-4'>
            <Label htmlFor='deduct-tax-rate' className='min-w-[200px] text-sm font-medium'>
              Cấu hình thuế khấu trừ <span className='text-red-500'>*</span>
            </Label>
            <div className='flex flex-1 items-center gap-2'>
              <Input
                id='deduct-tax-rate'
                type='number'
                value={formData.deductTaxRate}
                onChange={e => handleDeductTaxRateChange(Number(e.target.value))}
                placeholder='0'
                className='flex-1'
                min='0'
                max='100'
                disabled={isLoading}
              />
              <span className='text-sm text-gray-500'>%</span>
            </div>
          </div>

          <div className='flex items-start gap-4'>
            <div className='flex w-[200px] items-center gap-2'>
              <Label className='flex-1 text-sm leading-relaxed font-medium'>
                Sử dụng hóa đơn dành cho khách hàng khi in
              </Label>
              <Tooltip>
                <TooltipTrigger asChild>
                  <HelpCircle className='h-4 w-4 flex-shrink-0 cursor-help text-gray-400' />
                </TooltipTrigger>
                <TooltipContent className='max-w-xs'>
                  <p className='text-sm'>
                    Khi thanh toán POS sẽ không hiển thị tiền hoa hồng, tiền đối tác hỗ trợ vi vậy
                    tổng tiền trên hóa đơn khi in sẽ khác so với tổng tiền ghi nhận trên hệ thống.
                    Áp dụng cho trường hợp trả hóa đơn cho khách mua hàng!
                  </p>
                </TooltipContent>
              </Tooltip>
            </div>
            <div className='mt-0.5 flex-1'>
              <Select
                value={formData.notShowPartnerBill}
                onValueChange={value => onFormDataChange({ notShowPartnerBill: value })}
                disabled={isLoading}
              >
                <SelectTrigger className='w-full'>
                  <SelectValue placeholder='Chọn loại hóa đơn' />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value='0'>Không sử dụng hóa đơn dành cho khách khi in</SelectItem>
                  <SelectItem value='1'>Sử dụng hóa đơn dành cho khách khi in</SelectItem>
                  <SelectItem value='2'>In hóa đơn chỉ với phần giảm giá nhà hàng</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className='space-y-6'>
            <div className='flex items-center gap-4'>
              <Label htmlFor='apply-online-order' className='min-w-[200px] text-sm font-medium'>
                Áp dụng đơn online
              </Label>
              <Checkbox
                id='apply-online-order'
                checked={formData.useOnlineOrder}
                onCheckedChange={checked =>
                  onFormDataChange({ useOnlineOrder: checked as boolean })
                }
                disabled={isLoading}
              />
            </div>

            <div className='flex items-center gap-4'>
              <Label htmlFor='exclude-shipping' className='w-[200px] text-sm font-medium'>
                Không tính phí vận chuyển vào doanh thu (áp dụng đơn online)
              </Label>
              <Checkbox
                id='exclude-shipping'
                checked={formData.excludeShipping}
                onCheckedChange={checked =>
                  onFormDataChange({ excludeShipping: checked as boolean })
                }
                disabled={isLoading}
              />
            </div>
          </div>

          <div className='flex items-center gap-4'>
            <Label className='min-w-[200px] text-sm font-medium'>
              Hình thức thanh toán <span className='text-red-500'>*</span>
            </Label>
            <Select
              value={formData.paymentMethodId}
              onValueChange={value => onFormDataChange({ paymentMethodId: value })}
              disabled={isLoading}
            >
              <SelectTrigger className='flex-1'>
                <SelectValue placeholder='Chọn hình thức thanh toán' />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value='prepaid'>Trả trước</SelectItem>
                <SelectItem value='postpaid'>Trả sau</SelectItem>
              </SelectContent>
            </Select>
          </div>
          {(showPaymentMethodSelect || shouldShowPaymentMethodSelectImmediate) && (
            <div className='flex items-center gap-4'>
              <Label className='min-w-[200px] text-sm font-medium'>
                Phương thức thanh toán <span className='text-red-500'>*</span>
              </Label>
              {mode === 'edit' ? (
                // In edit mode, display the payment method name from channel extra_data
                <div className='border-input bg-background flex-1 rounded-md border px-3 py-2 text-sm'>
                  {formData.selectedPaymentMethodId || 'Chưa có thông tin'}
                </div>
              ) : (
                // In add mode, show the dropdown with API data
                <Select
                  key={`payment-method-${formData.storeId}`} // Force re-render when store changes
                  value={formData.selectedPaymentMethodId?.trim()}
                  onValueChange={value => onFormDataChange({ selectedPaymentMethodId: value })}
                  disabled={isLoading || !formData.storeId || paymentMethodsLoading}
                >
                  <SelectTrigger className='flex-1'>
                    <SelectValue
                      placeholder={
                        !formData.storeId
                          ? 'Vui lòng chọn cửa hàng trước'
                          : paymentMethodsLoading
                            ? 'Đang tải...'
                            : 'Chọn phương thức thanh toán'
                      }
                    />
                  </SelectTrigger>
                  <SelectContent>
                    {paymentMethods?.map(method => (
                      <SelectItem key={method.id} value={method.name.trim()}>
                        {method.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}
            </div>
          )}

          {showPaymentFields && (
            <div className='flex items-center gap-4'>
              <Label className='min-w-[200px] text-sm font-medium'>Nhập số hóa đơn đối tác</Label>
              <Checkbox
                checked={formData.requirePartnerInvoice}
                onCheckedChange={checked =>
                  onFormDataChange({ requirePartnerInvoice: checked as boolean })
                }
                disabled={isLoading}
              />
            </div>
          )}
        </div>
      </div>
    </TooltipProvider>
  )
}
