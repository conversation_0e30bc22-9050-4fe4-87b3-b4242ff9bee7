import type { ChannelFormData } from '../../data'
import { BasicInfoSection } from '../sections/basic-info-section'
import { ChannelConfigSection } from '../sections/channel-config-section'
import { MarketingConfigSection } from '../sections/marketing-config-section'

export type FormMode = 'add' | 'edit'

interface ChannelFormProps {
  formData: ChannelFormData
  onFormDataChange: (updates: Partial<ChannelFormData>) => void
  mode: FormMode
  isLoading?: boolean
}

export function ChannelForm({
  formData,
  onFormDataChange,
  mode,
  isLoading = false
}: ChannelFormProps) {
  return (
    <div className='space-y-8'>
      <BasicInfoSection
        formData={formData}
        onFormDataChange={onFormDataChange}
        mode={mode}
        isLoading={isLoading}
      />

      <ChannelConfigSection
        formData={formData}
        onFormDataChange={onFormDataChange}
        mode={mode}
        isLoading={isLoading}
      />

      <MarketingConfigSection
        formData={formData}
        onFormDataChange={onFormDataChange}
        mode={mode}
        isLoading={isLoading}
      />
    </div>
  )
}
