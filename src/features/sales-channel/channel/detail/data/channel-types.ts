export interface ChannelFormData {
  // Thông tin cơ bản
  sourceName: string[]
  description: string

  // C<PERSON>u hình kênh bán hàng
  commissionRate: number
  excludeShipping: boolean
  paymentType: string
  deductTaxRate: number
  requireTransactionNumber: boolean
  useOnlineOrder: boolean
  paymentMethodId: string // Hình thức thanh toán: 'prepaid' | 'postpaid'
  voucherRunPartner: string
  notShowPartnerBill: string // '0' | '1' | '2' - 0: Không sử dụng, 1: Sử dụng hóa đơn dành cho khách, 2: In hóa đơn chỉ với phần giảm giá nhà hàng

  // Thêm fields mới cho payment
  requirePartnerInvoice: boolean // Checkbox "Nhập số hóa đơn đối tác"
  selectedPaymentMethodId: string // Phương thức thanh toán (khi chọn trả sau)

  // C<PERSON><PERSON> hình chi phí đối tác marketing
  marketingPartnerCost: number
  voucherCostType: string
  marketingStartDate: string
  marketingEndDate: string
  marketingDays: string[]
  marketingHours: string[]

  // Store selection
  storeId: string
}

export const initialChannelFormData: ChannelFormData = {
  sourceName: [],
  description: '',
  commissionRate: 0,
  excludeShipping: false,
  paymentType: '',
  deductTaxRate: 0,
  requireTransactionNumber: false,
  useOnlineOrder: false,
  paymentMethodId: '',
  voucherRunPartner: '',
  notShowPartnerBill: '0', // Default: Không sử dụng hóa đơn dành cho khách khi in
  requirePartnerInvoice: false,
  selectedPaymentMethodId: '',
  marketingPartnerCost: 0,
  voucherCostType: '',
  marketingStartDate: '',
  marketingEndDate: '',
  marketingDays: [],
  marketingHours: [],
  storeId: ''
}
