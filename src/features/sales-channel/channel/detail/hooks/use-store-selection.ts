import { useState } from 'react'

import { useStoresData } from '@/hooks/api/use-stores'

export function useStoreSelection() {
  const [searchTerm, setSearchTerm] = useState('')
  const [isPopoverOpen, setIsPopoverOpen] = useState(false)

  const { data: stores, isLoading } = useStoresData()

  const filteredStores = !stores
    ? []
    : stores.filter(store => store.name.toLowerCase().includes(searchTerm.toLowerCase()))

  const getSelectedStore = (storeId?: string) => {
    return stores?.find(store => store.id === storeId)
  }

  const handleStoreSelect = (storeId: string, onSelect: (storeId: string) => void) => {
    onSelect(storeId)
    setIsPopoverOpen(false)
    setSearchTerm('')
  }

  return {
    stores,
    filteredStores,
    searchTerm,
    setSearchTerm,
    isPopoverOpen,
    setIsPopoverOpen,
    isLoading,
    getSelectedStore,
    handleStoreSelect
  }
}
