import { useNavigate } from '@tanstack/react-router'

import { toast } from 'sonner'

import { useAuthStore } from '@/stores/authStore'

import type { CreateChannelPayload } from '@/lib/channels-api'

import {
  useOrderSources,
  useCreateChannel,
  useUpdateChannel,
  useFilteredPaymentMethodsData
} from '@/hooks/api'

import type { ChannelFormData } from '../data'
import { convertDaysToNumeric, convertHoursToNumeric, getApiErrorMessage } from '../utils'

// Interface for edit payload - same as create but with ID
interface EditChannelPayload extends CreateChannelPayload {
  id: string
}

interface UseChannelSaveProps {
  formData: ChannelFormData
  isFormValid: boolean
  channelId?: string // Add channelId to determine edit mode
  onSuccess?: () => void
}

export const useChannelSave = ({
  formData,
  isFormValid,
  channelId,
  onSuccess
}: UseChannelSaveProps) => {
  const navigate = useNavigate()
  const { company, brands } = useAuthStore(state => state.auth)
  const selectedBrand = brands?.[0]

  const isEditMode = !!channelId
  const createChannelMutation = useCreateChannel()
  const updateChannelMutation = useUpdateChannel()

  // Only fetch payment methods in add mode (edit mode uses extraData.payment_method_name)
  const { data: paymentMethods } = useFilteredPaymentMethodsData({
    storeUid: !isEditMode ? formData.storeId || undefined : undefined
  })

  // Fetch order sources to get source details
  const { data: orderSourcesData } = useOrderSources(1, undefined, formData.storeId)

  const handleCreateOrUpdateChannel = async (data: ChannelFormData) => {
    if (!company?.id || !selectedBrand?.id) {
      toast.error('Thiếu thông tin công ty hoặc thương hiệu')
      return
    }

    if (!data.sourceName.length) {
      toast.error('Vui lòng chọn ít nhất một nguồn đơn hàng')
      return
    }

    // Get payment method details
    let finalPaymentMethodId: string
    let paymentMethodName: string

    if (isEditMode) {
      // In edit mode, use the values from extraData
      finalPaymentMethodId = data.paymentMethodId
      paymentMethodName = data.selectedPaymentMethodId || data.paymentMethodId
    } else {
      // In add mode, find payment method from API
      const selectedPaymentMethod = paymentMethods?.find(
        method => method.id === data.selectedPaymentMethodId
      )

      finalPaymentMethodId =
        data.paymentMethodId === 'postpaid'
          ? selectedPaymentMethod?.code || data.selectedPaymentMethodId
          : data.paymentMethodId

      paymentMethodName =
        data.paymentMethodId === 'postpaid'
          ? selectedPaymentMethod?.name || selectedPaymentMethod?.code || finalPaymentMethodId
          : data.paymentMethodId === 'prepaid'
            ? 'PREPAID'
            : finalPaymentMethodId
    }

    // Create an array of channel objects - one for each selected source
    const channelPayloads: (CreateChannelPayload | EditChannelPayload)[] = data.sourceName.map(
      (sourceId, index) => {
        // Find the source details from the order sources data
        const sourceDetails = orderSourcesData?.data?.find(source => source.id === sourceId)

        // Build payload with correct field order for edit mode
        if (isEditMode && channelId) {
          return {
            source_id: sourceDetails?.source_id || sourceId,
            source_name: sourceDetails?.source_name || `Source_${index + 1}`,
            description: null,
            active: 1,
            sort: index === 0 ? 4 : 1000,
            is_fabi: 1,
            source_type: [],
            id: channelId, // ID placed here like in working body
            company_uid: company.id,
            brand_uid: selectedBrand.id,
            store_uid: data.storeId,
            partner_config: 1,
            extra_data: {
              require_tran_no: data.requirePartnerInvoice ? 1 : 0,
              commission: data.commissionRate / 100,
              deduct_tax_rate: data.deductTaxRate / 100,
              payment_method_id: finalPaymentMethodId,
              payment_type: data.paymentMethodId.toUpperCase(),
              marketing_partner_cost_type:
                data.voucherCostType === 'PERCENTAGE' ? 'PERCENT' : 'AMOUNT',
              marketing_partner_cost: data.marketingPartnerCost,
              voucher_run_partner: data.voucherRunPartner || '',
              not_show_partner_bill: parseInt(data.notShowPartnerBill),
              use_order_online: data.useOnlineOrder ? 1 : 0,
              exclude_ship: data.excludeShipping ? 1 : 0,
              marketing_partner_cost_from_date: data.marketingStartDate
                ? new Date(data.marketingStartDate).getTime()
                : Date.now(),
              marketing_partner_cost_to_date: data.marketingEndDate
                ? new Date(data.marketingEndDate).getTime()
                : Date.now(),
              payment_method_name: paymentMethodName,
              marketing_partner_cost_hour_day: convertHoursToNumeric(data.marketingHours) || 138752,
              marketing_partner_cost_date_week: convertDaysToNumeric(data.marketingDays) || 224
            }
          } as EditChannelPayload
        }

        // For create mode, use original structure
        const basePayload = {
          source_id: sourceDetails?.source_id || sourceId,
          source_name: sourceDetails?.source_name || `Source_${index + 1}`,
          description: null,
          active: 1,
          sort: index === 0 ? 4 : 1000,
          is_fabi: 1,
          source_type: [],
          company_uid: company.id,
          brand_uid: selectedBrand.id,
          store_uid: data.storeId,
          partner_config: 1,
          extra_data: {
            require_tran_no: data.requirePartnerInvoice ? 1 : 0,
            commission: data.commissionRate / 100,
            deduct_tax_rate: data.deductTaxRate / 100,
            payment_method_id: finalPaymentMethodId,
            payment_type: data.paymentMethodId.toUpperCase(),
            marketing_partner_cost_type:
              data.voucherCostType === 'PERCENTAGE' ? 'PERCENT' : 'AMOUNT',
            marketing_partner_cost: data.marketingPartnerCost,
            voucher_run_partner: data.voucherRunPartner || '',
            not_show_partner_bill: parseInt(data.notShowPartnerBill),
            use_order_online: data.useOnlineOrder ? 1 : 0,
            exclude_ship: data.excludeShipping ? 1 : 0,
            marketing_partner_cost_from_date: data.marketingStartDate
              ? new Date(data.marketingStartDate).getTime()
              : Date.now(),
            marketing_partner_cost_to_date: data.marketingEndDate
              ? new Date(data.marketingEndDate).getTime()
              : Date.now(),
            marketing_partner_cost_date_week: convertDaysToNumeric(data.marketingDays) || 224,
            marketing_partner_cost_hour_day: convertHoursToNumeric(data.marketingHours) || 138752,
            payment_method_name: paymentMethodName
          }
        }

        return basePayload as CreateChannelPayload
      }
    )

    try {
      // Use appropriate mutation based on mode
      if (isEditMode) {
        // For edit mode, send single object (not array) since we're editing one channel
        const singlePayload = channelPayloads[0] // Take the first (and only) item
        await createChannelMutation.mutateAsync(singlePayload)
        toast.success('Cập nhật thành công kênh bán hàng')
      } else {
        // For create mode, use array as before
        await createChannelMutation.mutateAsync(channelPayloads)
        toast.success(`Tạo thành công ${channelPayloads.length} kênh bán hàng`)
      }

      // Navigate immediately after success
      navigate({ to: '/sales-channel/channel' })

      // Call onSuccess callback after navigation
      onSuccess?.()
    } catch (error: unknown) {
      // Check if it's actually a success case (status 200) but axios is throwing
      const apiError = error as any
      if (apiError?.response?.status === 200 || apiError?.response?.status === 201) {
        const successMessage = isEditMode
          ? 'Cập nhật thành công kênh bán hàng'
          : `Tạo thành công ${channelPayloads.length} kênh bán hàng`
        toast.success(successMessage)
        navigate({ to: '/sales-channel/channel' })
        onSuccess?.()
        return
      }

      // Use the new error handler to extract message
      const errorMessage = getApiErrorMessage(error)

      toast.error(errorMessage)
    }
  }

  const handleBack = () => {
    navigate({ to: '/sales-channel/channel' })
  }

  const handleSave = () => {
    if (!isFormValid) {
      toast.error('Vui lòng điền đầy đủ thông tin bắt buộc')
      return
    }

    // Use the same logic for both create and edit
    handleCreateOrUpdateChannel(formData)
  }

  // Return the appropriate mutation based on mode
  const activeMutation = isEditMode ? updateChannelMutation : createChannelMutation

  return {
    handleBack,
    handleSave,
    createChannelMutation: activeMutation // Keep the same name for backward compatibility
  }
}
