import { useEffect, useRef, useState } from 'react'

import type { Channel } from '@/types/channels'

import { useChannelById } from '@/hooks/api/use-channels'

import type { ChannelFormData } from '../data'
import { convertNumericToDays, convertNumericToHours } from '../utils'

interface UseChannelDataProps {
  channelId?: string
  onDataLoaded?: (formData: ChannelFormData) => void
}

export const useChannelData = ({ channelId, onDataLoaded }: UseChannelDataProps) => {
  const { data: channelData, isLoading, error } = useChannelById(channelId)

  // Track if we've already loaded data to prevent infinite loops
  const [hasLoaded, setHasLoaded] = useState(false)
  const [isDataLoaded, setIsDataLoaded] = useState(false)

  // Store the callback in a ref to avoid dependency issues
  const onDataLoadedRef = useRef(onDataLoaded)
  onDataLoadedRef.current = onDataLoaded

  // Force immediate conversion if data is available and not loaded yet
  if (channelData && onDataLoadedRef.current && !hasLoaded) {
    const formData = convertChannelToFormData(channelData)
    onDataLoadedRef.current(formData)
    setHasLoaded(true)
    setIsDataLoaded(true)
  }

  // Convert channel data to form data when loaded
  useEffect(() => {
    if (channelData && onDataLoadedRef.current && !hasLoaded) {
      const formData = convertChannelToFormData(channelData)
      onDataLoadedRef.current(formData)
      setHasLoaded(true)
      setIsDataLoaded(true)
    }
  }, [channelData, channelId, hasLoaded]) // Add hasLoaded to dependencies

  // Reset the flags when channelId changes
  useEffect(() => {
    setHasLoaded(false)
    setIsDataLoaded(false)
  }, [channelId])

  // Force reset on mount
  useEffect(() => {
    setHasLoaded(false)
    setIsDataLoaded(false)
  }, [])

  return {
    channelData,
    isLoading,
    error,
    isDataLoaded: isDataLoaded && !!channelData
  }
}

/**
 * Convert Channel API data to ChannelFormData for form editing
 */
export const convertChannelToFormData = (channel: Channel): ChannelFormData => {
  const extraData = channel.extra_data

  const formData = {
    // Thông tin cơ bản - Use source_name directly from channel data
    sourceName: channel.source_name ? [channel.source_name] : [],
    description: channel.description || '',
    storeId: channel.store_uid,

    // Cấu hình kênh bán hàng
    commissionRate: (extraData.commission || 0) * 100, // Convert decimal to percentage
    excludeShipping: extraData.exclude_ship === 1,
    paymentType: extraData.payment_type || '',
    deductTaxRate: (extraData.deduct_tax_rate || 0) * 100, // Convert decimal to percentage
    requireTransactionNumber: extraData.require_tran_no === 1,
    useOnlineOrder: extraData.use_order_online === 1,

    // Payment method logic - use payment_method_name from extra_data with fallbacks
    paymentMethodId: extraData.payment_type?.toLowerCase() || 'prepaid',
    selectedPaymentMethodId: extraData.payment_method_name || extraData.payment_method_id || '',
    requirePartnerInvoice: extraData.require_tran_no === 1,

    voucherRunPartner: extraData.voucher_run_partner || '',
    notShowPartnerBill: extraData.not_show_partner_bill?.toString() || '0',

    // Cấu hình chi phí đối tác marketing
    marketingPartnerCost: extraData.marketing_partner_cost || 0,
    voucherCostType: extraData.marketing_partner_cost_type || 'PERCENTAGE',

    // Convert timestamps to date strings
    marketingStartDate: extraData.marketing_partner_cost_from_date
      ? new Date(extraData.marketing_partner_cost_from_date).toISOString().split('T')[0]
      : '',
    marketingEndDate: extraData.marketing_partner_cost_to_date
      ? new Date(extraData.marketing_partner_cost_to_date).toISOString().split('T')[0]
      : '',

    // Convert numeric values back to day/hour arrays
    marketingDays: convertNumericToDays(extraData.marketing_partner_cost_date_week || 224),
    marketingHours: convertNumericToHours(extraData.marketing_partner_cost_hour_day || 138752)
  }

  return formData
}
