import { useState } from 'react'

import { useNavigate } from '@tanstack/react-router'

import { toast } from 'sonner'

import { getErrorMessage } from '@/utils/error-utils'

import { useChannelsData } from '@/hooks/api/use-channels'
import { useStoresData } from '@/hooks/api/use-stores'
import { useSourceData } from '@/hooks/use-source-data'

import { channelsColumns, ChannelsDataTable, ChannelHeader } from './components'
import { CopyChannelModal } from './components/copy-channel-modal'


export default function ChannelsPage() {
  const navigate = useNavigate()
  const [selectedStore, setSelectedStore] = useState<string>('all')
  const [selectedSource, setSelectedSource] = useState<string>('all')
  const [copyModalOpen, setCopyModalOpen] = useState(false)

  const { data: channels, isLoading: channelsLoading, error: channelsError } = useChannelsData()

  const { data: stores, isLoading: storesLoading, error: storesError } = useStoresData()

  const { sourceOptions, isLoading: sourcesLoading, error: sourcesError } = useSourceData()

  const isLoading = channelsLoading || storesLoading || sourcesLoading
  const error = channelsError || storesError || sourcesError

  // Filter channels based on selected store and source
  let filteredChannels = !channels ? [] : channels

  // Filter by store
  if (selectedStore !== 'all') {
    filteredChannels = filteredChannels.filter(channel => channel.store_uid === selectedStore)
  }

  // Filter by source
  if (selectedSource !== 'all') {
    filteredChannels = filteredChannels.filter(channel => channel.source_id === selectedSource)
  }

  const handleCreateChannel = () => {
    navigate({ to: '/sales-channel/channel/detail' })
  }

  const handleCopyChannels = () => {
    setCopyModalOpen(true)
  }

  const handleImportFromFile = () => {
    // TODO: Implement import from file functionality
    toast.info('Thêm kênh bán hàng từ file')
  }

  return (
    <div className='container mx-auto px-4 py-8'>
      <ChannelHeader
        selectedStore={selectedStore}
        onStoreChange={setSelectedStore}
        selectedSource={selectedSource}
        onSourceChange={setSelectedSource}
        stores={stores}
        sourceOptions={sourceOptions}
        storesLoading={storesLoading}
        sourcesLoading={sourcesLoading}
        onCreateChannel={handleCreateChannel}
        onCopyChannels={handleCopyChannels}
        onImportFromFile={handleImportFromFile}
      />

      {error ? (
        <div className='py-8 text-center'>
          <p className='text-red-600'>{getErrorMessage(error)}</p>
        </div>
      ) : isLoading ? (
        <div className='py-8 text-center'>
          <p>Đang tải dữ liệu kênh bán hàng...</p>
        </div>
      ) : (
        <ChannelsDataTable columns={channelsColumns} data={filteredChannels} />
      )}

      <CopyChannelModal open={copyModalOpen} onOpenChange={setCopyModalOpen} />
    </div>
  )
}
