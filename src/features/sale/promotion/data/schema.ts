import { z } from 'zod'

// Schema for the table display (matches columns)
export const promotionSchema = z.object({
  code: z.string(), // promotion_id
  name: z.string(), // promotion_name
  store: z.string(), // store names joined
  totalStores: z.number(),
  isActive: z.boolean(),
  createdAt: z.date(),
  // Keep original API data for reference
  originalData: z.object({
    promotion_id: z.string(),
    promotion_name: z.string(),
    partner_auto_gen: z.number(),
    array_agg: z.array(z.number()),
    ids_same_promotion: z.array(z.string()),
    promotions: z.array(
      z.object({
        promotion_uid: z.string(),
        active: z.number(),
        created_at: z.number(),
        store_uid: z.string(),
        store_name: z.string(),
      })
    ),
  }),
})

export type Promotion = z.infer<typeof promotionSchema>
