import { Skeleton } from '@/components/ui/skeleton'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'

export function PromotionTableSkeleton() {
  return (
    <div className='w-full space-y-4'>
      {/* Header skeleton */}
      <div className='space-y-2'>
        <div className='flex items-center justify-between'>
          <div className='flex items-center gap-2'>
            <Skeleton className='h-6 w-48' />
            <Skeleton className='h-5 w-20' />
            <Skeleton className='h-5 w-24' />
          </div>
        </div>
      </div>

      {/* Table skeleton */}
      <div className='w-full max-w-full overflow-hidden'>
        <div className='overflow-x-auto rounded-md border bg-white'>
          <Table style={{ minWidth: '800px' }}>
            <TableHeader>
              <TableRow>
                <TableHead className='w-[50px]'>
                  <Skeleton className='h-4 w-6' />
                </TableHead>
                <TableHead>
                  <Skeleton className='h-4 w-20' />
                </TableHead>
                <TableHead>
                  <Skeleton className='h-4 w-32' />
                </TableHead>
                <TableHead>
                  <Skeleton className='h-4 w-24' />
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {Array.from({ length: 10 }).map((_, index) => (
                <TableRow key={index}>
                  <TableCell>
                    <Skeleton className='h-4 w-6' />
                  </TableCell>
                  <TableCell>
                    <Skeleton className='h-4 w-16' />
                  </TableCell>
                  <TableCell>
                    <Skeleton className='h-4 w-40' />
                  </TableCell>
                  <TableCell>
                    <Skeleton className='h-4 w-28' />
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </div>
    </div>
  )
}
