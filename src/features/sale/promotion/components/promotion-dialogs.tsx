import { useDeletePromotion } from '@/hooks/api/use-promotions'
import { ConfirmDialog } from '@/components/confirm-dialog'
import { usePromotions } from '../context'
import { PromotionMutate } from './promotion-mutate'

export function PromotionsDialogs() {
  const { open, setOpen, currentRow, setCurrentRow } = usePromotions()
  const { deletePromotion } = useDeletePromotion()

  return (
    <>
      <PromotionMutate
        key='tasks-create'
        open={open === 'create'}
        onOpenChange={() => setOpen('create')}
      />

      {currentRow && (
        <>
          <PromotionMutate
            key={`tasks-update-${currentRow.code}`}
            open={open === 'update'}
            onOpenChange={() => {
              setOpen('update')
              setTimeout(() => {
                setCurrentRow(null)
              }, 500)
            }}
            currentRow={currentRow}
          />

          <ConfirmDialog
            key='task-delete'
            destructive
            open={open === 'delete'}
            onOpenChange={() => {
              setOpen('delete')
              setTimeout(() => {
                setCurrentRow(null)
              }, 500)
            }}
            handleConfirm={async () => {
              setOpen(null)
              setTimeout(() => {
                setCurrentRow(null)
              }, 500)
              await deletePromotion(
                currentRow.originalData.ids_same_promotion.join(',')
              )
            }}
            className='max-w-md'
            title={`Bạn có muốn xoá ?`}
            desc={<>Hành động không thể hoàn tác.</>}
            confirmText='Xoá'
          />
        </>
      )}
    </>
  )
}
