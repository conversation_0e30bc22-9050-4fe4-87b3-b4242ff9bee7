import { z } from 'zod'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { toast } from 'sonner'
import { usePosStore } from '@/stores/posStore'
import { getUserEmail } from '@/lib/utils'
import {
  useCreatePromotion,
  useUpdatePromotion,
  useStoresData,
} from '@/hooks/api'
import { Button } from '@/components/ui/button'
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { MultiSelect } from '@/components/multi-select'
import { Promotion } from '../data'

interface Props {
  open: boolean
  onOpenChange: (open: boolean) => void
  currentRow?: Promotion
}

const formSchema = z.object({
  promotion_name: z.string().min(1, 'Tên khu<PERSON>ến mãi là bắt buộc.'),
  store: z.array(z.string()).min(1, '<PERSON>ui lòng chọn ít nhất một cửa hàng.'),
})
type PromotionsForm = z.infer<typeof formSchema>

const generatePromotionId = (): string => {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'
  let result = ''
  for (let i = 0; i < 4; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return `PROMOTION-${result}`
}

export function PromotionMutate({ open, onOpenChange, currentRow }: Props) {
  const { data: stores = [], isLoading: isLoadingStores } = useStoresData()
  const { company, selectedBrand } = usePosStore()
  const { createPromotion, isCreating } = useCreatePromotion()
  const { updatePromotion, isUpdating } = useUpdatePromotion()
  const isUpdate = !!currentRow

  const form = useForm<PromotionsForm>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      promotion_name: currentRow?.name || '',
      store: currentRow?.originalData.promotions.map((p) => p.store_uid) || [],
    },
  })

  const onSubmit = (data: PromotionsForm) => {
    if (!company?.id || !selectedBrand?.id) {
      toast.error('Thiếu thông tin công ty hoặc thương hiệu')
      return
    }

    if (isUpdate && currentRow) {
      const originalData = currentRow.originalData
      const currentTime = Math.floor(Date.now() / 1000)
      const userEmail = getUserEmail()

      updatePromotion({
        brand_uid: selectedBrand.id,
        company_uid: company.id,
        sort: 1000,
        is_fabi: 1,
        partner_auto_gen: 0,
        active: 1,
        extra_data: {},
        source_uid: 'b78fe607-2ab5-4f08-baac-e9dfbade7a87',
        promotion_id: originalData.promotion_id,
        promotion_name: data.promotion_name,
        description: null,
        deleted: false,
        created_by: userEmail,
        updated_by: userEmail,
        deleted_by: null,
        created_at: currentTime,
        updated_at: currentTime,
        deleted_at: null,
        promotions: data.store.map((storeUid, index) => ({
          id:
            originalData.ids_same_promotion[index] ||
            originalData.ids_same_promotion[0] ||
            '',
          company_uid: company.id,
          brand_uid: selectedBrand.id,
          sort: 1000,
          is_fabi: 1,
          partner_auto_gen: 0,
          active: 1,
          extra_data: {},
          source_uid: 'b78fe607-2ab5-4f08-baac-e9dfbade7a87',
          promotion_id: originalData.promotion_id,
          promotion_name: data.promotion_name,
          description: null,
          store_uid: storeUid,
        })),
        promotion_uids_same_promotion_id:
          originalData.ids_same_promotion.join(','),
        stores: data.store,
        list_store_uid: data.store,
        list_promotion_uid: originalData.ids_same_promotion,
      })
    } else {
      createPromotion({
        promotion_name: data.promotion_name,
        company_uid: company.id,
        brand_uid: selectedBrand.id,
        promotion_id: generatePromotionId(),
        list_store_uid: data.store,
      })
    }

    onOpenChange(false)
    form.reset()
  }

  return (
    <Dialog
      open={open}
      onOpenChange={(v) => {
        onOpenChange(v)
        form.reset()
      }}
    >
      <DialogContent className='top-[50%] w-full max-w-4xl translate-y-[-50%]'>
        <DialogHeader>
          <DialogTitle>
            {isUpdate ? 'Chỉnh sửa khuyến mãi' : 'Tạo khuyến mãi mới'}
          </DialogTitle>
          <DialogDescription>
            {isUpdate
              ? 'Cập nhật thông tin khuyến mãi.'
              : 'Nhập thông tin để tạo khuyến mãi mới.'}
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-4'>
            <FormField
              control={form.control}
              name='promotion_name'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Tên khuyến mãi</FormLabel>
                  <FormControl>
                    <Input placeholder='Nhập tên khuyến mãi...' {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name='store'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Cửa hàng</FormLabel>
                  <FormControl>
                    <MultiSelect
                      options={stores.map((store) => ({
                        label: store.name,
                        value: store.id,
                      }))}
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                      placeholder={
                        isLoadingStores ? 'Đang tải...' : 'Chọn cửa hàng'
                      }
                      variant='default'
                      animation={0.2}
                      maxCount={3}
                      disabled={isLoadingStores}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <DialogClose asChild>
                <Button variant='outline' type='button'>
                  Hủy
                </Button>
              </DialogClose>
              <Button type='submit' disabled={isCreating || isUpdating}>
                {isCreating || isUpdating
                  ? isUpdate
                    ? 'Đang cập nhật...'
                    : 'Đang tạo...'
                  : isUpdate
                    ? 'Cập nhật'
                    : 'Tạo mới'}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
