import { Cross2Icon } from '@radix-ui/react-icons'
import { Table } from '@tanstack/react-table'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { DataTableViewOptions } from '@/components/data-table'

interface PromotionTableToolbarProps<TData> {
  table: Table<TData>
  storesData?: { id: string; name: string }[]
  selectedStoreUid?: string
  onStoreChange?: (storeUid: string) => void
}

export function PromotionTableToolbar<TData>({
  table,
  storesData = [],
  selectedStoreUid = 'all',
  onStoreChange,
}: PromotionTableToolbarProps<TData>) {
  const isFiltered = table.getState().columnFilters.length > 0

  return (
    <div className='flex items-center justify-between'>
      <div className='flex flex-1 flex-col-reverse items-start gap-y-2 sm:flex-row sm:items-center sm:space-x-2'>
        <Input
          placeholder='Tìm kiếm...'
          value={(table.getColumn('name')?.getFilterValue() as string) ?? ''}
          onChange={(event) =>
            table.getColumn('name')?.setFilterValue(event.target.value)
          }
          className='h-8 w-[150px] lg:w-[250px]'
        />
        <div className='flex gap-x-2'>
          <Select value={selectedStoreUid} onValueChange={onStoreChange}>
            <SelectTrigger className='h-8 w-[180px]'>
              <SelectValue placeholder='Chọn cửa hàng' />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value='all'>Tất cả cửa hàng</SelectItem>
              {storesData.map((store: { id: string; name: string }) => (
                <SelectItem key={store.id} value={store.id}>
                  {store.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        {isFiltered && (
          <Button
            variant='ghost'
            onClick={() => table.resetColumnFilters()}
            className='h-8 px-2 lg:px-3'
          >
            Reset
            <Cross2Icon className='ml-2 h-4 w-4' />
          </Button>
        )}
      </div>
      <DataTableViewOptions table={table} />
    </div>
  )
}
