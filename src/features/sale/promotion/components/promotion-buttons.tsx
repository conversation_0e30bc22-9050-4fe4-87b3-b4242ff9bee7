import { IconPlus } from '@tabler/icons-react'
import { But<PERSON> } from '@/components/ui/button'
import { usePromotions } from '../context'

export function PromotionsButtons() {
  const { setOpen } = usePromotions()
  return (
    <div className='flex gap-2'>
      <Button className='space-x-1' onClick={() => setOpen('create')}>
        <span>Tạo chương trình</span> <IconPlus size={18} />
      </Button>
    </div>
  )
}
