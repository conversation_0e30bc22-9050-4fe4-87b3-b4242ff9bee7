'use client'

import type { ColumnDef } from '@tanstack/react-table'
import { DataTableColumnHeader } from '@/components/data-table'
import { Promotion } from '../data'
import { PromotionRowActions } from './promotion-row-actions'

export const columns: ColumnDef<Promotion>[] = [
  {
    id: 'index',
    header: '#',
    cell: ({ row }) => <div className='w-[50px]'>{row.index + 1}</div>,
    enableSorting: false,
    enableHiding: false,
    size: 50,
  },
  {
    accessorKey: 'code',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Mã CTKM' />
    ),
    cell: ({ row }) => (
      <div className='text-sm font-medium'>{row.getValue('code')}</div>
    ),
    enableSorting: false,
    enableHiding: true,
  },
  {
    accessorKey: 'name',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Tên CTKM' />
    ),
    cell: ({ row }) => <div className='text-sm'>{row.getValue('name')}</div>,
    enableSorting: false,
    enableHiding: true,
  },
  {
    accessorKey: 'store',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Cửa hàng' />
    ),
    cell: ({ row }) => <div className='text-sm'>{row.getValue('store')}</div>,
    enableSorting: false,
    enableHiding: true,
  },
  {
    id: 'actions',
    cell: ({ row }) => <PromotionRowActions row={row} />,
  },
]
