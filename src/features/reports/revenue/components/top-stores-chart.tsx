import { useMemo } from 'react'
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from 'recharts'
import { RevenueDataItem } from '@/lib/revenue-api'
import { useReportsContext } from '@/hooks/use-reports-context'
import { useTopStoresLeastRevenue } from '@/hooks/use-top-stores'

export function TopStoresChart() {
  const { dateRange, filterType } = useReportsContext()

  const { topStores, isLoading, error, rawData } = useTopStoresLeastRevenue(
    dateRange,
    filterType
  )

  const chartData = useMemo(() => {
    if (!topStores || topStores.length === 0 || !rawData?.data) {
      return []
    }

    // Generate data points using real data from API
    const data = []

    // Create a map of store data for easy lookup
    const storeDataMap = new Map()
    rawData.data.forEach((store) => {
      storeDataMap.set(store.store_uid, store)
    })

    if (filterType === 'monthly') {
      // Generate monthly data points using real data
      const monthlyDataMap = new Map<string, Record<string, number | string>>()

      // Process each store's list_data to group by month
      topStores.slice(0, 5).forEach((store) => {
        const storeData = storeDataMap.get(store.storeId)
        if (!storeData?.list_data) return

        storeData.list_data.forEach((dayData: RevenueDataItem) => {
          const date = new Date(dayData.tran_date)
          const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`

          if (!monthlyDataMap.has(monthKey)) {
            monthlyDataMap.set(monthKey, { date: monthKey })
          }

          const monthData = monthlyDataMap.get(monthKey)!
          monthData[store.storeName] =
            ((monthData[store.storeName] as number) || 0) +
            dayData.revenue_gross
        })
      })

      // Convert to array and sort by date
      data.push(
        ...Array.from(monthlyDataMap.values()).sort((a, b) =>
          (a.date as string).localeCompare(b.date as string)
        )
      )
    } else {
      // Generate daily data points using real data
      const dailyDataMap = new Map<string, Record<string, number | string>>()

      // Process each store's list_data
      topStores.slice(0, 5).forEach((store) => {
        const storeData = storeDataMap.get(store.storeId)
        if (!storeData?.list_data) return

        storeData.list_data.forEach((dayData: RevenueDataItem) => {
          const dateKey = dayData.date // Use the date string from API

          if (!dailyDataMap.has(dateKey)) {
            dailyDataMap.set(dateKey, { date: dateKey })
          }

          const dayDataPoint = dailyDataMap.get(dateKey)!
          dayDataPoint[store.storeName] = dayData.revenue_gross
        })
      })

      // Convert to array and sort by date
      data.push(
        ...Array.from(dailyDataMap.values()).sort((a, b) =>
          (a.date as string).localeCompare(b.date as string)
        )
      )
    }

    return data
  }, [topStores, filterType, rawData])

  const lineColors = [
    '#ef4444', // Red
    '#f97316', // Orange
    '#f59e0b', // Amber
    '#eab308', // Yellow
    '#84cc16', // Lime
  ]

  if (isLoading) {
    return (
      <div className='flex h-[300px] items-center justify-center'>
        <span className='text-muted-foreground text-sm'>Đang tải...</span>
      </div>
    )
  }

  if (error) {
    return (
      <div className='flex h-[300px] items-center justify-center'>
        <span className='text-sm text-red-500'>Lỗi tải dữ liệu biểu đồ</span>
      </div>
    )
  }

  if (chartData.length === 0 || topStores.length === 0) {
    return (
      <div className='flex h-[300px] items-center justify-center'>
        <span className='text-muted-foreground text-sm'>
          Không có dữ liệu biểu đồ
        </span>
      </div>
    )
  }

  return (
    <div className='h-[300px]'>
      <ResponsiveContainer width='100%' height='100%'>
        <LineChart data={chartData}>
          <CartesianGrid strokeDasharray='3 3' />
          <XAxis
            dataKey='date'
            tickFormatter={(value) => {
              if (filterType === 'monthly') {
                const [year, month] = value.split('-')
                return `${month}/${year.slice(-2)}`
              } else {
                return value
              }
            }}
            fontSize={12}
            tickLine={false}
            axisLine={false}
            interval={0}
            angle={-35}
            textAnchor='end'
            height={60}
          />
          <YAxis
            fontSize={12}
            tickLine={false}
            axisLine={false}
            tickCount={6}
            domain={['dataMin', 'dataMax']}
            tickFormatter={(value) => {
              if (value >= 1000000) {
                return `${Math.round(value / 1000000)}tr`
              } else if (value >= 1000) {
                return `${Math.round(value / 1000)}k`
              } else if (value === 0) {
                return '0'
              } else {
                return Math.round(value).toString()
              }
            }}
          />
          <Tooltip
            formatter={(value, name) => [
              typeof value === 'number'
                ? value.toLocaleString('vi-VN') + ' ₫'
                : value,
              name,
            ]}
            labelFormatter={(label) => {
              if (filterType === 'monthly') {
                // label format: "2024-12" or "2025-01"
                const [year, month] = label.split('-')
                return `Tháng ${month}/${year}`
              } else {
                return `Ngày ${label}`
              }
            }}
          />
          <Legend wrapperStyle={{ fontSize: '12px' }} />
          {topStores.slice(0, 5).map((store, index) => (
            <Line
              key={store.storeId}
              type='monotone'
              dataKey={store.storeName}
              name={store.storeName}
              stroke={lineColors[index % lineColors.length]}
              strokeWidth={2}
              dot={false}
              activeDot={{ r: 4 }}
            />
          ))}
        </LineChart>
      </ResponsiveContainer>
    </div>
  )
}
