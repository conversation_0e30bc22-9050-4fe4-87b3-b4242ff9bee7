import { useTopItemsLeastSales } from '@/hooks/use-top-items'
import { Badge } from '@/components/ui/badge'

interface TopItemsProps {
  dateRange: {
    from: Date
    to: Date
  }
  filterType: 'monthly' | 'daily'
}

export function TopItems({ dateRange }: TopItemsProps) {
  const { topItems, isLoading, error } = useTopItemsLeastSales(dateRange)

  if (isLoading) {
    return (
      <div className='space-y-3'>
        {[...Array(5)].map((_, i) => (
          <div key={i} className='flex items-center justify-between'>
            <div className='flex items-center space-x-3'>
              <div className='h-6 w-6 animate-pulse rounded-full bg-gray-200' />
              <div className='h-4 w-32 animate-pulse rounded bg-gray-200' />
            </div>
            <div className='h-6 w-16 animate-pulse rounded bg-gray-200' />
          </div>
        ))}
      </div>
    )
  }

  if (error) {
    return (
      <div className='py-8 text-center'>
        <p className='text-sm text-red-500'>Lỗi tải dữ liệu: {error}</p>
      </div>
    )
  }

  if (!topItems || topItems.length === 0) {
    return (
      <div className='py-8 text-center'>
        <p className='text-muted-foreground text-sm'>
          Không có dữ liệu mặt hàng
        </p>
      </div>
    )
  }

  return (
    <div className='space-y-3'>
      {topItems.map((item, index) => {
        return (
          <div
            key={item.itemId}
            className='flex items-center justify-between gap-2'
          >
            <div className='flex min-w-0 flex-1 items-center space-x-2'>
              <Badge
                variant={
                  index === 0
                    ? 'destructive'
                    : index === 1
                      ? 'secondary'
                      : 'outline'
                }
                className='flex h-6 w-6 flex-shrink-0 items-center justify-center rounded-full p-0 text-xs font-medium'
              >
                {index + 1}
              </Badge>
              <div className='min-w-0 flex-1'>
                <div
                  className='cursor-help truncate text-sm font-medium'
                  title={item.itemName}
                >
                  {item.itemName}
                </div>
                <div className='text-muted-foreground truncate text-xs'>
                  {item.itemClass} • {item.itemType}
                </div>
              </div>
            </div>
            <div className='flex flex-shrink-0 items-center justify-end'>
              <Badge
                variant='outline'
                className='bg-red-100 text-xs font-bold whitespace-nowrap text-red-800'
              >
                {item.quantitySold.toLocaleString('vi-VN')} {item.unit}
              </Badge>
            </div>
          </div>
        )
      })}
    </div>
  )
}
