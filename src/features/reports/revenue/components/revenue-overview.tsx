import { useState, useMemo } from 'react'
import {
  getDefaultMonthlyDateRange,
  getDefaultDailyDateRange,
} from '@/lib/date-utils'
import { ReportsContext } from '@/hooks/use-reports-context'
import { useSources } from '@/hooks/use-sources'
import { FiltersPanel } from './filters-panel'
import { RevenueKpiCards } from './revenue-kpi-cards'
import { RevenueTotalAmountCards } from './revenue-total-amount'
import { TopStoresSection } from './top-stores-section'

export function RevenueOverview() {
  const [filterType, setFilterType] = useState<'monthly' | 'daily'>('monthly')

  const [dateRange, setDateRange] = useState<{
    from: Date
    to: Date
  }>(getDefaultMonthlyDateRange())
  const [selectedStores, setSelectedStores] = useState<string[]>(['all-stores'])
  const [selectedSources, setSelectedSources] = useState<string[]>([
    'all-sources',
  ])

  const {
    sources,
    chartData,
    isLoading: isSourcesLoading,
    error: sourcesError,
  } = useSources({
    dateRange,
    selectedStores,
    selectedSources,
    filterType,
    autoFetch: true,
  })

  const handleFilterTypeChange = (newFilterType: 'monthly' | 'daily') => {
    setFilterType(newFilterType)

    if (newFilterType === 'monthly') {
      setDateRange(getDefaultMonthlyDateRange())
    } else if (newFilterType === 'daily') {
      setDateRange(getDefaultDailyDateRange())
    }
  }

  const contextValue = useMemo(
    () => ({
      dateRange,
      filterType,
      selectedStores,
      selectedSources,
    }),
    [dateRange, filterType, selectedStores, selectedSources]
  )

  return (
    <ReportsContext.Provider value={contextValue}>
      {/* Filters Panel */}
      <FiltersPanel
        dateRange={dateRange}
        onDateRangeChange={setDateRange}
        selectedStores={selectedStores}
        onStoreChange={setSelectedStores}
        selectedSources={selectedSources}
        onSourceChange={setSelectedSources}
        filterType={filterType}
        onFilterTypeChange={handleFilterTypeChange}
      />

      {/* KPI Cards */}
      <RevenueKpiCards
        sources={{
          data: chartData,
          isLoading: isSourcesLoading,
          error: sourcesError,
        }}
        filterType={filterType}
        className='w-full'
      />

      {/* Revenue Dashboard */}
      <RevenueTotalAmountCards
        sources={{
          data: sources,
          isLoading: isSourcesLoading,
          error: sourcesError,
        }}
        dateRange={{
          startDate: dateRange.from.getTime(),
          endDate: dateRange.to.getTime(),
        }}
        filterType={filterType}
        className='w-full'
      />

      {/* Top Stores Section */}
      <TopStoresSection />
    </ReportsContext.Provider>
  )
}
