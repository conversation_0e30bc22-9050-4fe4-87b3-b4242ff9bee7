# Reports Components

This directory contains modular components for the revenue reports feature.

## Overview

The reports feature has been modularized into smaller, focused components to improve maintainability, reusability, and testability.

## Key Components

### 1. DateRangePicker (`date-range-picker.tsx`)

A reusable date range selector component with Vietnamese localization.

**Features:**

- Calendar-based date selection
- Vietnamese locale support
- Date validation (end date must be after start date)
- Quick preset buttons (7 days, 28 days, current month)
- Customizable styling

**Props:**

- `dateRange`: Object with `from` and `to` Date values
- `onDateRangeChange`: Callback function for date changes
- `className`: Optional CSS classes

### 2. FiltersPanel (`filters-panel.tsx`)

Contains all filter controls for the reports dashboard.

**Features:**

- Date range picker integration
- Store selection from global state
- Promotion filter dropdown
- Source filter dropdown
- Responsive layout

**Props:**

- `dateRange`: Current date range selection
- `onDateRangeChange`: Callback for date range changes
- `selectedStores`: Array of selected store IDs
- `onStoreChange`: Callback for store selection changes
- `className`: Optional CSS classes

### 3. RevenueKpiCards (`revenue-kpi-cards.tsx`)

Displays key performance indicators in a card layout.

**Features:**

- Real-time revenue data display
- Loading and error states
- Currency formatting
- Revenue breakdown by source
- Order count metrics

**Props:**

- `summary`: Revenue summary data object
- `isLoading`: Loading state boolean
- `error`: Error message string
- `className`: Optional CSS classes

### 4. RevenueOverview (`revenue-overview.tsx`)

Main container component that orchestrates the reports dashboard.

**Features:**

- State management for filters
- API data integration
- Component composition
- Clean, minimal interface

## Data Flow

```mermaid
graph TD
    A[RevenueOverview] --> B[FiltersPanel]
    A --> C[RevenueKpiCards]
    B --> D[DateRangePicker]
    A --> E[useRevenueData Hook]
    E --> F[Revenue API]
    F --> G[POS Backend]
```

## Component Composition

The modular approach follows these principles:

1. **Single Responsibility**: Each component has one clear purpose
2. **Reusability**: Components can be used in different contexts
3. **Prop-based Communication**: Clean interfaces between components
4. **State Separation**: State management is centralized in the container
5. **Type Safety**: Full TypeScript support with interfaces

## Usage Example

```tsx
import { RevenueOverview } from './components/revenue-overview'

export function ReportsPage() {
  return (
    <div className='p-6'>
      <h1>Revenue Reports</h1>
      <RevenueOverview />
    </div>
  )
}
```

## Benefits of Modularization

- **Maintainability**: Easier to update individual components
- **Testability**: Each component can be tested in isolation
- **Reusability**: Components can be used in other parts of the application
- **Performance**: Smaller bundle sizes and better tree shaking
- **Developer Experience**: Clearer code organization and navigation
