import {
  useTopStoresLeastSales,
  useTopStoresMostSales,
  useTopStoresLeastOrders,
  useTopStoresMostOrders,
  useTopStoresLeastRevenue,
  useTopStoresMostRevenue,
} from '@/hooks/use-top-stores'
import { Badge } from '@/components/ui/badge'

interface TopStoresProps {
  dateRange: {
    from: Date
    to: Date
  }
  filterType: 'monthly' | 'daily'
  type:
    | 'least-sales'
    | 'most-sales'
    | 'least-orders'
    | 'most-orders'
    | 'least-revenue'
    | 'most-revenue'
}

export function TopStores({ dateRange, filterType, type }: TopStoresProps) {
  const leastSalesData = useTopStoresLeastSales(dateRange, filterType)
  const mostSalesData = useTopStoresMostSales(dateRange, filterType)
  const leastOrdersData = useTopStoresLeastOrders(dateRange, filterType)
  const mostOrdersData = useTopStoresMostOrders(dateRange, filterType)
  const leastRevenueData = useTopStoresLeastRevenue(dateRange, filterType)
  const mostRevenueData = useTopStoresMostRevenue(dateRange, filterType)

  const getSelectedData = () => {
    switch (type) {
      case 'least-sales':
        return leastSalesData
      case 'most-sales':
        return mostSalesData
      case 'least-orders':
        return leastOrdersData
      case 'most-orders':
        return mostOrdersData
      case 'least-revenue':
        return leastRevenueData
      case 'most-revenue':
        return mostRevenueData
      default:
        return leastSalesData
    }
  }

  const { topStores, isLoading, error } = getSelectedData()

  // Get display configuration based on type
  const getDisplayConfig = () => {
    switch (type) {
      case 'least-sales':
        return {
          primaryMetric: 'sales',
          badgeVariant: 'destructive' as const,
          isAscending: true,
        }
      case 'most-sales':
        return {
          primaryMetric: 'sales',
          badgeVariant: 'default' as const,
          isAscending: false,
        }
      case 'least-orders':
        return {
          primaryMetric: 'bills',
          badgeVariant: 'destructive' as const,
          isAscending: true,
        }
      case 'most-orders':
        return {
          primaryMetric: 'bills',
          badgeVariant: 'default' as const,
          isAscending: false,
        }
      case 'least-revenue':
        return {
          primaryMetric: 'revenue',
          badgeVariant: 'destructive' as const,
          isAscending: true,
        }
      case 'most-revenue':
        return {
          primaryMetric: 'revenue',
          badgeVariant: 'default' as const,
          isAscending: false,
        }
      default:
        return {
          primaryMetric: 'sales',
          badgeVariant: 'destructive' as const,
          isAscending: true,
        }
    }
  }

  const displayConfig = getDisplayConfig()

  if (isLoading) {
    return (
      <div className='space-y-4'>
        <div className='space-y-3'>
          {[...Array(5)].map((_, index) => (
            <div
              key={index}
              className='flex animate-pulse items-center justify-between'
            >
              <div className='flex items-center space-x-3'>
                <div className='bg-muted h-6 w-6 rounded-full'></div>
                <div className='bg-muted h-4 w-32 rounded'></div>
              </div>
              <div className='bg-muted h-4 w-16 rounded'></div>
            </div>
          ))}
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className='space-y-4'>
        <div className='text-center text-sm text-red-500'>
          Lỗi tải dữ liệu: {error}
        </div>
      </div>
    )
  }

  if (topStores.length === 0) {
    return (
      <div className='space-y-4'>
        <div className='text-muted-foreground text-center text-sm'>
          Không có dữ liệu cửa hàng
        </div>
      </div>
    )
  }

  return (
    <div className='space-y-4'>
      <div className='space-y-3'>
        {topStores.map((store, index) => {
          // Get primary metric value
          const getPrimaryValue = () => {
            switch (displayConfig.primaryMetric) {
              case 'sales':
                return store.totalSales
              case 'bills':
                return store.totalBills
              case 'revenue':
                return store.totalRevenue
              default:
                return store.totalSales
            }
          }

          const primaryValue = getPrimaryValue()

          return (
            <div
              key={store.storeId}
              className='flex items-center justify-between gap-2'
            >
              <div className='flex min-w-0 flex-1 items-center space-x-2'>
                <Badge
                  variant={
                    index === 0
                      ? displayConfig.badgeVariant
                      : index === 1
                        ? 'secondary'
                        : 'outline'
                  }
                  className='flex h-6 w-6 flex-shrink-0 items-center justify-center rounded-full p-0 text-xs font-medium'
                >
                  {index + 1}
                </Badge>
                <div
                  className='min-w-0 cursor-help truncate text-sm font-medium'
                  title={store.storeName}
                >
                  {store.storeName}
                </div>
              </div>
              <div className='flex flex-shrink-0 items-center justify-end'>
                <Badge
                  variant='outline'
                  className='bg-blue-100 text-xs font-bold whitespace-nowrap text-blue-800'
                >
                  {displayConfig.primaryMetric === 'revenue'
                    ? primaryValue.toLocaleString('vi-VN')
                    : primaryValue.toLocaleString('vi-VN')}
                </Badge>
              </div>
            </div>
          )
        })}
      </div>
    </div>
  )
}
