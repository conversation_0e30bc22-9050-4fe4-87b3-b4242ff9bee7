import { useMemo } from 'react'
import {
  AreaChart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from 'recharts'
import { revenueApi } from '@/lib/revenue-api'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'

interface DataState<T> {
  data: T | null
  isLoading: boolean
  error: string | null
}

interface SourcesData {
  source_id: string
  source_name: string
  total_bill: number
  revenue_gross: number
  discount_amount: number
  commission_amount: number
  partner_marketing_amount: number
  revenue_net: number
  total_cost: number
  total_cost_rate: number
  peo_count: number
  list_data?: Array<{
    date: string
    tran_date: number
    total_bill: number
    revenue_gross: number
    discount_amount: number
    commission_amount: number
    partner_marketing_amount: number
    peo_count: number
    revenue_net: number
  }>
}

interface RevenueTotalAmountProps {
  sources: DataState<SourcesData[]>
  dateRange: { startDate: number; endDate: number }
  filterType?: 'monthly' | 'daily'
  className?: string
}

export function RevenueTotalAmountCards({
  sources,
  dateRange,
  filterType = 'daily',
  className,
}: RevenueTotalAmountProps) {
  const chartData = useMemo(() => {
    if (!sources.data || sources.data.length === 0) {
      return []
    }

    const startDate = new Date(dateRange.startDate)
    const endDate = new Date(dateRange.endDate)

    if (filterType === 'monthly') {
      const monthlyDataMap = new Map<string, { revenue: number }>()

      sources.data.forEach((source) => {
        if (source.list_data && source.list_data.length > 0) {
          source.list_data.forEach((day) => {
            const date = new Date(day.date)
            const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`

            const existing = monthlyDataMap.get(monthKey)
            if (existing) {
              existing.revenue += day.revenue_gross
            } else {
              monthlyDataMap.set(monthKey, { revenue: day.revenue_gross })
            }
          })
        }
      })

      const months: string[] = []
      const currentDate = new Date(
        startDate.getFullYear(),
        startDate.getMonth(),
        1
      )
      const endMonth = new Date(endDate.getFullYear(), endDate.getMonth(), 1)

      while (currentDate <= endMonth) {
        const monthKey = `${currentDate.getFullYear()}-${String(currentDate.getMonth() + 1).padStart(2, '0')}`
        months.push(monthKey)
        currentDate.setMonth(currentDate.getMonth() + 1)
      }

      return months.map((monthKey) => {
        const monthData = monthlyDataMap.get(monthKey)
        return {
          date: monthKey,
          revenue: monthData?.revenue || 0,
        }
      })
    } else {
      const dailyDataMap = new Map<string, { revenue: number }>()

      sources.data.forEach((source) => {
        if (source.list_data && source.list_data.length > 0) {
          source.list_data.forEach((day) => {
            const existing = dailyDataMap.get(day.date)
            if (existing) {
              existing.revenue += day.revenue_gross
            } else {
              dailyDataMap.set(day.date, { revenue: day.revenue_gross })
            }
          })
        }
      })

      // Create dates array based on actual date range
      const dates: string[] = []
      const currentDate = new Date(startDate)
      const endDateTime = new Date(endDate)

      while (currentDate <= endDateTime) {
        const year = currentDate.getFullYear()
        const month = String(currentDate.getMonth() + 1).padStart(2, '0')
        const day = String(currentDate.getDate()).padStart(2, '0')
        dates.push(`${year}-${month}-${day}`)
        currentDate.setDate(currentDate.getDate() + 1)
      }

      return dates.map((date) => {
        const dayData = dailyDataMap.get(date)
        return {
          date,
          revenue: dayData?.revenue || 0,
        }
      })
    }
  }, [sources.data, dateRange.startDate, dateRange.endDate, filterType])

  return (
    <div className={className}>
      <div className='grid gap-4 lg:grid-cols-5'>
        <Card className='flex h-[256px] flex-col sm:col-span-1 lg:col-span-5'>
          <CardHeader className='flex-shrink-0 pb-2'>
            <CardTitle className='text-sm font-medium'>
              {filterType === 'monthly'
                ? 'Biểu đồ doanh thu theo tháng'
                : 'Biểu đồ doanh thu theo ngày'}
            </CardTitle>
          </CardHeader>
          <CardContent className='flex flex-1 flex-col'>
            {sources.isLoading ? (
              <div className='flex flex-1 items-center justify-center'>
                <span className='text-muted-foreground text-sm'>
                  Đang tải...
                </span>
              </div>
            ) : sources.error ? (
              <div className='flex flex-1 items-center justify-center'>
                <span className='text-sm text-red-500'>
                  Lỗi tải dữ liệu biểu đồ
                </span>
              </div>
            ) : chartData.length > 0 ? (
              <div className='flex-1'>
                <ResponsiveContainer width='100%' height='100%'>
                  <AreaChart data={chartData}>
                    <CartesianGrid strokeDasharray='3 3' />
                    <XAxis
                      dataKey='date'
                      tickFormatter={(value) => {
                        if (filterType === 'monthly') {
                          // Format as "Tháng MM/YYYY"
                          const [year, month] = value.split('-')
                          return `${month}/${year.slice(-2)}`
                        } else {
                          // Format as day number with month for clarity
                          const date = new Date(value)
                          const day = date.getDate()
                          const month = date.getMonth() + 1
                          return `${day}/${month}`
                        }
                      }}
                      fontSize={12}
                      tickLine={false}
                      axisLine={false}
                      interval={0}
                      angle={-35}
                      textAnchor='end'
                      height={60}
                    />
                    <YAxis
                      fontSize={12}
                      tickLine={false}
                      axisLine={false}
                      tickCount={6}
                      domain={['dataMin', 'dataMax']}
                      tickFormatter={(value) => {
                        if (value >= 1000000) {
                          return `${Math.round(value / 1000000)}`
                        } else if (value >= 1000) {
                          return `${Math.round(value / 1000)}`
                        } else if (value === 0) {
                          return '0'
                        } else {
                          return Math.round(value).toString()
                        }
                      }}
                    />
                    <Tooltip
                      formatter={(value, _) => [
                        typeof value === 'number'
                          ? revenueApi.formatCurrency(value)
                          : value,
                        'Tổng GROSS',
                      ]}
                      labelFormatter={(label) => {
                        if (filterType === 'monthly') {
                          const [year, month] = label.split('-')
                          return `Tháng ${month}/${year}`
                        } else {
                          const date = new Date(label)
                          return `Ngày ${date.getDate()}/${date.getMonth() + 1}/${date.getFullYear()}`
                        }
                      }}
                    />
                    <Legend wrapperStyle={{ fontSize: '12px' }} />
                    <Area
                      type='monotone'
                      dataKey='revenue'
                      stroke='#f97316'
                      strokeWidth={2}
                      fill='#f97316'
                      fillOpacity={0.3}
                      name='Doanh thu'
                    />
                  </AreaChart>
                </ResponsiveContainer>
              </div>
            ) : (
              <div className='flex flex-1 items-center justify-center'>
                <span className='text-muted-foreground text-sm'>
                  Không có dữ liệu biểu đồ
                </span>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
