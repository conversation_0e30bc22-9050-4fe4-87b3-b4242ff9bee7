interface RevenueChartProps {
  data?: Array<{
    date: string
    revenue: number
    orders: number
  }>
  isLoading?: boolean
  error?: string | null
}

export function RevenueChart({
  data = [],
  isLoading = false,
  error = null,
}: RevenueChartProps) {
  // Transform real data for chart display (convert revenue to millions for display)
  const realChartData = data.map((item) => ({
    day: new Date(item.date).getDate().toString(),
    value: item.revenue / 1000000, // Convert to millions for better chart display
  }))

  // Use real data if available, otherwise show fallback
  const chartData =
    !isLoading && !error && realChartData.length > 0
      ? realChartData
      : [
          { day: '1', value: 16.84 },
          { day: '2', value: 15.52 },
          { day: '3', value: 16.23 },
          { day: '4', value: 14.67 },
          { day: '5', value: 13.79 },
          { day: '6', value: 18.65 },
          { day: '7', value: 17.48 },
          { day: '8', value: 15.67 },
          { day: '9', value: 17.63 },
          { day: '10', value: 17.95 },
          { day: '11', value: 19.35 },
          { day: '12', value: 17.47 },
          { day: '13', value: 18.63 },
          { day: '14', value: 17.09 },
          { day: '15', value: 18.01 },
        ]

  const maxValue = Math.max(...chartData.map((d) => d.value))
  const minValue = Math.min(...chartData.map((d) => d.value))

  return (
    <div className='space-y-4'>
      {/* Chart Area */}
      <div className='relative h-[350px] w-full'>
        <svg
          className='h-full w-full'
          viewBox='0 0 800 300'
          preserveAspectRatio='none'
        >
          {/* Grid Lines */}
          <defs>
            <pattern
              id='grid'
              width='47.06'
              height='60'
              patternUnits='userSpaceOnUse'
            >
              <path
                d='M 47.06 0 L 0 0 0 60'
                fill='none'
                stroke='#e5e7eb'
                strokeWidth='0.5'
              />
            </pattern>
          </defs>
          <rect width='100%' height='100%' fill='url(#grid)' />

          {/* Y-axis Grid Lines */}
          {[0, 50, 100, 150].map((y) => (
            <line
              key={y}
              x1='0'
              y1={250 - (y / 150) * 200}
              x2='800'
              y2={250 - (y / 150) * 200}
              stroke='#e5e7eb'
              strokeWidth='0.5'
            />
          ))}

          {/* Chart Line */}
          <polyline
            fill='none'
            stroke='#3b82f6'
            strokeWidth='2'
            points={chartData
              .map((point, index) => {
                const x = (index / (chartData.length - 1)) * 750 + 25
                const y =
                  250 - ((point.value - minValue) / (maxValue - minValue)) * 200
                return `${x},${y}`
              })
              .join(' ')}
          />

          {/* Data Points */}
          {chartData.map((point, index) => {
            const x = (index / (chartData.length - 1)) * 750 + 25
            const y =
              250 - ((point.value - minValue) / (maxValue - minValue)) * 200
            return (
              <g key={index}>
                <circle
                  cx={x}
                  cy={y}
                  r='3'
                  fill='#3b82f6'
                  className='hover:r-5 transition-all'
                />
                <text
                  x={x}
                  y={y - 10}
                  textAnchor='middle'
                  className='fill-current text-xs text-gray-700'
                  fontSize='10'
                >
                  {point.value}
                </text>
              </g>
            )
          })}

          {/* X-axis Labels */}
          {chartData.map((point, index) => {
            const x = (index / (chartData.length - 1)) * 750 + 25
            return (
              <text
                key={index}
                x={x}
                y={280}
                textAnchor='middle'
                className='fill-current text-xs text-gray-500'
                fontSize='12'
              >
                {point.day}
              </text>
            )
          })}
        </svg>

        {/* Y-axis Labels */}
        <div className='absolute top-0 left-0 flex h-full flex-col justify-between py-4 text-xs text-gray-500'>
          <span>150</span>
          <span>100</span>
          <span>50</span>
          <span>0</span>
        </div>
      </div>
    </div>
  )
}
