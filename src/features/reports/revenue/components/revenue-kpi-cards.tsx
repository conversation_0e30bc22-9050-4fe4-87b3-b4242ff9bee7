import { useMemo } from 'react'
import { DollarSign, Users } from 'lucide-react'
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  <PERSON>lt<PERSON>,
  Legend,
  ResponsiveContainer,
} from 'recharts'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'

interface DataState<T> {
  data: T | null
  isLoading: boolean
  error: string | null
}

interface SourcesChartData {
  dailyData: Array<Record<string, number>>
  sourceData: Array<{
    sourceId: string
    sourceName: string
    revenue: number
    bills: number
    percentage: number
  }>
  totalRevenue: number
  totalBills: number
}

interface RevenueKpiCardsProps {
  sources: DataState<SourcesChartData>
  filterType?: 'monthly' | 'daily'
  className?: string
}

export function RevenueKpiCards({
  sources,
  filterType = 'daily',
  className,
}: RevenueKpiCardsProps) {
  const sourcesData = useMemo(
    () => sources.data?.sourceData || [],
    [sources.data?.sourceData]
  )

  const chartData = useMemo(() => {
    if (!sources.data?.dailyData || sources.data.dailyData.length === 0) {
      return []
    }
    return sources.data.dailyData
  }, [sources.data])

  const lineColors = [
    '#f97316', // Orange
    '#3b82f6', // Blue
    '#10b981', // Green
    '#f59e0b', // Amber
    '#ef4444', // Red
    '#8b5cf6', // Purple
    '#06b6d4', // Cyan
  ]

  return (
    <div className={className}>
      <div className='grid gap-3 sm:gap-4 lg:grid-cols-5'>
        <div className='space-y-3 sm:space-y-4 lg:col-span-2'>
          <div className='grid gap-3 sm:grid-cols-2 sm:gap-4'>
            <Card>
              <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
                <CardTitle
                  className='truncate pr-2 text-sm font-medium'
                  title='Tổng tiền GROSS'
                >
                  Tổng tiền GROSS
                </CardTitle>
                <DollarSign className='h-4 w-4 flex-shrink-0' />
              </CardHeader>
              <CardContent className='flex flex-1 flex-col justify-center'>
                <div
                  className='truncate text-2xl font-bold'
                  title={
                    sources.isLoading
                      ? '...'
                      : sources.data
                        ? `${sources.data.totalRevenue.toLocaleString('vi-VN')} ₫`
                        : '0 ₫'
                  }
                >
                  {sources.isLoading
                    ? '...'
                    : sources.data
                      ? `${sources.data.totalRevenue.toLocaleString('vi-VN')}`
                      : '0 ₫'}
                </div>
                <p
                  className='text-muted-foreground truncate text-xs'
                  title={
                    sources.error ? 'Lỗi tải dữ liệu' : 'Tổng doanh thu brutto'
                  }
                >
                  {sources.error ? 'Lỗi tải dữ liệu' : 'Tổng doanh thu brutto'}
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
                <CardTitle
                  className='truncate pr-2 text-sm font-medium'
                  title='Số đơn'
                >
                  Số đơn
                </CardTitle>
                <Users className='h-4 w-4 flex-shrink-0' />
              </CardHeader>
              <CardContent className='flex flex-1 flex-col justify-center'>
                <div
                  className='truncate text-2xl font-bold'
                  title={
                    sources.isLoading
                      ? '...'
                      : sources.data
                        ? sources.data.totalBills.toLocaleString('vi-VN')
                        : '0'
                  }
                >
                  {sources.isLoading
                    ? '...'
                    : sources.data
                      ? sources.data.totalBills.toLocaleString('vi-VN')
                      : '0'}
                </div>
                <p
                  className='text-muted-foreground truncate text-xs'
                  title={sources.error ? 'Lỗi tải dữ liệu' : 'Tổng số đơn hàng'}
                >
                  {sources.error ? 'Lỗi tải dữ liệu' : 'Tổng số đơn hàng'}
                </p>
              </CardContent>
            </Card>
          </div>

          <Card className='flex h-[300px] flex-col sm:h-[360px]'>
            <CardHeader className='flex-shrink-0 pb-2'>
              <CardTitle
                className='truncate text-sm font-medium'
                title='Nguồn bán hàng'
              >
                Nguồn bán hàng
              </CardTitle>
            </CardHeader>
            <CardContent className='flex flex-1 flex-col'>
              <div className='flex flex-1 flex-col'>
                {sources.isLoading ? (
                  <div className='flex flex-1 items-center justify-center'>
                    <span className='text-muted-foreground text-sm'>
                      Đang tải...
                    </span>
                  </div>
                ) : sources.error ? (
                  <div className='flex flex-1 items-center justify-center'>
                    <span className='text-sm text-red-500'>
                      Lỗi tải dữ liệu nguồn bán hàng
                    </span>
                  </div>
                ) : sourcesData.length > 0 ? (
                  <>
                    <div className='flex-1 overflow-x-auto overflow-y-auto'>
                      <table className='w-full min-w-full'>
                        <thead>
                          <tr className='border-b'>
                            <th className='text-muted-foreground px-1 py-2 text-left text-xs font-medium sm:px-2 sm:text-sm'>
                              <span className='block truncate' title='Nguồn'>
                                Nguồn
                              </span>
                            </th>
                            <th className='text-muted-foreground px-1 py-2 text-right text-xs font-medium sm:px-2 sm:text-sm'>
                              <span
                                className='block truncate'
                                title='Tổng tiền GROSS'
                              >
                                Tổng tiền GROSS
                              </span>
                            </th>
                            <th className='text-muted-foreground px-1 py-2 text-right text-xs font-medium sm:px-2 sm:text-sm'>
                              <span className='block truncate' title='Số đơn'>
                                Số đơn
                              </span>
                            </th>
                          </tr>
                        </thead>
                        <tbody>
                          {sourcesData.map((source, index) => (
                            <tr
                              key={source.sourceId}
                              className='border-b last:border-b-0'
                            >
                              <td className='px-1 py-2 text-xs sm:px-2 sm:py-3 sm:text-sm'>
                                <span
                                  className={
                                    index === 0
                                      ? 'block truncate font-medium text-orange-600'
                                      : 'block truncate font-medium'
                                  }
                                  title={source.sourceName}
                                >
                                  {source.sourceName}
                                </span>
                              </td>
                              <td className='px-1 py-2 text-right text-xs sm:px-2 sm:py-3 sm:text-sm'>
                                <span
                                  className={`block truncate font-medium ${index === 0 ? 'text-orange-600' : ''}`}
                                  title={`${source.revenue.toLocaleString('vi-VN')} ₫`}
                                >
                                  {source.revenue.toLocaleString('vi-VN')} ₫
                                </span>
                              </td>
                              <td className='text-muted-foreground px-1 py-2 text-right text-xs sm:px-2 sm:py-3 sm:text-sm'>
                                <span
                                  className='block truncate'
                                  title={source.bills.toLocaleString('vi-VN')}
                                >
                                  {source.bills.toLocaleString('vi-VN')}
                                </span>
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </>
                ) : (
                  <div className='flex flex-1 items-center justify-center'>
                    <span className='text-muted-foreground text-sm'>
                      Không có dữ liệu nguồn bán hàng
                    </span>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        <Card className='flex h-[525px] flex-col lg:col-span-3'>
          <CardHeader className='flex-shrink-0 pb-2'>
            <CardTitle
              className='truncate text-sm font-medium'
              title={
                filterType === 'monthly'
                  ? 'Tổng tiền GROSS theo nguồn (theo tháng)'
                  : 'Tổng tiền GROSS theo nguồn (theo ngày)'
              }
            >
              {filterType === 'monthly'
                ? 'Tổng tiền GROSS theo nguồn (theo tháng)'
                : 'Tổng tiền GROSS theo nguồn (theo ngày)'}
            </CardTitle>
          </CardHeader>
          <CardContent className='flex flex-1 flex-col'>
            {sources.isLoading ? (
              <div className='flex flex-1 items-center justify-center'>
                <span className='text-muted-foreground text-sm'>
                  Đang tải...
                </span>
              </div>
            ) : sources.error ? (
              <div className='flex flex-1 items-center justify-center'>
                <span className='text-sm text-red-500'>
                  Lỗi tải dữ liệu biểu đồ
                </span>
              </div>
            ) : chartData.length > 0 ? (
              <div className='flex-1'>
                <ResponsiveContainer width='100%' height='100%'>
                  <LineChart data={chartData}>
                    <CartesianGrid strokeDasharray='3 3' />
                    <XAxis
                      dataKey='date'
                      tickFormatter={(value) => {
                        if (filterType === 'monthly') {
                          const [year, month] = value.split('-')
                          return `${month}/${year.slice(-2)}`
                        } else {
                          const date = new Date(value)
                          const day = date.getDate()
                          const month = date.getMonth() + 1
                          return `${day}/${month}`
                        }
                      }}
                      fontSize={12}
                      tickLine={false}
                      axisLine={false}
                      interval={0}
                      angle={-35}
                      textAnchor='end'
                      height={60}
                    />
                    <YAxis
                      fontSize={12}
                      tickLine={false}
                      axisLine={false}
                      tickCount={6}
                      domain={['dataMin', 'dataMax']}
                      tickFormatter={(value) => {
                        if (value >= 1000000) {
                          return `${Math.round(value / 1000000)}tr`
                        } else if (value >= 1000) {
                          return `${Math.round(value / 1000)}k`
                        } else if (value === 0) {
                          return '0'
                        } else {
                          return Math.round(value).toString()
                        }
                      }}
                    />

                    <Tooltip
                      formatter={(value, name) => [
                        typeof value === 'number'
                          ? value.toLocaleString('vi-VN') + ' ₫'
                          : value,
                        name,
                      ]}
                      labelFormatter={(label) => {
                        if (filterType === 'monthly') {
                          // label format: "2024-12" or "2025-01"
                          const [year, month] = label.split('-')
                          return `Tháng ${month}/${year}`
                        } else {
                          const date = new Date(label)
                          return `Ngày ${date.getDate()}/${date.getMonth() + 1}/${date.getFullYear()}`
                        }
                      }}
                    />
                    <Legend wrapperStyle={{ fontSize: '12px' }} />
                    {sourcesData.map((source, index) => (
                      <Line
                        key={source.sourceId}
                        type='monotone'
                        dataKey={source.sourceName}
                        name={source.sourceName}
                        stroke={lineColors[index % lineColors.length]}
                        strokeWidth={2}
                        dot={false}
                        activeDot={{ r: 5 }}
                      />
                    ))}
                  </LineChart>
                </ResponsiveContainer>
              </div>
            ) : (
              <div className='flex flex-1 items-center justify-center'>
                <span className='text-muted-foreground text-sm'>
                  Không có dữ liệu biểu đồ
                </span>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
