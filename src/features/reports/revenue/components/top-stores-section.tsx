import { useReportsContext } from '@/hooks/use-reports-context'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { TopDeletedStores } from './top-deleted-stores'
import { TopItems } from './top-items'
import { TopStores } from './top-stores'
import { TopStoresChart } from './top-stores-chart'

export function TopStoresSection() {
  const { dateRange, filterType } = useReportsContext()

  return (
    <div className='space-y-4 sm:space-y-6'>
      {/* Top Stores Row */}
      <div className='grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4'>
        <Card className='h-full'>
          <CardHeader className='pb-3'>
            <CardTitle className='text-sm font-medium'>
              5 CH ÍT ĐƠN NHẤT
            </CardTitle>
            <CardDescription className='text-xs'>
              Cửa hàng có số đơn thấp nhất
            </CardDescription>
          </CardHeader>
          <CardContent className='pt-0'>
            <TopStores
              type='least-sales'
              dateRange={dateRange}
              filterType={filterType}
            />
          </CardContent>
        </Card>

        <Card className='h-full'>
          <CardHeader className='pb-3'>
            <CardTitle className='text-sm font-medium'>
              5 CH NHIỀU ĐƠN NHẤT
            </CardTitle>
            <CardDescription className='text-xs'>
              Cửa hàng có số đơn cao nhất
            </CardDescription>
          </CardHeader>
          <CardContent className='pt-0'>
            <TopStores
              type='most-sales'
              dateRange={dateRange}
              filterType={filterType}
            />
          </CardContent>
        </Card>

        <Card className='h-full'>
          <CardHeader className='pb-3'>
            <CardTitle className='text-sm font-medium'>
              5 CH DS THẤP NHẤT
            </CardTitle>
            <CardDescription className='text-xs'>
              Cửa hàng có doanh số thấp nhất
            </CardDescription>
          </CardHeader>
          <CardContent className='pt-0'>
            <TopStores
              type='least-revenue'
              dateRange={dateRange}
              filterType={filterType}
            />
          </CardContent>
        </Card>

        <Card className='h-full'>
          <CardHeader className='pb-3'>
            <CardTitle className='text-sm font-medium'>
              5 CH DS CAO NHẤT
            </CardTitle>
            <CardDescription className='text-xs'>
              Cửa hàng có doanh số cao nhất
            </CardDescription>
          </CardHeader>
          <CardContent className='pt-0'>
            <TopStores
              type='most-revenue'
              dateRange={dateRange}
              filterType={filterType}
            />
          </CardContent>
        </Card>
      </div>

      {/* Charts and Items Row */}
      <div className='grid grid-cols-1 gap-4 lg:grid-cols-4'>
        <Card className='h-full lg:col-span-2'>
          <CardHeader className='pb-3'>
            <CardTitle className='text-sm font-medium'>
              TOP 5 CH DOANH SỐ BÁN CHẬM
            </CardTitle>
            <CardDescription className='text-xs'>
              Biểu đồ doanh số 5 cửa hàng bán chậm nhất
            </CardDescription>
          </CardHeader>
          <CardContent className='pt-0'>
            <TopStoresChart />
          </CardContent>
        </Card>

        <Card className='h-full'>
          <CardHeader className='pb-3'>
            <CardTitle className='text-sm font-medium'>
              DS MẶT HÀNG BÁN CHẬM NHẤT
            </CardTitle>
            <CardDescription className='text-xs'>
              5 mặt hàng có số lượng bán thấp nhất
            </CardDescription>
          </CardHeader>
          <CardContent className='pt-0'>
            <TopItems dateRange={dateRange} filterType={filterType} />
          </CardContent>
        </Card>

        <Card className='h-full'>
          <CardHeader className='pb-3'>
            <CardTitle className='text-sm font-medium'>
              DS CH HUỶ ĐƠN NHIỀU NHẤT
            </CardTitle>
            <CardDescription className='text-xs'>
              Cửa hàng có số lượng đơn hàng bị huỷ cao nhất
            </CardDescription>
          </CardHeader>
          <CardContent className='pt-0'>
            <TopDeletedStores dateRange={dateRange} />
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
