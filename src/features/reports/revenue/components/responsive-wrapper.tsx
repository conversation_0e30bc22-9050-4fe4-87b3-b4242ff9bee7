import { ReactNode } from 'react'
import { cn } from '@/lib/utils'
import { responsiveClasses } from '@/utils/responsive-classes'

interface ResponsiveWrapperProps {
  children: ReactNode
  className?: string
  variant?: 'container' | 'grid-1' | 'grid-2' | 'grid-3' | 'grid-4' | 'grid-5'
  spacing?: 'sm' | 'md' | 'lg'
  padding?: 'sm' | 'md' | 'lg'
}

export function ResponsiveWrapper({
  children,
  className,
  variant = 'container',
  spacing = 'md',
  padding = 'md',
}: ResponsiveWrapperProps) {
  const baseClasses = {
    container: responsiveClasses.container.main,
    'grid-1': responsiveClasses.grid.grid1,
    'grid-2': responsiveClasses.grid.grid2,
    'grid-3': responsiveClasses.grid.grid3,
    'grid-4': responsiveClasses.grid.grid4,
    'grid-5': responsiveClasses.grid.grid5,
  }

  const spacingClasses = {
    sm: responsiveClasses.spacing.spacingSm,
    md: responsiveClasses.spacing.spacingMd,
    lg: responsiveClasses.spacing.spacingLg,
  }

  const paddingClasses = {
    sm: responsiveClasses.spacing.paddingSm,
    md: responsiveClasses.spacing.paddingMd,
    lg: responsiveClasses.spacing.paddingLg,
  }

  return (
    <div
      className={cn(
        baseClasses[variant],
        variant === 'container' && spacingClasses[spacing],
        variant === 'container' && paddingClasses[padding],
        className
      )}
    >
      {children}
    </div>
  )
}

interface ResponsiveCardProps {
  children: ReactNode
  className?: string
  title?: string
  description?: string
  size?: 'sm' | 'md' | 'lg'
}

export function ResponsiveCard({
  children,
  className,
  title,
  description,
  size = 'md',
}: ResponsiveCardProps) {
  const sizeClasses = {
    sm: responsiveClasses.card.heightSm,
    md: responsiveClasses.card.heightMd,
    lg: responsiveClasses.card.heightLg,
  }

  return (
    <div
      className={cn(responsiveClasses.card.base, sizeClasses[size], className)}
    >
      {(title || description) && (
        <div className={responsiveClasses.card.headerPadding}>
          {title && (
            <h3
              className={cn(
                responsiveClasses.typography.titleTruncate,
                'leading-none tracking-tight'
              )}
              title={title}
            >
              {title}
            </h3>
          )}
          {description && (
            <p
              className={cn(
                responsiveClasses.typography.subtitleTruncate,
                'mt-1'
              )}
              title={description}
            >
              {description}
            </p>
          )}
        </div>
      )}
      <div
        className={cn(
          responsiveClasses.card.contentPadding,
          responsiveClasses.card.flexContent
        )}
      >
        {children}
      </div>
    </div>
  )
}

interface ResponsiveChartProps {
  children: ReactNode
  className?: string
  height?: 'sm' | 'md' | 'lg' | 'xl'
}

export function ResponsiveChart({
  children,
  className,
  height = 'md',
}: ResponsiveChartProps) {
  const heightClasses = {
    sm: responsiveClasses.chart.containerSm,
    md: responsiveClasses.chart.containerMd,
    lg: responsiveClasses.chart.containerLg,
    xl: responsiveClasses.chart.containerXl,
  }

  return <div className={cn(heightClasses[height], className)}>{children}</div>
}

interface ResponsiveTableProps {
  children: ReactNode
  className?: string
}

export function ResponsiveTable({ children, className }: ResponsiveTableProps) {
  return (
    <div
      className={cn(
        responsiveClasses.table.container,
        'scrollbar-thin scrollbar-thumb-border scrollbar-track-transparent',
        className
      )}
    >
      <div className={responsiveClasses.table.base}>{children}</div>
    </div>
  )
}

interface ResponsiveButtonGroupProps {
  children: ReactNode
  className?: string
  variant?: 'flex' | 'grid'
}

export function ResponsiveButtonGroup({
  children,
  className,
  variant = 'flex',
}: ResponsiveButtonGroupProps) {
  const variantClasses = {
    flex: responsiveClasses.button.groupFlex,
    grid: responsiveClasses.button.groupGrid,
  }

  return (
    <div
      className={cn(
        variantClasses[variant],
        responsiveClasses.utility.wFull,
        className
      )}
    >
      {children}
    </div>
  )
}

interface ResponsiveTextProps {
  children: ReactNode
  className?: string
  variant?: 'title' | 'subtitle' | 'value' | 'value-small'
  truncate?: boolean
  title?: string
}

export function ResponsiveText({
  children,
  className,
  variant = 'title',
  truncate = false,
  title,
}: ResponsiveTextProps) {
  const variantClasses = {
    title: truncate
      ? responsiveClasses.typography.titleTruncate
      : responsiveClasses.typography.title,
    subtitle: truncate
      ? responsiveClasses.typography.subtitleTruncate
      : responsiveClasses.typography.subtitle,
    value: truncate
      ? responsiveClasses.typography.valueTruncate
      : responsiveClasses.typography.value,
    'value-small': truncate
      ? responsiveClasses.typography.valueSmallTruncate
      : responsiveClasses.typography.valueSmall,
  }

  return (
    <div
      className={cn(
        variantClasses[variant],
        !truncate && responsiveClasses.utility.textBreak,
        className
      )}
      title={
        title ||
        (truncate && typeof children === 'string' ? children : undefined)
      }
    >
      {children}
    </div>
  )
}
