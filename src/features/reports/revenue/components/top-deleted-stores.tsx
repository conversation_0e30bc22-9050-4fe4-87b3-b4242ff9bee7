import { useMemo, useEffect } from 'react'
import { AlertTriangle, CheckCircle } from 'lucide-react'
import { useDeletedStores } from '@/hooks/use-deleted-stores'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { ResponsiveText } from './responsive-wrapper'

interface TopDeletedStoresProps {
  dateRange: {
    from: Date
    to: Date
  }
  selectedStores?: string[]
  className?: string
}

export function TopDeletedStores({
  dateRange,
  selectedStores,
  className,
}: TopDeletedStoresProps) {
  const isDateRangeTooLarge = useMemo(() => {
    if (!dateRange?.from || !dateRange?.to) return false
    const diffInMs = dateRange.to.getTime() - dateRange.from.getTime()
    const diffInDays = diffInMs / (1000 * 60 * 60 * 24)
    return diffInDays > 90 // 3 months = ~90 days
  }, [dateRange])

  const { topStores, isLoading, error, refetch } = useDeletedStores({
    dateRange,
    selectedStores,
    limit: 7,
    autoFetch: !isDateRangeTooLarge,
  })

  const fromTime = dateRange?.from?.getTime()
  const toTime = dateRange?.to?.getTime()

  useEffect(() => {
    if (dateRange?.from && dateRange?.to && !isDateRangeTooLarge) {
      refetch()
    }
  }, [
    fromTime,
    toTime,
    isDateRangeTooLarge,
    refetch,
    dateRange?.from,
    dateRange?.to,
  ])

  if (typeof window !== 'undefined') {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    ;(window as any).forceRefreshDeletedStores = refetch
  }

  if (isDateRangeTooLarge) {
    return (
      <div className={className}>
        <Alert className='border-yellow-200 bg-yellow-50'>
          <AlertTriangle className='h-4 w-4 text-yellow-600' />
          <AlertDescription className='text-xs text-yellow-800'>
            Khoảng thời gian quá lớn (hơn 3 tháng). Vui lòng chọn khoảng thời
            gian nhỏ hơn để tải dữ liệu hoá đơn huỷ.
          </AlertDescription>
        </Alert>
      </div>
    )
  }

  if (isLoading) {
    return (
      <div className='space-y-3'>
        {[...Array(5)].map((_, i) => (
          <div key={i} className='flex items-center justify-between'>
            <div className='flex items-center space-x-3'>
              <div className='h-6 w-6 animate-pulse rounded-full bg-gray-200' />
              <div className='h-4 w-32 animate-pulse rounded bg-gray-200' />
            </div>
            <div className='h-6 w-16 animate-pulse rounded bg-gray-200' />
          </div>
        ))}
      </div>
    )
  }
  if (error) {
    return (
      <div className={className}>
        <div className='py-4 text-center'>
          <AlertTriangle className='text-destructive mx-auto mb-2 h-6 w-6' />
          <ResponsiveText
            variant='subtitle'
            className='text-destructive text-xs'
          >
            Không thể tải dữ liệu
          </ResponsiveText>
        </div>
      </div>
    )
  }

  if (topStores.length === 0) {
    return (
      <div className={className}>
        <div className='py-4 text-center'>
          <CheckCircle className='mx-auto mb-2 h-6 w-6 text-green-500' />
          <ResponsiveText variant='subtitle' className='text-xs text-green-600'>
            Không có đơn hàng bị huỷ
          </ResponsiveText>
        </div>
      </div>
    )
  }

  return (
    <div className={className}>
      <div className='space-y-3'>
        {/* Top stores list */}
        {topStores.map((store, index) => (
          <div
            key={store.storeUid}
            className='flex items-center justify-between'
          >
            <div className='flex min-w-0 flex-1 items-center space-x-3'>
              <Badge
                variant={
                  index === 0
                    ? 'destructive'
                    : index === 1
                      ? 'secondary'
                      : 'outline'
                }
                className='flex h-6 w-6 flex-shrink-0 items-center justify-center rounded-full p-0 text-xs font-medium'
              >
                {index + 1}
              </Badge>
              <div className='min-w-0 flex-1'>
                <ResponsiveText
                  variant='subtitle'
                  truncate
                  title={store.storeName}
                  className='font-medium'
                >
                  {store.storeName}
                </ResponsiveText>
              </div>
            </div>
            <div className='text-right'>
              <Badge
                variant={index === 0 ? 'destructive' : 'secondary'}
                className='text-xs'
              >
                {store.totalDeleted}
              </Badge>
            </div>
          </div>
        ))}

        {topStores.length === 0 && (
          <div className='py-4 text-center'>
            <p className='text-muted-foreground text-sm'>Không có dữ liệu</p>
          </div>
        )}
      </div>
    </div>
  )
}

export default TopDeletedStores
