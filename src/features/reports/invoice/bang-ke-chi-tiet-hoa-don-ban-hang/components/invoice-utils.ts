// Utility functions for invoice formatting and processing

export function formatCurrency(amount: number): string {
  return new Intl.NumberFormat('vi-VN', {
    style: 'currency',
    currency: 'VND',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(amount)
}

export function formatDate(timestamp: number): string {
  return new Date(timestamp * 1000).toLocaleDateString('vi-VN')
}

export function formatTime(timestamp: number): string {
  return new Date(timestamp * 1000).toLocaleTimeString('vi-VN', {
    hour: '2-digit',
    minute: '2-digit',
  })
}

export function formatPercentage(value: number): string {
  return `${value.toFixed(2)}%`
}

export function getPaymentMethodBadgeVariant(
  paymentMethod: string
): 'default' | 'secondary' | 'destructive' | 'outline' {
  switch (paymentMethod?.toLowerCase()) {
    case 'cod':
    case 'cash':
      return 'default'
    case 'card':
    case 'banking':
      return 'secondary'
    default:
      return 'outline'
  }
}

export function getTableServiceBadgeVariant(
  serviceType: string
): 'default' | 'secondary' | 'destructive' | 'outline' {
  switch (serviceType?.toLowerCase()) {
    case 'dine-in':
    case 'tại chỗ':
      return 'default'
    case 'takeaway':
    case 'mang về':
      return 'secondary'
    case 'delivery':
    case 'giao hàng':
      return 'destructive'
    default:
      return 'outline'
  }
}

// Props interface
export interface InvoiceListProps {
  dateRange?: {
    from: Date
    to: Date
  }
  selectedStores?: string[]
  sourceId?: number
  pageSize?: number
  className?: string
  showStoreInfo?: boolean
  showEmployeeInfo?: boolean
  showPaymentMethod?: boolean
  showPagination?: boolean
  paymentMethodId?: string
}
