import { useMemo, useEffect, useState } from 'react'
import { usePosStores } from '@/stores/posStore'
import { cn } from '@/lib/utils'
import { useAccountingSalesAll } from '@/hooks/use-accounting-sales-all'
import { Badge } from '@/components/ui/badge'
import { invoiceColumns } from './invoice-columns'
import { DataTable } from './invoice-data-table'
import { InvoiceTableSkeleton } from './invoice-table-skeleton'
import { formatCurrency, type InvoiceListProps } from './invoice-utils'

// Main Invoice List Component

export function InvoiceList({
  dateRange,
  selectedStores = ['all-stores'],
  pageSize = 20,
  className,
  showPagination = true,
}: InvoiceListProps) {
  // Get current brand to ensure component re-renders when brand changes
  const { selectedBrand } = usePosStores()

  // Force re-render when brand changes
  const [forceRender, setForceRender] = useState(0)

  useEffect(() => {
    const handleBrandChange = () => {
      setForceRender((prev) => prev + 1)
    }

    window.addEventListener('brandChanged', handleBrandChange)
    return () => {
      window.removeEventListener('brandChanged', handleBrandChange)
    }
  }, [])

  const {
    data: salesData,
    totalAmount,
    totalTransactions: _totalTransactions,
    isLoading,
    error,
    refetch: _refetch,
  } = useAccountingSalesAll({
    dateRange,
    selectedStores,
    autoFetch: true,
  })

  const allInvoicesData = useMemo(() => {
    return salesData
  }, [salesData, selectedBrand?.id, forceRender])

  if (error) {
    return (
      <div className={cn('text-center text-red-500', className)}>
        Lỗi khi tải dữ liệu: {error}
      </div>
    )
  }

  return (
    <div
      className={cn('w-full max-w-full space-y-4 overflow-hidden', className)}
    >
      {/* Summary Header */}
      <div className='space-y-2'>
        <div className='flex items-center justify-between'>
          <div className='flex items-center gap-2'>
            <h3 className='text-lg font-semibold'>
              Bảng Kê Chi Tiết Hoá Đơn Bán Hàng
            </h3>
            <Badge variant='outline' className='text-xs'>
              Tổng: {allInvoicesData.length} hoá đơn
            </Badge>
            <Badge variant='secondary' className='text-xs'>
              {formatCurrency(totalAmount)}
            </Badge>
          </div>
          {/* <QuickExportButton
            type='custom'
            data={excelData}
            filename={`bang-ke-chi-tiet-hoa-don-${new Date().toISOString().split('T')[0]}`}
            disabled={allInvoicesData.length === 0}
          /> */}
        </div>
      </div>

      {/* Table */}
      {isLoading && <InvoiceTableSkeleton />}

      {!isLoading && (
        <>
          <DataTable
            columns={invoiceColumns()}
            data={allInvoicesData}
            showPagination={showPagination}
            pageSize={pageSize}
          />
        </>
      )}
    </div>
  )
}

export default InvoiceList
