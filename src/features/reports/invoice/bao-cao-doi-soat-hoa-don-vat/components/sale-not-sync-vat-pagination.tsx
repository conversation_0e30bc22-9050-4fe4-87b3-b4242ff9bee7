import { useMemo, useState } from 'react'
import {
  ChevronLeftIcon,
  ChevronRightIcon,
  DoubleArrowLeftIcon,
  DoubleArrowRightIcon,
} from '@radix-ui/react-icons'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'

interface SaleNotSyncVatPaginationProps {
  data: unknown[]
  pageSize?: number
  className?: string
  showPageSizeSelector?: boolean
  showItemCount?: boolean
  showFirstLastButtons?: boolean
  pageSizeOptions?: number[]
  disabled?: boolean
  children: (
    paginatedData: unknown[],
    paginationInfo: PaginationInfo
  ) => React.ReactNode
}

interface PaginationInfo {
  currentPage: number
  totalPages: number
  pageSize: number
  totalItems: number
  startIndex: number
  endIndex: number
  onPageChange: (page: number) => void
  onPageSizeChange: (pageSize: number) => void
}

export function SaleNotSyncVatPagination({
  data,
  pageSize: defaultPageSize = 20,
  className,
  showPageSizeSelector = true,
  showItemCount = true,
  showFirstLastButtons = true,
  pageSizeOptions = [10, 20, 50, 100],
  disabled = false,
  children,
}: SaleNotSyncVatPaginationProps) {
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize, setPageSize] = useState(defaultPageSize)

  const totalItems = data.length
  const totalPages = Math.ceil(totalItems / pageSize)

  // Ensure current page is valid
  const validCurrentPage = useMemo(() => {
    return Math.min(Math.max(1, currentPage), totalPages || 1)
  }, [currentPage, totalPages])

  // Calculate pagination
  const startIndex = (validCurrentPage - 1) * pageSize
  const endIndex = Math.min(startIndex + pageSize, totalItems)

  const paginatedData = useMemo(() => {
    return data.slice(startIndex, endIndex)
  }, [data, startIndex, endIndex])

  const handlePageChange = (page: number) => {
    setCurrentPage(page)
  }

  const handlePageSizeChange = (newPageSize: number) => {
    setPageSize(newPageSize)
    setCurrentPage(1) // Reset to first page when page size changes
  }

  const paginationInfo: PaginationInfo = {
    currentPage: validCurrentPage,
    totalPages,
    pageSize,
    totalItems,
    startIndex,
    endIndex,
    onPageChange: handlePageChange,
    onPageSizeChange: handlePageSizeChange,
  }

  return (
    <div className='space-y-4'>
      {children(paginatedData, paginationInfo)}

      {totalItems > 0 && (
        <div className={cn('flex items-center justify-between', className)}>
          <div className='flex items-center space-x-2'>
            {showItemCount && (
              <p className='text-muted-foreground text-sm'>
                Hiển thị {startIndex + 1} - {endIndex} của {totalItems} kết quả
              </p>
            )}
          </div>

          <div className='flex items-center space-x-2'>
            {showPageSizeSelector && (
              <div className='flex items-center space-x-2'>
                <p className='text-sm font-medium'>Hiển thị</p>
                <Select
                  value={pageSize.toString()}
                  onValueChange={(value) => handlePageSizeChange(Number(value))}
                  disabled={disabled}
                >
                  <SelectTrigger className='h-8 w-[70px]'>
                    <SelectValue placeholder={pageSize} />
                  </SelectTrigger>
                  <SelectContent side='top'>
                    {pageSizeOptions.map((size) => (
                      <SelectItem key={size} value={size.toString()}>
                        {size}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <p className='text-sm font-medium'>dòng</p>
              </div>
            )}

            <div className='flex items-center space-x-2'>
              <p className='text-sm font-medium'>
                Trang {validCurrentPage} / {totalPages}
              </p>
              <div className='flex items-center space-x-1'>
                {showFirstLastButtons && (
                  <Button
                    variant='outline'
                    className='hidden h-8 w-8 p-0 lg:flex'
                    onClick={() => handlePageChange(1)}
                    disabled={validCurrentPage <= 1 || disabled}
                  >
                    <span className='sr-only'>Trang đầu</span>
                    <DoubleArrowLeftIcon className='h-4 w-4' />
                  </Button>
                )}
                <Button
                  variant='outline'
                  className='h-8 w-8 p-0'
                  onClick={() => handlePageChange(validCurrentPage - 1)}
                  disabled={validCurrentPage <= 1 || disabled}
                >
                  <span className='sr-only'>Trang trước</span>
                  <ChevronLeftIcon className='h-4 w-4' />
                </Button>
                <Button
                  variant='outline'
                  className='h-8 w-8 p-0'
                  onClick={() => handlePageChange(validCurrentPage + 1)}
                  disabled={validCurrentPage >= totalPages || disabled}
                >
                  <span className='sr-only'>Trang sau</span>
                  <ChevronRightIcon className='h-4 w-4' />
                </Button>
                {showFirstLastButtons && (
                  <Button
                    variant='outline'
                    className='hidden h-8 w-8 p-0 lg:flex'
                    onClick={() => handlePageChange(totalPages)}
                    disabled={validCurrentPage >= totalPages || disabled}
                  >
                    <span className='sr-only'>Trang cuối</span>
                    <DoubleArrowRightIcon className='h-4 w-4' />
                  </Button>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default SaleNotSyncVatPagination
