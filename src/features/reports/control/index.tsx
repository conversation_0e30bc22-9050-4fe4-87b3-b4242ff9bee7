import { useCurrentBrand } from '@/stores/posStore'
import { Button } from '@/components/ui/button'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/ui/tabs'
import { Header } from '@/components/layout/header'
import { Main } from '@/components/layout/main'
import { TopNav } from '@/components/layout/top-nav'
import { ProfileDropdown } from '@/components/profile-dropdown'
import { Search } from '@/components/search'
import { ThemeSwitch } from '@/components/theme-switch'
import { ControlOverview } from './components/control-overview'

export default function Reports() {
  const { selectedBrand } = useCurrentBrand()

  const brandName = selectedBrand?.name

  const pageTitle = brandName
    ? `Báo Cáo <PERSON> Soát ${brandName}`
    : 'Báo Cáo Ki<PERSON>m Soát'

  return (
    <>
      {/* ===== Top Heading ===== */}
      <Header>
        <TopNav links={topNav} />
        <div className='ml-auto flex items-center space-x-4'>
          <Search />
          <ThemeSwitch />
          <ProfileDropdown />
        </div>
      </Header>

      {/* ===== Main ===== */}
      <Main>
        <div className='mb-2 flex items-center justify-between space-y-2'>
          <h1 className='text-2xl font-bold tracking-tight'>{pageTitle}</h1>
          <div className='flex items-center space-x-2'>
            <Button variant='outline'>Xuất Excel</Button>
            <Button>Tải Báo Cáo</Button>
          </div>
        </div>
        <Tabs
          orientation='vertical'
          defaultValue='overview'
          className='space-y-4'
        >
          <div className='w-full overflow-x-auto pb-2'>
            <TabsList>
              <TabsTrigger value='overview'>Tổng Quan</TabsTrigger>
              <TabsTrigger value='stores'>Theo Cửa Hàng</TabsTrigger>
              <TabsTrigger value='products'>Theo Sản Phẩm</TabsTrigger>
              <TabsTrigger value='time'>Theo Thời Gian</TabsTrigger>
            </TabsList>
          </div>
          <TabsContent value='overview' className='space-y-4'>
            <ControlOverview />
          </TabsContent>
          <TabsContent value='stores' className='space-y-4'>
            <div className='py-8 text-center'>
              <h3 className='text-lg font-medium'>Báo cáo theo sản phẩm</h3>
              <p className='text-muted-foreground mt-2'>
                Tính năng đang được phát triển
              </p>
            </div>
          </TabsContent>
          <TabsContent value='products' className='space-y-4'>
            <div className='py-8 text-center'>
              <h3 className='text-lg font-medium'>Báo cáo theo sản phẩm</h3>
              <p className='text-muted-foreground mt-2'>
                Tính năng đang được phát triển
              </p>
            </div>
          </TabsContent>
          <TabsContent value='time' className='space-y-4'>
            <div className='py-8 text-center'>
              <h3 className='text-lg font-medium'>Báo cáo theo thời gian</h3>
              <p className='text-muted-foreground mt-2'>
                Tính năng đang được phát triển
              </p>
            </div>
          </TabsContent>
        </Tabs>
      </Main>
    </>
  )
}

const topNav = [
  {
    title: 'Tổng Quan',
    href: '/bao-cao/doanh-thu',
    isActive: true,
    disabled: false,
  },
  {
    title: 'Doanh Thu Net',
    href: '/bao-cao/doanh-thu/net',
    isActive: false,
    disabled: true,
  },
  {
    title: 'Theo Cửa Hàng',
    href: '/bao-cao/doanh-thu/cua-hang',
    isActive: false,
    disabled: true,
  },
  {
    title: 'Theo Khu Vực',
    href: '/bao-cao/doanh-thu/khu-vuc',
    isActive: false,
    disabled: true,
  },
]
