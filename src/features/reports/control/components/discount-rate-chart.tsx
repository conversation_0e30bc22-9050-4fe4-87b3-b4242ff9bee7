import { useMemo } from 'react'
import { TrendingUp, TrendingDown, Percent } from 'lucide-react'
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Cell,
} from 'recharts'
import { useStoresDiscountData } from '@/hooks/use-stores-discount-data'
import { Badge } from '@/components/ui/badge'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'

interface DiscountRateChartProps {
  dateRange: {
    from: Date
    to: Date
  }
  selectedStores?: string[]
  className?: string
}

const getBarColor = (discountRate: number) => {
  if (discountRate >= 15) return '#ef4444' // Red for high discount rates (15%+)
  if (discountRate >= 10) return '#f97316' // Orange for medium-high (10-15%)
  if (discountRate >= 5) return '#eab308' // Yellow for medium (5-10%)
  return '#22c55e' // Green for low discount rates (<5%)
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const CustomTooltip = ({ active, payload, label }: any) => {
  if (active && payload && payload.length) {
    const data = payload[0].payload
    return (
      <div className='rounded-lg border border-gray-200 bg-white p-3 shadow-lg'>
        <p className='mb-2 font-medium text-gray-900'>{label}</p>
        <div className='space-y-1 text-sm'>
          <p className='flex items-center gap-2'>
            <Percent className='h-3 w-3 text-blue-500' />
            <span>
              Tỷ lệ chiết khấu: <strong>{data.discountRate}%</strong>
            </span>
          </p>
          <p className='text-gray-600'>
            Doanh thu gốc:{' '}
            {new Intl.NumberFormat('vi-VN').format(data.revenueGross)} VNĐ
          </p>
          <p className='text-gray-600'>
            Chiết khấu:{' '}
            {new Intl.NumberFormat('vi-VN').format(data.discountAmount)} VNĐ
          </p>
          <p className='text-gray-600'>
            Số đơn: {new Intl.NumberFormat('vi-VN').format(data.totalSales)}
          </p>
        </div>
      </div>
    )
  }
  return null
}

export function DiscountRateChart({
  dateRange,
  selectedStores,
  className,
}: DiscountRateChartProps) {
  const { storesData, isLoading, error, totalStores, averageDiscountRate } =
    useStoresDiscountData({
      dateRange,
      selectedStores,
      autoFetch: true,
    })

  const chartData = useMemo(() => {
    return storesData.map((store) => ({
      name:
        store.storeName.length > 15
          ? `${store.storeName.substring(0, 15)}...`
          : store.storeName,
      fullName: store.storeName,
      discountRate: store.discountRate,
      discountAmount: store.discountAmount,
      revenueGross: store.revenueGross,
      totalSales: store.totalSales,
      color: getBarColor(store.discountRate),
    }))
  }, [storesData])

  // Calculate statistics
  const highestDiscountRate = useMemo(() => {
    return storesData.length > 0 ? storesData[0].discountRate : 0
  }, [storesData])

  const lowestDiscountRate = useMemo(() => {
    return storesData.length > 0
      ? storesData[storesData.length - 1].discountRate
      : 0
  }, [storesData])

  if (isLoading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className='flex items-center gap-2'>
            <Percent className='h-5 w-5' />
            Tỷ lệ chiết khấu theo cửa hàng
          </CardTitle>
          <CardDescription>
            Biểu đồ tỷ lệ chiết khấu của các cửa hàng
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className='flex h-80 items-center justify-center'>
            <div className='text-center'>
              <div className='border-primary mx-auto mb-2 h-8 w-8 animate-spin rounded-full border-b-2'></div>
              <p className='text-muted-foreground text-sm'>
                Đang tải dữ liệu...
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className='flex items-center gap-2'>
            <Percent className='h-5 w-5' />
            Tỷ lệ chiết khấu theo cửa hàng
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className='flex h-80 items-center justify-center'>
            <div className='text-center text-red-500'>
              <p className='font-medium'>Lỗi tải dữ liệu</p>
              <p className='text-sm'>{error}</p>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (chartData.length === 0) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className='flex items-center gap-2'>
            <Percent className='h-5 w-5' />
            Tỷ lệ chiết khấu theo cửa hàng
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className='flex h-80 items-center justify-center'>
            <div className='text-muted-foreground text-center'>
              <p className='font-medium'>Không có dữ liệu</p>
              <p className='text-sm'>
                Chọn khoảng thời gian khác để xem dữ liệu
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className='flex items-center justify-between'>
          <div>
            <CardTitle className='flex items-center gap-2'>
              <Percent className='h-5 w-5' />
              Tỷ lệ chiết khấu theo cửa hàng
            </CardTitle>
            <CardDescription>
              Tất cả cửa hàng được sắp xếp theo tỷ lệ chiết khấu cao nhất
            </CardDescription>
          </div>
          <div className='flex gap-2'>
            <Badge variant='outline' className='flex items-center gap-1'>
              <TrendingUp className='h-3 w-3 text-red-500' />
              Cao nhất: {highestDiscountRate}%
            </Badge>
            <Badge variant='outline' className='flex items-center gap-1'>
              <TrendingDown className='h-3 w-3 text-green-500' />
              Thấp nhất: {lowestDiscountRate}%
            </Badge>
          </div>
        </div>
        <div className='text-muted-foreground flex items-center gap-4 text-sm'>
          <span>Tổng số cửa hàng: {totalStores}</span>
          <span>Tỷ lệ trung bình: {averageDiscountRate}%</span>
        </div>
      </CardHeader>
      <CardContent>
        <div className='h-96 min-h-[400px]'>
          <ResponsiveContainer width='100%' height='100%'>
            <BarChart
              data={chartData}
              margin={{
                top: 20,
                right: 30,
                left: 20,
                bottom: 80,
              }}
            >
              <CartesianGrid strokeDasharray='3 3' className='opacity-30' />
              <XAxis
                dataKey='name'
                angle={-45}
                textAnchor='end'
                height={100}
                fontSize={11}
                interval={0}
                tick={{ fontSize: 11 }}
              />
              <YAxis fontSize={12} tickFormatter={(value) => `${value}%`} />
              <Tooltip content={<CustomTooltip />} />
              <Bar dataKey='discountRate' radius={[4, 4, 0, 0]}>
                {chartData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Bar>
            </BarChart>
          </ResponsiveContainer>
        </div>

        {/* Legend */}
        <div className='mt-4 flex flex-wrap gap-4 text-xs'>
          <div className='flex items-center gap-2'>
            <div className='h-3 w-3 rounded bg-red-500'></div>
            <span>Cao (≥15%)</span>
          </div>
          <div className='flex items-center gap-2'>
            <div className='h-3 w-3 rounded bg-orange-500'></div>
            <span>Trung bình cao (10-15%)</span>
          </div>
          <div className='flex items-center gap-2'>
            <div className='h-3 w-3 rounded bg-yellow-500'></div>
            <span>Trung bình (5-10%)</span>
          </div>
          <div className='flex items-center gap-2'>
            <div className='h-3 w-3 rounded bg-green-500'></div>
            <span>Thấp (&lt;5%)</span>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

export default DiscountRateChart
