import { Badge } from '@/components/ui/badge'
import { useSourcesByStores } from '../../../../hooks/use-sources-by-stores'

interface IndividualSourceStoresProps {
  dateRange: {
    from: Date
    to: Date
  }
  selectedStores?: string[]
  filterType?: 'monthly' | 'daily'
  sourceId: string
  sourceName?: string
  type?: 'least-revenue' | 'most-revenue'
  limit?: number
  className?: string
}

// Format currency in Vietnamese style
function formatCurrency(amount: number): string {
  return new Intl.NumberFormat('vi-VN').format(amount)
}

function getSourceColor(sourceId: string): string {
  if (['10000232', 'GRAPDHI'].includes(sourceId)) {
    return 'bg-green-100 text-green-800'
  }
  if (['BEFOOD', 'BE_TUTIMI', 'BE-DHI'].includes(sourceId)) {
    return 'bg-blue-100 text-blue-800'
  }
  if (['10000169', 'NOWDHI'].includes(sourceId)) {
    return 'bg-orange-100 text-orange-800'
  }
  if (['10000172'].includes(sourceId)) {
    return 'bg-red-100 text-red-800'
  }
  return 'bg-gray-100 text-gray-800'
}

export function IndividualSourceStores({
  dateRange,
  selectedStores = ['all-stores'],
  filterType = 'monthly',
  sourceId,
  sourceName,
  type = 'least-revenue',
  limit = 5,
  className,
}: IndividualSourceStoresProps) {
  const { storesData, isLoading, hasError } = useSourcesByStores({
    dateRange,
    selectedStores,
    filterType,
    autoFetch: true,
  })

  // Filter and calculate revenue for this specific source
  const processedStores = storesData.map((store) => {
    const sourceRevenue = store.allSources
      .filter((source) => source.source_id === sourceId)
      .reduce((sum, source) => sum + (source.revenue_gross || 0), 0)

    return {
      ...store,
      sourceRevenue,
    }
  })

  // Sort stores based on type
  const sortedStores = [...processedStores].sort((a, b) => {
    if (type === 'least-revenue') {
      return a.sourceRevenue - b.sourceRevenue
    } else {
      return b.sourceRevenue - a.sourceRevenue
    }
  })

  const rankingData = sortedStores.slice(0, limit)
  const displayName = sourceName || sourceId

  if (isLoading) {
    return (
      <div className={className}>
        <div className='space-y-3'>
          {[...Array(5)].map((_, index) => (
            <div
              key={index}
              className='flex animate-pulse items-center justify-between'
            >
              <div className='flex items-center space-x-3'>
                <div className='bg-muted h-6 w-6 rounded-full'></div>
                <div className='bg-muted h-4 w-32 rounded'></div>
              </div>
              <div className='bg-muted h-4 w-16 rounded'></div>
            </div>
          ))}
        </div>
      </div>
    )
  }

  if (hasError) {
    return (
      <div className={className}>
        <div className='text-center text-sm text-red-500'>
          Lỗi tải dữ liệu {displayName}
        </div>
      </div>
    )
  }

  if (rankingData.length === 0) {
    return (
      <div className={className}>
        <div className='text-muted-foreground text-center text-sm'>
          Không có dữ liệu {displayName}
        </div>
      </div>
    )
  }

  return (
    <div className={className}>
      <div className='space-y-3'>
        {rankingData.map((store, index) => (
          <div
            key={store.storeId}
            className='flex items-center justify-between'
          >
            <div className='flex min-w-0 flex-1 items-center space-x-3'>
              <Badge
                variant={
                  index === 0
                    ? 'destructive'
                    : index === 1
                      ? 'secondary'
                      : 'outline'
                }
                className='flex h-6 w-6 flex-shrink-0 items-center justify-center rounded-full p-0 text-xs font-medium'
              >
                {index + 1}
              </Badge>
              <div className='min-w-0 flex-1'>
                <div
                  className='cursor-help truncate text-sm font-medium'
                  title={store.storeName}
                >
                  {store.storeName}
                </div>
              </div>
            </div>
            <div className='flex flex-shrink-0 items-center justify-end'>
              <Badge
                variant='outline'
                className={`text-xs font-bold whitespace-nowrap ${
                  store.sourceRevenue === 0
                    ? 'border-gray-200 bg-gray-50 text-gray-400'
                    : getSourceColor(sourceId)
                }`}
              >
                {store.sourceRevenue === 0
                  ? '0 ₫'
                  : formatCurrency(store.sourceRevenue)}
              </Badge>
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}

// Specific source components for Grab
export function GrabStores(
  props: Omit<IndividualSourceStoresProps, 'sourceId' | 'sourceName'>
) {
  return (
    <IndividualSourceStores
      {...props}
      sourceId='10000232'
      sourceName='Grab (10000232)'
    />
  )
}

export function GrabDHIStores(
  props: Omit<IndividualSourceStoresProps, 'sourceId' | 'sourceName'>
) {
  return (
    <IndividualSourceStores
      {...props}
      sourceId='GRAPDHI'
      sourceName='Grab (GRAPDHI)'
    />
  )
}

// Specific source components for Be
export function BeFOODStores(
  props: Omit<IndividualSourceStoresProps, 'sourceId' | 'sourceName'>
) {
  return (
    <IndividualSourceStores
      {...props}
      sourceId='BEFOOD'
      sourceName='Be (BEFOOD)'
    />
  )
}

export function BeTUTIMIStores(
  props: Omit<IndividualSourceStoresProps, 'sourceId' | 'sourceName'>
) {
  return (
    <IndividualSourceStores
      {...props}
      sourceId='BE_TUTIMI'
      sourceName='Be (BE_TUTIMI)'
    />
  )
}

export function BeDHIStores(
  props: Omit<IndividualSourceStoresProps, 'sourceId' | 'sourceName'>
) {
  return (
    <IndividualSourceStores
      {...props}
      sourceId='BE-DHI'
      sourceName='Be (BE-DHI)'
    />
  )
}

// Specific source components for Shopee
export function ShopeeStores(
  props: Omit<IndividualSourceStoresProps, 'sourceId' | 'sourceName'>
) {
  return (
    <IndividualSourceStores
      {...props}
      sourceId='10000169'
      sourceName='Shopee (10000169)'
    />
  )
}

export function ShopeeNOWDHIStores(
  props: Omit<IndividualSourceStoresProps, 'sourceId' | 'sourceName'>
) {
  return (
    <IndividualSourceStores
      {...props}
      sourceId='NOWDHI'
      sourceName='Shopee (NOWDHI)'
    />
  )
}

// Specific source components for Tai Cho
export function TaiChoStores(
  props: Omit<IndividualSourceStoresProps, 'sourceId' | 'sourceName'>
) {
  return (
    <IndividualSourceStores
      {...props}
      sourceId='10000172'
      sourceName='Tại Chỗ (10000172)'
    />
  )
}

export default IndividualSourceStores
