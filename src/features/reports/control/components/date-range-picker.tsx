import { format, startOfMonth, endOfMonth } from 'date-fns'
import { vi } from 'date-fns/locale'
import { CalendarIcon } from 'lucide-react'
import { getLast3MonthsRange, getDaysAgoDateRange } from '@/lib/date-utils'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { Calendar } from '@/components/ui/calendar'
import { Label } from '@/components/ui/label'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'

interface DateRangePickerProps {
  dateRange: {
    from: Date
    to: Date
  }
  onDateRangeChange: (dateRange: { from: Date; to: Date }) => void
  filterType?: 'monthly' | 'daily'
  className?: string
}

export function DateRangePicker({
  dateRange,
  onDateRangeChange,
  filterType = 'daily',
  className,
}: DateRangePickerProps) {
  const setDateRange = (
    updater: (prev: { from: Date; to: Date }) => { from: Date; to: Date }
  ) => {
    onDateRangeChange(updater(dateRange))
  }

  // Handle date selection based on filter type
  const handleDateSelect = (date: Date | undefined, type: 'from' | 'to') => {
    if (!date) return

    if (filterType === 'monthly') {
      // For monthly filter, set to start/end of selected month
      if (type === 'from') {
        const monthStart = startOfMonth(date)
        setDateRange((prev) => ({
          from: monthStart,
          to: prev.to, // Keep existing 'to' date
        }))
      } else {
        const monthEnd = endOfMonth(date)
        setDateRange((prev) => ({
          from: prev.from, // Keep existing 'from' date
          to: monthEnd,
        }))
      }
    } else {
      // For daily filter, use exact date
      setDateRange((prev) => ({ ...prev, [type]: date }))
    }
  }

  return (
    <div className={cn('space-y-4', className)}>
      {/* Date Pickers Row */}
      <div className='flex flex-col gap-3 sm:flex-row sm:gap-4'>
        <div className='space-y-2'>
          <Label className='text-sm font-medium'>
            {filterType === 'monthly' ? 'Từ tháng' : 'Từ ngày'}
          </Label>
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant={'outline'}
                className={cn(
                  'w-fit justify-start text-left font-normal',
                  !dateRange.from && 'text-muted-foreground'
                )}
              >
                <CalendarIcon className='mr-2 h-4 w-4 flex-shrink-0' />
                <span className='truncate'>
                  {dateRange.from
                    ? filterType === 'monthly'
                      ? format(dateRange.from, 'MM/yyyy', { locale: vi })
                      : format(dateRange.from, 'dd/MM/yyyy', { locale: vi })
                    : filterType === 'monthly'
                      ? 'Chọn tháng bắt đầu'
                      : 'Chọn ngày bắt đầu'}
                </span>
              </Button>
            </PopoverTrigger>
            <PopoverContent className='w-auto p-0' align='start'>
              <Calendar
                mode='single'
                selected={dateRange.from}
                onSelect={(date) => handleDateSelect(date, 'from')}
                initialFocus
                locale={vi}
              />
            </PopoverContent>
          </Popover>
        </div>

        <div className='space-y-2'>
          <Label className='text-sm font-medium'>
            {filterType === 'monthly' ? 'Đến tháng' : 'Đến ngày'}
          </Label>
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant={'outline'}
                className={cn(
                  'w-fit justify-start text-left font-normal',
                  !dateRange.to && 'text-muted-foreground'
                )}
              >
                <CalendarIcon className='mr-2 h-4 w-4 flex-shrink-0' />
                <span className='truncate'>
                  {dateRange.to
                    ? filterType === 'monthly'
                      ? format(dateRange.to, 'MM/yyyy', { locale: vi })
                      : format(dateRange.to, 'dd/MM/yyyy', { locale: vi })
                    : filterType === 'monthly'
                      ? 'Chọn tháng kết thúc'
                      : 'Chọn ngày kết thúc'}
                </span>
              </Button>
            </PopoverTrigger>
            <PopoverContent className='w-auto p-0' align='start'>
              <Calendar
                mode='single'
                selected={dateRange.to}
                onSelect={(date) => handleDateSelect(date, 'to')}
                initialFocus
                locale={vi}
                disabled={(date) => {
                  if (filterType === 'monthly') {
                    // For monthly, disable months before the selected 'from' month
                    return startOfMonth(date) < startOfMonth(dateRange.from)
                  } else {
                    // For daily, disable dates before the selected 'from' date
                    return date < dateRange.from
                  }
                }}
              />
            </PopoverContent>
          </Popover>
        </div>
      </div>

      {/* Quick Select Row */}
      <div className='space-y-2'>
        <Label className='text-sm font-medium'>Chọn nhanh</Label>
        <div className='grid grid-cols-2 gap-2 sm:flex sm:flex-wrap'>
          {filterType === 'daily' ? (
            // Daily filter presets
            <>
              <Button
                variant='outline'
                size='sm'
                className='flex-1 sm:flex-none'
                onClick={() => onDateRangeChange(getDaysAgoDateRange(7))}
              >
                7 ngày
              </Button>
              <Button
                variant='outline'
                size='sm'
                className='flex-1 sm:flex-none'
                onClick={() => onDateRangeChange(getDaysAgoDateRange(28))}
              >
                28 ngày
              </Button>
              <Button
                variant='outline'
                size='sm'
                className='col-span-2 sm:col-span-1 sm:flex-none'
                onClick={() => {
                  const today = new Date()
                  const thisMonth = new Date(
                    today.getFullYear(),
                    today.getMonth(),
                    1
                  )
                  onDateRangeChange({ from: thisMonth, to: today })
                }}
              >
                Tháng này
              </Button>
            </>
          ) : (
            // Monthly filter presets
            <>
              <Button
                variant='outline'
                size='sm'
                className='flex-1 sm:flex-none'
                onClick={() => onDateRangeChange(getLast3MonthsRange())}
              >
                3 tháng gần đây
              </Button>
              <Button
                variant='outline'
                size='sm'
                className='col-span-2 sm:col-span-1 sm:flex-none'
                onClick={() => {
                  const today = new Date()
                  const currentYear = today.getFullYear()
                  const lastHalfYear = new Date(
                    currentYear,
                    today.getMonth() - 6,
                    1
                  )
                  onDateRangeChange({ from: lastHalfYear, to: today })
                }}
              >
                6 tháng gần đây
              </Button>
            </>
          )}
        </div>
      </div>
    </div>
  )
}
