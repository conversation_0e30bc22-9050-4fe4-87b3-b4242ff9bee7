import { useMemo } from 'react'

import { SourceData } from '@/lib/sale-sources-api'
import { cn } from '@/lib/utils'

import { useSources } from '@/hooks/use-sources'

import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Skeleton } from '@/components/ui/skeleton'

// Types
interface SourcesListProps {
  dateRange: {
    from: Date
    to: Date
  }
  selectedStores?: string[]
  filterType?: 'monthly' | 'daily'
  platformFilter?: 'all' | 'grab' | 'be' | 'shopee' | 'tai-cho' | 'mang-ve'
  limit?: number
  className?: string
  showRevenue?: boolean
  showBills?: boolean
  showDiscountRate?: boolean
}

// Use SourceData type from API
type SourceItem = SourceData

// Platform mapping for filtering - using exact source_id
const PLATFORM_KEYWORDS = {
  grab: ['10000232', 'GRAPDHI'],
  be: ['BEFOOD', 'BE_TUTIMI', 'BE-DHI'],
  shopee: ['10000169', 'NOWDHI'],
  'tai-cho': ['10000172'],
  'mang-ve': ['10000171']
}

// Hook for sources list data using existing useSources
function useSourcesList({
  dateRange,
  selectedStores = ['all-stores'],
  filterType = 'monthly',
  platformFilter = 'all',
  autoFetch = true
}: {
  dateRange: { from: Date; to: Date }
  selectedStores?: string[]
  filterType?: 'monthly' | 'daily'
  platformFilter?: string
  autoFetch?: boolean
}) {
  // Use existing useSources hook
  const {
    sources: rawSources,
    isLoading,
    error,
    refetch
  } = useSources({
    dateRange,
    selectedStores,
    filterType,
    autoFetch
  })

  // Filter sources by platform
  const filteredSources = useMemo(() => {
    if (!rawSources || rawSources.length === 0) return []

    let sources = rawSources as SourceItem[]

    // Apply platform filter using exact source_id match
    if (platformFilter !== 'all') {
      const keywords = PLATFORM_KEYWORDS[platformFilter as keyof typeof PLATFORM_KEYWORDS]
      if (keywords) {
        sources = sources.filter(source => keywords.includes(source.source_id))
      }
    }

    return sources
  }, [rawSources, platformFilter])

  // Calculate totals
  const totals = useMemo(() => {
    const totalRevenue = filteredSources.reduce((sum, source) => sum + source.revenue_gross, 0)
    const totalBills = filteredSources.reduce((sum, source) => sum + source.total_bill, 0)
    const totalDiscount = filteredSources.reduce((sum, source) => sum + source.discount_amount, 0)

    return {
      totalRevenue,
      totalBills,
      totalDiscount,
      averageDiscountRate: totalRevenue > 0 ? (totalDiscount / totalRevenue) * 100 : 0
    }
  }, [filteredSources])

  return {
    sources: filteredSources,
    totals,
    isLoading,
    error,
    refetch
  }
}

// Format currency in Vietnamese style
function formatCurrency(amount: number): string {
  return new Intl.NumberFormat('vi-VN').format(amount)
}

// Format percentage
function formatPercentage(value: number): string {
  return `${value.toFixed(1)}%`
}

// Get platform badge variant
function getPlatformBadgeVariant(
  sourceName: string
): 'default' | 'secondary' | 'destructive' | 'outline' {
  const name = sourceName.toLowerCase()

  if (name.includes('grab')) return 'default'
  if (name.includes('shopee') || name.includes('now')) return 'secondary'
  if (name.includes('be-') || name.includes('be ')) return 'outline'
  if (name.includes('tại chỗ') || name.includes('tai cho')) return 'destructive'

  return 'outline'
}

export function SourcesList({
  dateRange,
  selectedStores = ['all-stores'],
  filterType = 'monthly',
  platformFilter = 'all',
  limit = 10,
  className,
  showRevenue = true,
  showBills = true,
  showDiscountRate = false
}: SourcesListProps) {
  const { sources, totals, isLoading, error } = useSourcesList({
    dateRange,
    selectedStores,
    filterType,
    platformFilter,
    autoFetch: true
  })

  // Limit the number of sources displayed
  const displaySources = useMemo(() => {
    return sources.slice(0, limit)
  }, [sources, limit])

  if (error) {
    return (
      <Card className={cn('w-full', className)}>
        <CardContent className='p-6'>
          <div className='text-center text-red-500'>
            Lỗi khi tải dữ liệu sources: {typeof error === 'string' ? error : 'Có lỗi xảy ra'}
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={cn('w-full', className)}>
      <CardHeader className='pb-3'>
        <CardTitle className='text-lg font-semibold'>
          Danh Sách Sources
          {platformFilter !== 'all' && (
            <Badge variant='outline' className='ml-2 text-xs'>
              {platformFilter.toUpperCase()}
            </Badge>
          )}
        </CardTitle>
        {!isLoading && (
          <div className='text-muted-foreground text-sm'>
            Tổng: {sources.length} sources • {formatCurrency(totals.totalRevenue)} VNĐ
          </div>
        )}
      </CardHeader>
      <CardContent className='p-0'>
        {isLoading ? (
          <div className='space-y-3 p-6'>
            {Array.from({ length: 5 }).map((_, index) => (
              <div key={index} className='flex items-center justify-between'>
                <div className='space-y-2'>
                  <Skeleton className='h-4 w-32' />
                  <Skeleton className='h-3 w-24' />
                </div>
                <div className='space-y-2 text-right'>
                  <Skeleton className='h-4 w-20' />
                  <Skeleton className='h-3 w-16' />
                </div>
              </div>
            ))}
          </div>
        ) : displaySources.length === 0 ? (
          <div className='text-muted-foreground p-6 text-center'>Không có dữ liệu sources</div>
        ) : (
          <div className='space-y-0'>
            {displaySources.map((source, index) => {
              const discountRate =
                source.revenue_gross > 0 ? (source.discount_amount / source.revenue_gross) * 100 : 0

              return (
                <div
                  key={source.source_id}
                  className={cn(
                    'hover:bg-muted/50 flex items-center justify-between p-4 transition-colors',
                    index !== displaySources.length - 1 && 'border-b'
                  )}
                >
                  <div className='min-w-0 flex-1'>
                    <div className='mb-1 flex items-center gap-2'>
                      <h4 className='truncate text-sm font-medium'>{source.source_name}</h4>
                      <Badge
                        variant={getPlatformBadgeVariant(source.source_name)}
                        className='px-2 py-0.5 text-xs'
                      >
                        {source.source_id}
                      </Badge>
                    </div>
                    <div className='text-muted-foreground flex items-center gap-4 text-xs'>
                      {showBills && <span>{formatCurrency(source.total_bill)} đơn</span>}
                      {showDiscountRate && <span>Giảm giá: {formatPercentage(discountRate)}</span>}
                    </div>
                  </div>

                  <div className='text-right'>
                    {showRevenue && (
                      <div className='text-sm font-semibold'>
                        {formatCurrency(source.revenue_gross)} VNĐ
                      </div>
                    )}
                    <div className='text-muted-foreground text-xs'>
                      Net: {formatCurrency(source.revenue_net)} VNĐ
                    </div>
                  </div>
                </div>
              )
            })}
          </div>
        )}
      </CardContent>
    </Card>
  )
}

export default SourcesList
