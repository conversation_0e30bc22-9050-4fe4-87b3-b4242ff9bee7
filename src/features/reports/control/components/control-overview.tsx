import { useState, useMemo } from 'react'
import {
  getDefaultMonthlyDateRange,
  getDefaultDailyDateRange,
} from '@/lib/date-utils'
import { ReportsContext } from '@/hooks/use-reports-context'
import { DiscountRateChart } from './discount-rate-chart'
import { FiltersPanel } from './filters-panel'
import { SourcesSection } from './sources-section'
import { VoucherDiscountChart } from './voucher-discount-chart'

export function ControlOverview() {
  const [filterType, setFilterType] = useState<'monthly' | 'daily'>('monthly')

  const [dateRange, setDateRange] = useState<{
    from: Date
    to: Date
  }>(getDefaultMonthlyDateRange())
  const [selectedStores, setSelectedStores] = useState<string[]>(['all-stores'])
  const [selectedSources, setSelectedSources] = useState<string[]>([
    'all-sources',
  ])

  // Note: useSources removed as we're focusing on discount rate chart
  // Can be added back when needed for other charts

  const handleFilterTypeChange = (newFilterType: 'monthly' | 'daily') => {
    setFilterType(newFilterType)

    if (newFilterType === 'monthly') {
      setDateRange(getDefaultMonthlyDateRange())
    } else if (newFilterType === 'daily') {
      setDateRange(getDefaultDailyDateRange())
    }
  }

  const contextValue = useMemo(
    () => ({
      dateRange,
      filterType,
      selectedStores,
      selectedSources,
    }),
    [dateRange, filterType, selectedStores, selectedSources]
  )

  return (
    <ReportsContext.Provider value={contextValue}>
      <div className='space-y-6'>
        {/* Filters Panel */}
        <FiltersPanel
          dateRange={dateRange}
          onDateRangeChange={setDateRange}
          selectedStores={selectedStores}
          onStoreChange={setSelectedStores}
          selectedSources={selectedSources}
          onSourceChange={setSelectedSources}
          filterType={filterType}
          onFilterTypeChange={handleFilterTypeChange}
        />

        {/* Charts Section */}
        <div className='grid gap-6'>
          {/* Discount Rate Chart */}
          <div className='col-span-full'>
            <DiscountRateChart
              dateRange={dateRange}
              selectedStores={selectedStores}
              className='w-full'
            />
          </div>

          {/* Voucher Discount Chart */}
          <div className='col-span-full'>
            <VoucherDiscountChart
              dateRange={dateRange}
              selectedStores={selectedStores}
              className='w-full'
            />
          </div>

          {/* Sources Section */}
          <div className='col-span-full'>
            <SourcesSection
              dateRange={dateRange}
              selectedStores={selectedStores}
              filterType={filterType}
            />
          </div>
        </div>
      </div>
    </ReportsContext.Provider>
  )
}
