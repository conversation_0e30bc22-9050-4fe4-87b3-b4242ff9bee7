import { cn } from '@/lib/utils'
import { SourcesList } from './sources-list'

// Common props interface
interface PlatformSourcesProps {
  dateRange: {
    from: Date
    to: Date
  }
  selectedStores?: string[]
  filterType?: 'monthly' | 'daily'
  limit?: number
  className?: string
  showRevenue?: boolean
  showBills?: boolean
  showDiscountRate?: boolean
}

// Grab Sources Component
export function GrabSources({
  dateRange,
  selectedStores = ['all-stores'],
  filterType = 'monthly',
  limit = 10,
  className,
  showRevenue = true,
  showBills = true,
  showDiscountRate = false,
}: PlatformSourcesProps) {
  return (
    <SourcesList
      dateRange={dateRange}
      selectedStores={selectedStores}
      filterType={filterType}
      platformFilter='grab'
      limit={limit}
      className={cn('border-green-200 bg-green-50/50', className)}
      showRevenue={showRevenue}
      showBills={showBills}
      showDiscountRate={showDiscountRate}
    />
  )
}

// BE Sources Component
export function BeSources({
  dateRange,
  selectedStores = ['all-stores'],
  filterType = 'monthly',
  limit = 10,
  className,
  showRevenue = true,
  showBills = true,
  showDiscountRate = false,
}: PlatformSourcesProps) {
  return (
    <SourcesList
      dateRange={dateRange}
      selectedStores={selectedStores}
      filterType={filterType}
      platformFilter='be'
      limit={limit}
      className={cn('border-blue-200 bg-blue-50/50', className)}
      showRevenue={showRevenue}
      showBills={showBills}
      showDiscountRate={showDiscountRate}
    />
  )
}

// Shopee Sources Component
export function ShopeeSources({
  dateRange,
  selectedStores = ['all-stores'],
  filterType = 'monthly',
  limit = 10,
  className,
  showRevenue = true,
  showBills = true,
  showDiscountRate = false,
}: PlatformSourcesProps) {
  return (
    <SourcesList
      dateRange={dateRange}
      selectedStores={selectedStores}
      filterType={filterType}
      platformFilter='shopee'
      limit={limit}
      className={cn('border-orange-200 bg-orange-50/50', className)}
      showRevenue={showRevenue}
      showBills={showBills}
      showDiscountRate={showDiscountRate}
    />
  )
}

// Tại Chỗ Sources Component
export function TaiChoSources({
  dateRange,
  selectedStores = ['all-stores'],
  filterType = 'monthly',
  limit = 10,
  className,
  showRevenue = true,
  showBills = true,
  showDiscountRate = false,
}: PlatformSourcesProps) {
  return (
    <SourcesList
      dateRange={dateRange}
      selectedStores={selectedStores}
      filterType={filterType}
      platformFilter='tai-cho'
      limit={limit}
      className={cn('border-red-200 bg-red-50/50', className)}
      showRevenue={showRevenue}
      showBills={showBills}
      showDiscountRate={showDiscountRate}
    />
  )
}

// Mang Về Sources Component
export function MangVeSources({
  dateRange,
  selectedStores = ['all-stores'],
  filterType = 'monthly',
  limit = 10,
  className,
  showRevenue = true,
  showBills = true,
  showDiscountRate = false,
}: PlatformSourcesProps) {
  return (
    <SourcesList
      dateRange={dateRange}
      selectedStores={selectedStores}
      filterType={filterType}
      platformFilter='mang-ve'
      limit={limit}
      className={cn('border-purple-200 bg-purple-50/50', className)}
      showRevenue={showRevenue}
      showBills={showBills}
      showDiscountRate={showDiscountRate}
    />
  )
}

// All Sources Component (no filter)
export function AllSources({
  dateRange,
  selectedStores = ['all-stores'],
  filterType = 'monthly',
  limit = 10,
  className,
  showRevenue = true,
  showBills = true,
  showDiscountRate = false,
}: PlatformSourcesProps) {
  return (
    <SourcesList
      dateRange={dateRange}
      selectedStores={selectedStores}
      filterType={filterType}
      platformFilter='all'
      limit={limit}
      className={className}
      showRevenue={showRevenue}
      showBills={showBills}
      showDiscountRate={showDiscountRate}
    />
  )
}

// Combined Platform Sources Grid
interface PlatformSourcesGridProps {
  dateRange: {
    from: Date
    to: Date
  }
  selectedStores?: string[]
  filterType?: 'monthly' | 'daily'
  limit?: number
  className?: string
  showRevenue?: boolean
  showBills?: boolean
  showDiscountRate?: boolean
  platforms?: ('grab' | 'be' | 'shopee' | 'tai-cho' | 'mang-ve')[]
}

export function PlatformSourcesGrid({
  dateRange,
  selectedStores = ['all-stores'],
  filterType = 'monthly',
  limit = 5,
  className,
  showRevenue = true,
  showBills = true,
  showDiscountRate = false,
  platforms = ['grab', 'be', 'shopee', 'tai-cho'],
}: PlatformSourcesGridProps) {
  const platformComponents = {
    grab: GrabSources,
    be: BeSources,
    shopee: ShopeeSources,
    'tai-cho': TaiChoSources,
    'mang-ve': MangVeSources,
  }

  return (
    <div className={cn('grid gap-6 md:grid-cols-2 lg:grid-cols-3', className)}>
      {platforms.map((platform) => {
        const Component = platformComponents[platform]
        return (
          <Component
            key={platform}
            dateRange={dateRange}
            selectedStores={selectedStores}
            filterType={filterType}
            limit={limit}
            showRevenue={showRevenue}
            showBills={showBills}
            showDiscountRate={showDiscountRate}
          />
        )
      })}
    </div>
  )
}

// Export all components
export { SourcesList }

export default {
  SourcesList,
  GrabSources,
  BeSources,
  ShopeeSources,
  TaiChoSources,
  MangVeSources,
  AllSources,
  PlatformSourcesGrid,
}
