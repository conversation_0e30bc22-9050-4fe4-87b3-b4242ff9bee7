import { useMemo } from 'react'
import { TrendingUp, TrendingDown, Ticket, DollarSign } from 'lucide-react'
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Cell,
} from 'recharts'
import { useSalesVoucherData } from '@/hooks/use-sales-voucher-data'
import { Badge } from '@/components/ui/badge'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'

interface VoucherDiscountChartProps {
  dateRange: {
    from: Date
    to: Date
  }
  selectedStores?: string[]
  className?: string
}

const getBarColor = (voucherPercentage: number) => {
  if (voucherPercentage >= 15) return '#dc2626' // Red for very high voucher percentage (15%+)
  if (voucherPercentage >= 10) return '#ea580c' // Orange for high (10-15%)
  if (voucherPercentage >= 5) return '#ca8a04' // Yellow for medium-high (5-10%)
  if (voucherPercentage >= 2) return '#16a34a' // Green for medium (2-5%)
  return '#0ea5e9' // Blue for low voucher percentage (<2%)
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const CustomTooltip = ({ active, payload, label }: any) => {
  if (active && payload && payload.length) {
    const data = payload[0].payload
    return (
      <div className='rounded-lg border border-gray-200 bg-white p-3 shadow-lg'>
        <p className='mb-2 font-medium text-gray-900'>{label}</p>
        <div className='space-y-1 text-sm'>
          <p className='flex items-center gap-2'>
            <Ticket className='h-3 w-3 text-blue-500' />
            <span>
              Tỷ lệ voucher: <strong>{data.voucherPercentage}%</strong>
            </span>
          </p>
          <p className='text-gray-600'>
            Doanh thu cửa hàng:{' '}
            {new Intl.NumberFormat('vi-VN').format(data.revenueGross)} VNĐ
          </p>
          <p className='text-gray-600'>
            Voucher gốc:{' '}
            {new Intl.NumberFormat('vi-VN').format(data.totalPrice)} VNĐ
          </p>
          <p className='text-gray-600'>
            Số giao dịch:{' '}
            {new Intl.NumberFormat('vi-VN').format(data.totalTransactions)}
          </p>
        </div>
      </div>
    )
  }
  return null
}

export function VoucherDiscountChart({
  dateRange,
  selectedStores,
  className,
}: VoucherDiscountChartProps) {
  const {
    data: voucherData,
    totalPrice,
    isLoading,
    error,
  } = useSalesVoucherData({
    dateRange,
    selectedStores,
    voucherCodes: ['FB100SK', 'FBGTVM', 'FBUPXL5'], // Filter by specific voucher codes
    autoFetch: true,
  })

  const chartData = useMemo(() => {
    if (!voucherData || !Array.isArray(voucherData)) {
      return []
    }

    return voucherData
      .filter(
        (store) =>
          store.totalPrice > 0 && store.revenueGross && store.revenueGross > 0
      ) // Only show stores with voucher usage and revenue data
      .map((store) => {
        const voucherPercentage = store.voucherPercentage || 0
        const storeName = store.storeName || 'Unknown Store'

        return {
          name:
            storeName.length > 15
              ? `${storeName.substring(0, 15)}...`
              : storeName,
          fullName: storeName,
          voucherPercentage: Number(voucherPercentage.toFixed(2)),
          totalPrice: store.totalPrice, // amount_origin total
          revenueGross: store.revenueGross, // total store revenue
          totalTransactions: store.totalTransactions,
          color: getBarColor(voucherPercentage),
        }
      })
      .sort((a, b) => b.voucherPercentage - a.voucherPercentage) // Sort by voucher percentage descending
  }, [voucherData])

  // Calculate statistics
  const averageVoucherPercentage = useMemo(() => {
    if (chartData.length === 0) return 0
    const totalRate = chartData.reduce(
      (sum, store) => sum + store.voucherPercentage,
      0
    )
    return Number((totalRate / chartData.length).toFixed(2))
  }, [chartData])

  const highestVoucherPercentage = useMemo(() => {
    return chartData.length > 0 ? chartData[0].voucherPercentage : 0
  }, [chartData])

  const lowestVoucherPercentage = useMemo(() => {
    return chartData.length > 0
      ? chartData[chartData.length - 1].voucherPercentage
      : 0
  }, [chartData])

  const totalDiscountAmount = useMemo(() => {
    return chartData.reduce((sum, store) => sum + store.totalPrice, 0)
  }, [chartData])

  if (isLoading || voucherData?.length === 0) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className='flex items-center gap-2'>
            <Ticket className='h-5 w-5' />
            Tỷ lệ Voucher Thành Viên theo Cửa hàng
          </CardTitle>
          <CardDescription>
            Biểu đồ tỷ lệ giảm giá voucher theo cửa hàng
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className='flex h-80 items-center justify-center'>
            <div className='text-center'>
              <div className='border-primary mx-auto mb-2 h-8 w-8 animate-spin rounded-full border-b-2'></div>
              <p className='text-muted-foreground text-sm'>
                Đang tải dữ liệu...
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className='flex items-center gap-2'>
            <Ticket className='h-5 w-5' />
            Tỷ lệ Voucher Thành Viên
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className='flex h-80 items-center justify-center'>
            <div className='text-center text-red-500'>
              <p className='font-medium'>Lỗi tải dữ liệu</p>
              <p className='text-sm'>{error}</p>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (chartData.length === 0) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className='flex items-center gap-2'>
            <Ticket className='h-5 w-5' />
            Tỷ lệ Voucher Thành Viên
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className='flex h-80 items-center justify-center'>
            <div className='text-muted-foreground text-center'>
              <p className='font-medium'>Không có dữ liệu voucher</p>
              <p className='text-sm'>
                Không tìm thấy giao dịch nào với voucher codes: FB100SK, FBGTVM,
                FBUPXL5
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className='flex items-center justify-between'>
          <div>
            <CardTitle className='flex items-center gap-2'>
              <Ticket className='h-5 w-5' />
              Tỷ lệ Voucher Thành Viên
            </CardTitle>
            <CardDescription>
              Voucher Codes: FB100SK, FBGTVM, FBUPXL5 - Sắp xếp theo tỷ lệ cao
              nhất
            </CardDescription>
          </div>
          <div className='flex gap-2'>
            <Badge variant='outline' className='flex items-center gap-1'>
              <TrendingUp className='h-3 w-3 text-red-500' />
              Cao nhất: {highestVoucherPercentage}%
            </Badge>
            <Badge variant='outline' className='flex items-center gap-1'>
              <TrendingDown className='h-3 w-3 text-green-500' />
              Thấp nhất: {lowestVoucherPercentage}%
            </Badge>
          </div>
        </div>
        <div className='grid grid-cols-2 gap-4 text-sm md:grid-cols-4'>
          <div className='flex items-center gap-2'>
            <DollarSign className='h-4 w-4 text-blue-500' />
            <div>
              <p className='text-muted-foreground'>Tổng voucher</p>
              <p className='font-medium'>
                {new Intl.NumberFormat('vi-VN').format(totalPrice)} VNĐ
              </p>
            </div>
          </div>
          <div>
            <p className='text-muted-foreground'>Tổng giảm giá</p>
            <p className='font-medium'>
              {new Intl.NumberFormat('vi-VN').format(totalDiscountAmount)} VNĐ
            </p>
          </div>
          <div>
            <p className='text-muted-foreground'>Tỷ lệ trung bình</p>
            <p className='font-medium'>{averageVoucherPercentage}%</p>
          </div>
          <div>
            <p className='text-muted-foreground'>Số cửa hàng</p>
            <p className='font-medium'>{chartData.length}</p>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className='h-96 min-h-[400px]'>
          <ResponsiveContainer width='100%' height='100%'>
            <BarChart
              data={chartData}
              margin={{
                top: 20,
                right: 30,
                left: 20,
                bottom: 80,
              }}
            >
              <CartesianGrid strokeDasharray='3 3' className='opacity-30' />
              <XAxis
                dataKey='name'
                angle={-45}
                textAnchor='end'
                height={100}
                fontSize={11}
                interval={0}
                tick={{ fontSize: 11 }}
              />
              <YAxis fontSize={12} tickFormatter={(value) => `${value}%`} />
              <Tooltip content={<CustomTooltip />} />
              <Bar dataKey='voucherPercentage' radius={[4, 4, 0, 0]}>
                {chartData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Bar>
            </BarChart>
          </ResponsiveContainer>
        </div>

        {/* Legend */}
        <div className='mt-4 flex flex-wrap gap-4 text-xs'>
          <div className='flex items-center gap-2'>
            <div className='h-3 w-3 rounded bg-red-600'></div>
            <span>Rất cao (≥15%)</span>
          </div>
          <div className='flex items-center gap-2'>
            <div className='h-3 w-3 rounded bg-orange-600'></div>
            <span>Cao (10-15%)</span>
          </div>
          <div className='flex items-center gap-2'>
            <div className='h-3 w-3 rounded bg-yellow-600'></div>
            <span>Trung bình cao (5-10%)</span>
          </div>
          <div className='flex items-center gap-2'>
            <div className='h-3 w-3 rounded bg-green-600'></div>
            <span>Trung bình (2-5%)</span>
          </div>
          <div className='flex items-center gap-2'>
            <div className='h-3 w-3 rounded bg-blue-500'></div>
            <span>Thấp (&lt;2%)</span>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

export default VoucherDiscountChart
