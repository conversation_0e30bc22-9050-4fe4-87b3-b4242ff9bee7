import { useMemo } from 'react'
import { usePosStores } from '@/stores/posStore'
import { useAllStores } from '@/hooks/use-all-stores'
import { Badge } from '@/components/ui/badge'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import {
  GrabStores,
  GrabDHIStores,
  BeFOODStores,
  BeTUTIMIStores,
  BeDHIStores,
  ShopeeStores,
  ShopeeNOWDHIStores,
  TaiChoStores,
} from './individual-source-stores'

interface SourcesSectionProps {
  dateRange: {
    from: Date
    to: Date
  }
  selectedStores?: string[]
  filterType?: 'monthly' | 'daily'
}

export function SourcesSection({
  dateRange,
  selectedStores = ['all-stores'],
  filterType = 'monthly',
}: SourcesSectionProps) {
  const { currentBrandStores, selectedBrand } = usePosStores()
  const { stores: allStoresFromAPI } = useAllStores()

  // Determine which sources to show based on brand_id
  const brandId = selectedBrand?.id
  const isLimitedBrand = brandId === '5ed8968a-e4ed-4a04-870d-b53b7758fdc7'

  const availableStores =
    allStoresFromAPI.length > 0 ? allStoresFromAPI : currentBrandStores

  const selectedStoreName = useMemo(() => {
    if (selectedStores.includes('all-stores')) {
      return 'Tất cả cửa hàng'
    }

    const selectedStoreId = selectedStores[0]
    const store = availableStores.find((s) => s.id === selectedStoreId)
    return store?.store_name || `Store ${selectedStoreId}`
  }, [selectedStores, availableStores])

  return (
    <div className='space-y-4 sm:space-y-6'>
      {/* Header with store name */}
      <div className='flex items-center justify-between'>
        <div>
          <h2 className='text-lg font-semibold'>Top Cửa Hàng Theo Source</h2>
          <div className='mt-1 flex items-center gap-2'>
            <span className='text-muted-foreground text-sm'>Cửa hàng:</span>
            <Badge variant='outline' className='text-xs'>
              {selectedStoreName}
            </Badge>
          </div>
        </div>
      </div>

      <div className='grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4'>
        {/* Grab Sources */}
        <Card className='h-full'>
          <CardHeader className='pb-3'>
            <CardTitle className='text-sm font-medium text-green-700'>
              GRABFOOD
            </CardTitle>
            <CardDescription className='text-xs'>
              Doanh số thấp nhất
            </CardDescription>
          </CardHeader>
          <CardContent className='pt-0'>
            <GrabStores
              dateRange={dateRange}
              selectedStores={selectedStores}
              filterType={filterType}
              type='least-revenue'
              limit={5}
            />
          </CardContent>
        </Card>

        {!isLimitedBrand && (
          <Card className='h-full'>
            <CardHeader className='pb-3'>
              <CardTitle className='text-sm font-medium text-green-700'>
                GRAB - DHI
              </CardTitle>
              <CardDescription className='text-xs'>
                Doanh số thấp nhất
              </CardDescription>
            </CardHeader>
            <CardContent className='pt-0'>
              <GrabDHIStores
                dateRange={dateRange}
                selectedStores={selectedStores}
                filterType={filterType}
                type='least-revenue'
                limit={5}
              />
            </CardContent>
          </Card>
        )}

        {/* Be Sources */}
        {isLimitedBrand && (
          <Card className='h-full'>
            <CardHeader className='pb-3'>
              <CardTitle className='text-sm font-medium text-blue-700'>
                BEFOOD
              </CardTitle>
              <CardDescription className='text-xs'>
                Doanh số thấp nhất
              </CardDescription>
            </CardHeader>
            <CardContent className='pt-0'>
              <BeFOODStores
                dateRange={dateRange}
                selectedStores={selectedStores}
                filterType={filterType}
                type='least-revenue'
                limit={5}
              />
            </CardContent>
          </Card>
        )}

        {!isLimitedBrand && (
          <>
            <Card className='h-full'>
              <CardHeader className='pb-3'>
                <CardTitle className='text-sm font-medium text-blue-700'>
                  BE - TUTIMI
                </CardTitle>
                <CardDescription className='text-xs'>
                  Doanh số thấp nhất
                </CardDescription>
              </CardHeader>
              <CardContent className='pt-0'>
                <BeTUTIMIStores
                  dateRange={dateRange}
                  selectedStores={selectedStores}
                  filterType={filterType}
                  type='least-revenue'
                  limit={5}
                />
              </CardContent>
            </Card>

            <Card className='h-full'>
              <CardHeader className='pb-3'>
                <CardTitle className='text-sm font-medium text-blue-700'>
                  BE - DHI
                </CardTitle>
                <CardDescription className='text-xs'>
                  Doanh số thấp nhất
                </CardDescription>
              </CardHeader>
              <CardContent className='pt-0'>
                <BeDHIStores
                  dateRange={dateRange}
                  selectedStores={selectedStores}
                  filterType={filterType}
                  type='least-revenue'
                  limit={5}
                />
              </CardContent>
            </Card>
          </>
        )}

        {/* Shopee Sources */}
        <Card className='h-full'>
          <CardHeader className='pb-3'>
            <CardTitle className='text-sm font-medium text-orange-700'>
              ShopeeFood
            </CardTitle>
            <CardDescription className='text-xs'>
              Doanh số thấp nhất
            </CardDescription>
          </CardHeader>
          <CardContent className='pt-0'>
            <ShopeeStores
              dateRange={dateRange}
              selectedStores={selectedStores}
              filterType={filterType}
              type='least-revenue'
              limit={5}
            />
          </CardContent>
        </Card>

        {!isLimitedBrand && (
          <Card className='h-full'>
            <CardHeader className='pb-3'>
              <CardTitle className='text-sm font-medium text-orange-700'>
                ShopeeFood - DHI
              </CardTitle>
              <CardDescription className='text-xs'>
                Doanh số thấp nhất
              </CardDescription>
            </CardHeader>
            <CardContent className='pt-0'>
              <ShopeeNOWDHIStores
                dateRange={dateRange}
                selectedStores={selectedStores}
                filterType={filterType}
                type='least-revenue'
                limit={5}
              />
            </CardContent>
          </Card>
        )}

        {/* Tai Cho Sources */}
        <Card className='h-full'>
          <CardHeader className='pb-3'>
            <CardTitle className='text-sm font-medium text-red-700'>
              TẠI CHỖ
            </CardTitle>
            <CardDescription className='text-xs'>
              Doanh số thấp nhất
            </CardDescription>
          </CardHeader>
          <CardContent className='pt-0'>
            <TaiChoStores
              dateRange={dateRange}
              selectedStores={selectedStores}
              filterType={filterType}
              type='least-revenue'
              limit={5}
            />
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

export default SourcesSection
