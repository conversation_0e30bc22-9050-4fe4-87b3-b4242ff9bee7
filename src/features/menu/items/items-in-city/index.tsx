import { useState, useMemo } from 'react'

import { useCustomizationsData } from '@/hooks/api'

import { Header } from '@/components/layout/header'
import { Main } from '@/components/layout/main'
import { ProfileDropdown } from '@/components/profile-dropdown'
import { Search } from '@/components/search'
import { ThemeSwitch } from '@/components/theme-switch'

import {
  columns,
  ItemsInCityButtons,
  ItemsInCityTableSkeleton,
  ItemsInCityDialogs,
  ItemsInCityDataTable,
  CustomizationDialog
} from './components'
import MenuItemsProvider, { useItemsInCity } from './context'
import { ItemsInCity } from './data'
import { useItemsInCityForTable } from './hooks'

function ItemsInCityContent() {
  const { setOpen, setCurrentRow } = useItemsInCity()
  const [isCustomizationDialogOpen, setIsCustomizationDialogOpen] = useState(false)
  const [selectedMenuItem, setSelectedMenuItem] = useState<ItemsInCity | null>(null)
  const [selectedItemTypeUid, setSelectedItemTypeUid] = useState<string>('all')
  const [selectedCityUid, setSelectedCityUid] = useState<string>('all')
  const [selectedDaysOfWeek, setSelectedDaysOfWeek] = useState<string[]>([])
  const [selectedStatus, setSelectedStatus] = useState<string>('all')

  const filterParams = useMemo(() => {
    const params: Record<string, string> = {}
    if (selectedItemTypeUid !== 'all') {
      params.item_type_uid = selectedItemTypeUid
    }
    if (selectedCityUid !== 'all') {
      params.city_uid = selectedCityUid
    }
    if (selectedDaysOfWeek.length > 0) {
      params.time_sale_date_week = selectedDaysOfWeek.join(',')
    }

    if (selectedStatus !== 'all') {
      params.active = selectedStatus
    }
    return params
  }, [selectedItemTypeUid, selectedCityUid, selectedDaysOfWeek, selectedStatus])

  const {
    data: menuItems = [],
    isLoading: itemsLoading,
    error: itemsError
  } = useItemsInCityForTable({
    params: filterParams
  })

  const { data: customizations = [] } = useCustomizationsData()

  const isLoading = itemsLoading
  const error = itemsError

  const handleCustomizationClick = (menuItem: ItemsInCity) => {
    try {
      setSelectedMenuItem(menuItem)
      setIsCustomizationDialogOpen(true)
    } catch (_error) {
      // Error
    }
  }

  const handleCopyClick = (menuItem: ItemsInCity) => {
    try {
      setCurrentRow(menuItem)
      setOpen('copy')
    } catch (_error) {
      // Error
    }
  }

  if (error) {
    return (
      <div className='flex items-center justify-center p-8'>
        <div className='text-center'>
          <p className='text-muted-foreground mb-2 text-sm'>Có lỗi xảy ra khi tải dữ liệu</p>
          <p className='text-muted-foreground text-xs'>
            {itemsError && `Món ăn: ${itemsError?.message || 'Lỗi không xác định'}`}
          </p>
        </div>
      </div>
    )
  }

  return (
    <>
      <Header>
        <div className='ml-auto flex items-center space-x-4'>
          <Search />
          <ThemeSwitch />
          <ProfileDropdown />
        </div>
      </Header>

      <Main>
        <div className='mb-2 flex flex-wrap items-center justify-between space-y-2 gap-x-4'>
          <div>
            <h2 className='text-2xl font-bold tracking-tight'>Món ăn tại thành phố</h2>
            <p className='text-muted-foreground'>
              Quản lý thông tin món ăn, giá cả và cấu hình theo từng thành phố
            </p>
          </div>
          <ItemsInCityButtons />
        </div>
        <div className='-mx-4 flex-1 overflow-auto px-4 py-1 lg:flex-row lg:space-y-0 lg:space-x-12'>
          {isLoading && <ItemsInCityTableSkeleton />}
          {!isLoading && (
            <ItemsInCityDataTable
              columns={columns}
              data={menuItems}
              onCustomizationClick={handleCustomizationClick}
              onCopyClick={handleCopyClick}
              customizations={customizations}
              selectedItemTypeUid={selectedItemTypeUid}
              onItemTypeChange={setSelectedItemTypeUid}
              selectedCityUid={selectedCityUid}
              onCityChange={setSelectedCityUid}
              selectedDaysOfWeek={selectedDaysOfWeek}
              onDaysOfWeekChange={setSelectedDaysOfWeek}
              selectedStatus={selectedStatus}
              onStatusChange={setSelectedStatus}
            />
          )}
        </div>
      </Main>

      <ItemsInCityDialogs />

      {isCustomizationDialogOpen && (
        <CustomizationDialog
          open={isCustomizationDialogOpen}
          onOpenChange={setIsCustomizationDialogOpen}
          menuItem={selectedMenuItem}
          menuItems={menuItems}
          customizations={customizations}
        />
      )}
    </>
  )
}

export default function ItemsInCityPage() {
  return (
    <MenuItemsProvider>
      <ItemsInCityContent />
    </MenuItemsProvider>
  )
}
