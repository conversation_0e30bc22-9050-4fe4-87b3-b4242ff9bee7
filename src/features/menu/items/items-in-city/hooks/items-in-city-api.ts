import * as XLSX from 'xlsx'

import { api } from '@/lib/api'

// Type definitions
export interface ItemExtraData {
  id: string
  item_name: string
  description?: string
  ots_price: number
  ots_tax: number
  time_cooking: number
  item_id_barcode?: string
  is_eat_with: boolean
  is_featured: boolean
  item_class_uid: string
  item_type_uid: string
  city_uid: string
  item_id?: string
  unit_uid: string
  unit_secondary_uid?: string
  enable_edit_price: boolean
  is_print_label: boolean
  is_allow_discount: boolean
  is_virtual_item: boolean
  is_item_service: boolean
  is_buffet_item: boolean
  inqrFormula?: string
  is_service: boolean
  sort: number
}

export interface ItemCity {
  id: string
  city_name: string
}

export interface ItemInCity extends ItemExtraData {
  created_at: string | number
  updated_at: string | number
  active: boolean
  revision?: number
  cities?: ItemCity[]
  item_id_eat_with?: string
  customization_uid?: string
  extra_data?: {
    enable_edit_price?: boolean
    is_virtual_item?: boolean
    is_item_service?: boolean
    is_buffet_item?: boolean
    [key: string]: unknown
  }
}

export interface ItemsInCityApiResponse {
  data: ItemInCity[]
  total_item: number
  track_id: string
}

export interface GetItemsInCityParams {
  company_uid: string
  brand_uid: string
  page?: number
  city_uid?: string
  item_type_uid?: string
  time_sale_date_week?: number
  active?: number
  reverse?: number
  search?: string
  limit?: number
}

export interface DeleteItemInCityParams {
  company_uid: string
  brand_uid: string
  id: string
}

export interface CreateItemInCityRequest extends Omit<ItemExtraData, 'id'> {
  company_uid: string
  brand_uid: string
}

export interface UpdateItemInCityRequest extends ItemExtraData {
  company_uid: string
  brand_uid: string
}

export interface UpdateItemCustomizationRequest {
  id: string
  customization_uid: string | null
}

export interface GetItemByListIdParams {
  company_uid: string
  brand_uid: string
  list_item_id: string
}

export interface GetItemByIdParams {
  id: string
}

export interface DownloadTemplateParams {
  company_uid: string
  brand_uid: string
  city_uid?: string
  item_type_uid?: string
  active?: string
}

// Cache implementation
const itemsInCityCache = new Map<string, { data: ItemsInCityApiResponse; timestamp: number }>()
const pendingRequests = new Map<string, Promise<ItemsInCityApiResponse>>()
const CACHE_DURATION = 10 * 60 * 1000 // 10 minutes

// Type guard for API errors
interface ApiError {
  response?: {
    status?: number
    data?: {
      message?: string
    }
  }
}

function isApiError(error: unknown): error is ApiError {
  return typeof error === 'object' && error !== null && 'response' in error
}

// Local interface for import items
interface ImportItemsRequest {
  company_uid: string
  brand_uid: string
  items: unknown[]
}

// Helper function to create Excel blob
function createItemsExcelBlob(data: unknown[]): Blob {
  const worksheet = XLSX.utils.json_to_sheet(data)
  const workbook = XLSX.utils.book_new()
  XLSX.utils.book_append_sheet(workbook, worksheet, 'Items')
  const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' })
  return new Blob([excelBuffer], {
    type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  })
}

export const itemsInCityApiService = {
  /**
   * Get items in city data with request deduplication and caching
   */
  getItemsInCity: async (params: GetItemsInCityParams): Promise<ItemsInCityApiResponse> => {
    const requestKey = `${params.company_uid}-${params.brand_uid}-${params.page || 1}-${params.city_uid || 'all'}-${params.item_type_uid || 'all'}-${params.time_sale_date_week || 0}-${params.active || 0}-${params.reverse || 0}-${params.search || ''}-${params.active ?? 1}-${params.limit || 50}`

    const cached = itemsInCityCache.get(requestKey)
    if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
      return cached.data
    }

    const existingRequest = pendingRequests.get(requestKey)
    if (existingRequest) {
      return existingRequest
    }

    const requestPromise = (async () => {
      try {
        const queryParams = new URLSearchParams()
        queryParams.append('company_uid', params.company_uid)
        queryParams.append('brand_uid', params.brand_uid)

        if (params.page) {
          queryParams.append('page', params.page.toString())
        }

        if (params.item_type_uid) {
          queryParams.append('item_type_uid', params.item_type_uid)
        }

        if (params.city_uid) {
          queryParams.append('city_uid', params.city_uid)
        }

        if (params.time_sale_date_week) {
          queryParams.append('time_sale_date_week', params.time_sale_date_week.toString())
        }

        if (params.reverse !== undefined) {
          queryParams.append('reverse', params.reverse.toString())
        }

        if (params.search) {
          queryParams.append('search', params.search)
        }

        if (params.active !== undefined) {
          queryParams.append('active', params.active.toString())
        }

        if (params.limit) {
          queryParams.append('limit', params.limit.toString())
        }

        const response = await api.get(`/mdata/v1/items?${queryParams.toString()}`, {
          headers: {
            Accept: 'application/json, text/plain, */*',
            'accept-language': 'vi',
            fabi_type: 'pos-cms',
            'x-client-timezone': '25200000'
          },
          timeout: 30000
        })

        if (!response.data || typeof response.data !== 'object') {
          throw new Error('Invalid response format from items in city API')
        }

        const result = response.data as ItemsInCityApiResponse

        itemsInCityCache.set(requestKey, {
          data: result,
          timestamp: Date.now()
        })

        return result
      } finally {
        pendingRequests.delete(requestKey)
      }
    })()

    pendingRequests.set(requestKey, requestPromise)
    return requestPromise
  },

  /**
   * Delete item in city
   */
  deleteItemInCity: async (params: DeleteItemInCityParams): Promise<void> => {
    try {
      const queryParams = new URLSearchParams()
      queryParams.append('company_uid', params.company_uid)
      queryParams.append('brand_uid', params.brand_uid)
      queryParams.append('id', params.id)

      await api.delete(`/mdata/v1/item?${queryParams.toString()}`, {
        headers: {
          Accept: 'application/json, text/plain, */*',
          Connection: 'keep-alive',
          Origin: 'https://fabi.ipos.vn',
          Referer: 'https://fabi.ipos.vn/',
          'Sec-Fetch-Dest': 'empty',
          'Sec-Fetch-Mode': 'cors',
          'Sec-Fetch-Site': 'same-site',
          'User-Agent':
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
          'accept-language': 'vi',
          fabi_type: 'pos-cms',
          'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"',
          'sec-ch-ua-mobile': '?0',
          'sec-ch-ua-platform': '"Windows"',
          'x-client-timezone': '25200000'
        },
        timeout: 30000
      })

      itemsInCityCache.clear()
    } catch (error: unknown) {
      if (isApiError(error) && error.response?.status === 404) {
        throw new Error('Item not found.')
      }
      throw error
    }
  },

  /**
   * Download template for items in city
   */
  downloadTemplate: async (params: DownloadTemplateParams): Promise<Blob> => {
    const queryParams = new URLSearchParams({
      skip_limit: 'true',
      company_uid: params.company_uid,
      brand_uid: params.brand_uid,
      ...(params.city_uid && params.city_uid !== 'all' && { city_uid: params.city_uid }),
      ...(params.item_type_uid &&
        params.item_type_uid !== 'all' && {
          item_type_uid: params.item_type_uid
        }),
      ...(params.active && params.active !== 'all' && { active: params.active })
    })

    const response = await api.get(`/mdata/v1/items?${queryParams}`)
    const data = Array.isArray(response.data?.data) ? response.data.data : []
    return createItemsExcelBlob(data)
  },

  /**
   * Create a new item in city
   */
  createItemInCity: async (data: CreateItemInCityRequest): Promise<unknown> => {
    try {
      const response = await api.post('/mdata/v1/item', data, {
        headers: {
          'Content-Type': 'application/json',
          'accept-language': 'vi',
          fabi_type: 'pos-cms',
          'x-client-timezone': '25200000'
        },
        timeout: 30000
      })

      itemsInCityCache.clear()
      return response.data.data || response.data
    } catch (error: unknown) {
      if (isApiError(error) && error.response?.status === 400) {
        throw new Error(error.response.data?.message || 'Invalid data provided.')
      }
      throw error
    }
  },

  /**
   * Update an existing item in city
   */
  updateItemInCity: async (data: UpdateItemInCityRequest): Promise<unknown> => {
    try {
      const response = await api.put('/mdata/v1/item', data, {
        headers: {
          'Content-Type': 'application/json;charset=UTF-8',
          'accept-language': 'vi',
          fabi_type: 'pos-cms',
          'x-client-timezone': '25200000'
        },
        timeout: 30000
      })

      itemsInCityCache.clear()
      return response.data.data || response.data
    } catch (error: unknown) {
      if (isApiError(error) && error.response?.status === 400) {
        throw new Error(error.response.data?.message || 'Invalid data provided.')
      }
      throw error
    }
  },

  /**
   * Update only customization_uid for an item
   */
  updateItemCustomization: async (data: UpdateItemCustomizationRequest): Promise<unknown> => {
    try {
      const currentItem = await itemsInCityApiService.getItemById({ id: data.id })

      const updateData = {
        ...(currentItem.data as Record<string, unknown>),
        customization_uid: data.customization_uid
      }

      const response = await api.put('/mdata/v1/item', updateData, {
        headers: {
          'Content-Type': 'application/json;charset=UTF-8',
          'accept-language': 'vi',
          fabi_type: 'pos-cms',
          'x-client-timezone': '25200000'
        },
        timeout: 30000
      })

      itemsInCityCache.clear()
      return response.data.data || response.data
    } catch (_error) {
      // Error handling
    }
  },

  /**
   * Get item by list_item_id for copy functionality
   */
  getItemByListId: async (params: GetItemByListIdParams): Promise<{ data: unknown }> => {
    try {
      const queryParams = new URLSearchParams({
        skip_limit: 'true',
        company_uid: params.company_uid,
        brand_uid: params.brand_uid,
        is_all: 'true',
        list_item_id: params.list_item_id
      })

      const response = await api.get(`/mdata/v1/items?${queryParams}`, {
        headers: {
          'Content-Type': 'application/json;charset=UTF-8',
          'accept-language': 'vi',
          fabi_type: 'pos-cms',
          'x-client-timezone': '25200000'
        },
        timeout: 30000
      })

      const items = Array.isArray(response.data?.data) ? response.data.data : []
      if (!items.length) {
        throw new Error('Item not found')
      }

      return { data: items[0] }
    } catch (error: unknown) {
      if (isApiError(error) && error.response?.status === 404) {
        throw new Error('Item not found.')
      }
      throw error
    }
  },

  /**
   * Get item by ID
   */
  getItemById: async (params: GetItemByIdParams): Promise<{ data: unknown }> => {
    try {
      const response = await api.get(`/mdata/v1/item?id=${params.id}`, {
        headers: {
          Accept: 'application/json, text/plain, */*',
          'accept-language': 'vi',
          fabi_type: 'pos-cms',
          'x-client-timezone': '25200000'
        },
        timeout: 30000
      })

      if (!response.data || typeof response.data !== 'object') {
        throw new Error('Invalid response format from item detail API')
      }

      return response.data as { data: unknown }
    } catch (error: unknown) {
      if (isApiError(error) && error.response?.status === 404) {
        throw new Error('Item not found.')
      }
      throw error
    }
  },

  importItems: async (params: ImportItemsRequest) => {
    const response = await api.post('/mdata/v1/items/import', {
      company_uid: params.company_uid,
      brand_uid: params.brand_uid,
      items: params.items
    })
    return response.data
  },

  /**
   * Clear cache for items in city
   */
  clearCache: () => {
    itemsInCityCache.clear()
    pendingRequests.clear()
  },

  /**
   * Get cache stats
   */
  getCacheStats: () => ({
    cacheSize: itemsInCityCache.size,
    pendingRequests: pendingRequests.size
  })
}
