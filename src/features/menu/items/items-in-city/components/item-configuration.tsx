import type { UseFormReturn } from 'react-hook-form'

import { Checkbox } from '@/components/ui/checkbox'
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { Input } from '@/components/ui/input'

import type { FormValues } from './items-in-city-mutate'

interface Props {
  form: UseFormReturn<FormValues>
}

export function ItemConfiguration({ form }: Props) {
  return (
    <div className='space-y-6'>
      {/* Cấu hình section */}
      <div className='space-y-4'>
        <h3 className='border-b pb-2 text-lg font-medium'><PERSON><PERSON><PERSON> hình sửa giá, nhập số l<PERSON>ợng</h3>
        <div className='space-y-3'>
          <FormField
            control={form.control}
            name='enable_edit_price'
            render={({ field }) => (
              <FormItem className='flex flex-row items-start space-y-0 space-x-3'>
                <FormControl>
                  <Checkbox checked={field.value} onCheckedChange={field.onChange} />
                </FormControl>
                <div className='space-y-1 leading-none'>
                  <FormLabel>Cho phép sửa giá khi bán</FormLabel>
                </div>
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name='is_print_label'
            render={({ field }) => (
              <FormItem className='flex flex-row items-start space-y-0 space-x-3'>
                <FormControl>
                  <Checkbox checked={field.value} onCheckedChange={field.onChange} />
                </FormControl>
                <div className='space-y-1 leading-none'>
                  <FormLabel>Yêu cầu nhập số lượng khi gọi đồ</FormLabel>
                </div>
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name='is_allow_discount'
            render={({ field }) => (
              <FormItem className='flex flex-row items-start space-y-0 space-x-3'>
                <FormControl>
                  <Checkbox checked={field.value} onCheckedChange={field.onChange} />
                </FormControl>
                <div className='space-y-1 leading-none'>
                  <FormLabel>Cho phép bỏ món này khỏng cần quyền áp dụng</FormLabel>
                </div>
              </FormItem>
            )}
          />
        </div>

        <div className='space-y-3'>
          <FormField
            control={form.control}
            name='is_virtual_item'
            render={({ field }) => (
              <FormItem className='flex flex-row items-start space-y-0 space-x-3'>
                <FormControl>
                  <Checkbox checked={field.value} onCheckedChange={field.onChange} />
                </FormControl>
                <div className='space-y-1 leading-none'>
                  <FormLabel>Cấu hình món ảo</FormLabel>
                </div>
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name='is_item_service'
            render={({ field }) => (
              <FormItem className='flex flex-row items-start space-y-0 space-x-3'>
                <FormControl>
                  <Checkbox checked={field.value} onCheckedChange={field.onChange} />
                </FormControl>
                <div className='space-y-1 leading-none'>
                  <FormLabel>Cấu hình món dịch vụ</FormLabel>
                </div>
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name='is_buffet_item'
            render={({ field }) => (
              <FormItem className='flex flex-row items-start space-y-0 space-x-3'>
                <FormControl>
                  <Checkbox checked={field.value} onCheckedChange={field.onChange} />
                </FormControl>
                <div className='space-y-1 leading-none'>
                  <FormLabel>Cấu hình món ăn là vé buffet</FormLabel>
                </div>
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name='is_service'
            render={({ field }) => (
              <FormItem className='flex flex-row items-start space-y-0 space-x-3'>
                <FormControl>
                  <Checkbox checked={field.value} onCheckedChange={field.onChange} />
                </FormControl>
                <div className='space-y-1 leading-none'>
                  <FormLabel>Cấu hình món dịch vụ</FormLabel>
                </div>
              </FormItem>
            )}
          />
        </div>

        {/* InQR Formula */}
        <FormField
          control={form.control}
          name='inqrFormula'
          render={({ field }) => (
            <FormItem>
              <FormLabel>Công thức inQr cho máy pha trà</FormLabel>
              <FormControl>
                <Input placeholder='Nhập công thức InQR' {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      {/* Thứ tự hiển thị */}
      <div className='grid grid-cols-1 gap-4'>
        <FormField
          control={form.control}
          name='sort'
          render={({ field }) => (
            <FormItem>
              <FormLabel>Thứ tự hiển thị</FormLabel>
              <FormControl>
                <Input
                  type='number'
                  placeholder='Nhập số thứ tự hiển thị'
                  {...field}
                  onChange={e => field.onChange(Number(e.target.value))}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>
    </div>
  )
}
