import { useState, useEffect } from 'react'

import { z } from 'zod'

import { useForm, type SubmitHandler } from 'react-hook-form'

import { zodResolver } from '@hookform/resolvers/zod'

import { useAuthStore } from '@/stores/authStore'

import { useCitiesData, useItemTypesData, useUnitsData, useItemClassesData } from '@/hooks/api'

import { Button } from '@/components/ui/button'
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog'
import { Form } from '@/components/ui/form'

import { ItemsInCity } from '../data'
import {
  useCreateItemInCity,
  useUpdateItemInCity,
  useItemInCityDetail,
  useItemByListId
} from '../hooks'
import type { CreateItemInCityRequest, UpdateItemInCityRequest } from '../hooks/items-in-city-api'
import { ItemFormSections } from './item-form-sections'
import { PriceSourceDialog } from './price-source-dialog'

const formSchema = z.object({
  item_name: z.string().min(1, 'Tên món là bắt buộc'),
  ots_price: z.coerce.number().min(0, 'Giá phải lớn hơn hoặc bằng 0'),
  description: z.string().optional(),
  item_id_barcode: z.string().optional(),
  is_eat_with: z.preprocess(val => Boolean(val), z.boolean()),
  is_featured: z.preprocess(val => Boolean(val), z.boolean()),
  item_class_uid: z.string().min(1, 'Vui lòng chọn nhóm'),
  item_type_uid: z.string().min(1, 'Vui lòng chọn loại món'),
  city_uid: z.string().min(1, 'Vui lòng chọn thành phố'),
  item_id: z.string().optional(),
  unit_uid: z.string().min(1, 'Vui lòng chọn đơn vị tính'),
  unit_secondary_uid: z.string().optional(),
  ots_tax: z.coerce.number().min(0).max(100).default(0),
  time_cooking: z.coerce.number().min(0).default(0),
  enable_edit_price: z.preprocess(val => Boolean(val), z.boolean()),
  is_print_label: z.preprocess(val => Boolean(val), z.boolean()),
  is_allow_discount: z.preprocess(val => Boolean(val), z.boolean()),
  is_virtual_item: z.preprocess(val => Boolean(val), z.boolean()),
  is_item_service: z.preprocess(val => Boolean(val), z.boolean()),
  is_buffet_item: z.preprocess(val => Boolean(val), z.boolean()),
  inqrFormula: z.string().optional(),
  is_service: z.preprocess(val => Boolean(val), z.boolean()),
  priceBySource: z.preprocess(val => Boolean(val), z.boolean()),
  selectedDays: z.array(z.string()).default([]),
  selectedHours: z.array(z.string()).default([]),
  sort: z.coerce.number().min(0).default(0),
  store_uid: z.string().optional()
})

export type FormValues = z.infer<typeof formSchema>

interface Props {
  open: boolean
  onOpenChange: (open: boolean) => void
  currentRow?: ItemsInCity
  isCopyMode?: boolean
}

export function ItemsInCityMutate({ open, onOpenChange, currentRow, isCopyMode = false }: Props) {
  const isUpdate = !!currentRow && !isCopyMode
  const isCopy = !!currentRow && isCopyMode
  const [_imageFile, setImageFile] = useState<File | null>(null)
  const [openDialog, setOpenDialog] = useState(false)

  const { company, brands } = useAuthStore(state => state.auth)
  const selectedBrand = brands?.[0]

  const { data: cities = [] } = useCitiesData()
  const { data: itemTypes = [] } = useItemTypesData()
  const { data: units = [] } = useUnitsData()
  const { data: itemClasses = [] } = useItemClassesData()
  const { data: itemDetail } = useItemInCityDetail(currentRow?.id, isUpdate && open)

  const { data: itemDetailForCopy } = useItemByListId(
    {
      company_uid: company?.id || '',
      brand_uid: selectedBrand?.id || '',
      list_item_id: currentRow?.code || ''
    },
    isCopy && open
  )

  const activeItemDetail = isCopy ? itemDetailForCopy : itemDetail
  const createItemMutation = useCreateItemInCity()
  const updateItemMutation = useUpdateItemInCity()

  // Type assertion for itemDetail data
  const itemData = activeItemDetail?.data as Record<string, unknown> | undefined
  const extraData = (itemData?.extra_data as Record<string, unknown>) || {}

  const form = useForm({
    resolver: zodResolver(formSchema),
    defaultValues: {
      item_name: (itemData?.item_name as string) || '',
      ots_price: (itemData?.ots_price as number) || 0,
      description: (itemData?.description as string) || '',
      item_id_barcode: (itemData?.item_id_barcode as string) || '',
      is_eat_with: Boolean(itemData?.is_eat_with),
      is_featured: Boolean(itemData?.is_featured),
      item_class_uid: (itemData?.item_class_uid as string) || '',
      item_type_uid: (itemData?.item_type_uid as string) || '',
      city_uid: (itemData?.city_uid as string) || '',
      item_id: (itemData?.item_id as string) || '',
      unit_uid: (itemData?.unit_uid as string) || '',
      unit_secondary_uid: (itemData?.unit_secondary_uid as string) || '',
      ots_tax: (itemData?.ots_tax as number) || 0,
      time_cooking: (itemData?.time_cooking as number) || 0,
      enable_edit_price: Boolean(extraData?.enable_edit_price) || false,
      is_print_label: Boolean(itemData?.is_print_label) || false,
      is_allow_discount: Boolean(itemData?.is_allow_discount) || false,
      is_virtual_item: Boolean(extraData?.is_virtual_item) || false,
      is_item_service: Boolean(extraData?.is_item_service) || false,
      is_buffet_item:
        Boolean(extraData?.is_buffet_item) ||
        currentRow?.buffetConfig === 'Áp dụng buffet' ||
        false,
      inqrFormula: '',
      is_service: Boolean(itemData?.is_service) || false,
      priceBySource: false,
      selectedDays: [],
      selectedHours: [],
      sort: (itemData?.sort as number) || 0,
      store_uid: ''
    }
  })

  useEffect(() => {
    if ((isUpdate || isCopy) && activeItemDetail?.data && open) {
      const resetItemData = activeItemDetail.data as Record<string, unknown>
      const resetExtraData = (resetItemData?.extra_data as Record<string, unknown>) || {}

      form.reset({
        item_name: (resetItemData?.item_name as string) || '',
        ots_price: (resetItemData?.ots_price as number) || 0,
        description: (resetItemData?.description as string) || '',
        item_id_barcode: (resetItemData?.item_id_barcode as string) || '',
        is_eat_with: Boolean(resetItemData?.is_eat_with),
        is_featured: Boolean(resetItemData?.is_featured),
        item_class_uid: (resetItemData?.item_class_uid as string) || '',
        item_type_uid: (resetItemData?.item_type_uid as string) || '',
        city_uid: (resetItemData?.city_uid as string) || '',
        item_id: isCopy ? '' : (resetItemData?.item_id as string) || '', // Để trống mã khi copy
        unit_uid: (resetItemData?.unit_uid as string) || '',
        unit_secondary_uid: (resetItemData?.unit_secondary_uid as string) || '',
        ots_tax: (resetItemData?.ots_tax as number) || 0,
        time_cooking: (resetItemData?.time_cooking as number) || 0,
        enable_edit_price: Boolean(resetExtraData?.enable_edit_price) || false,
        is_print_label: Boolean(resetItemData?.is_print_label) || false,
        is_allow_discount: Boolean(resetItemData?.is_allow_discount) || false,
        is_virtual_item: Boolean(resetExtraData?.is_virtual_item) || false,
        is_item_service: Boolean(resetExtraData?.is_item_service) || false,
        is_buffet_item:
          Boolean(resetExtraData?.is_buffet_item) ||
          currentRow?.buffetConfig === 'Áp dụng buffet' ||
          false,
        inqrFormula: '',
        is_service: Boolean(resetItemData?.is_service) || false,
        priceBySource: false,
        selectedDays: [],
        selectedHours: [],
        sort: (resetItemData?.sort as number) || 0
      })
    }
  }, [isUpdate, isCopy, activeItemDetail, open, form, currentRow?.buffetConfig])

  const onSubmit: SubmitHandler<FormValues> = async data => {
    try {
      if (!company?.id || !selectedBrand?.id) {
        return
      }

      const requestData = isUpdate
        ? {
            id: currentRow?.id || '',
            item_id: (itemData?.item_id as string) || currentRow?.originalData?.item_id || '',
            item_name: data.item_name,
            description: data.description || '',
            ots_price: data.ots_price,
            ots_tax: data.ots_tax,
            ta_price: data.ots_price,
            ta_tax: data.ots_tax,
            time_sale_hour_day: 0,
            time_sale_date_week: 0,
            allow_take_away: 1,
            is_eat_with: data.is_eat_with ? 1 : 0,
            image_path: '',
            image_path_thumb: '',
            item_color: '',
            list_order: 0,
            is_service: data.is_service ? 1 : 0,
            is_material: 0,
            active: 1,
            user_id: '',
            is_foreign: 0,
            quantity_default: 0,
            price_change: 0,
            currency_type_id: '',
            point: 0,
            is_gift: 0,
            is_fc: 0,
            show_on_web: 0,
            show_price_on_web: 0,
            cost_price: 0,
            is_print_label: data.is_print_label ? 1 : 0,
            quantity_limit: 0,
            is_kit: 0,
            time_cooking: data.time_cooking || 0,
            item_id_barcode: data.item_id_barcode || '',
            process_index: 0,
            is_allow_discount: data.is_allow_discount ? 1 : 0,
            quantity_per_day: 0,
            item_id_eat_with: '',
            is_parent: 0,
            is_sub: 0,
            item_id_mapping: '',
            effective_date: 0,
            expire_date: 0,
            sort: data.sort || 0,
            sort_online: 1000,
            extra_data: {
              price_by_source: [],
              is_virtual_item: data.is_virtual_item ? 1 : 0,
              is_item_service: data.is_item_service ? 1 : 0,
              no_update_quantity_toping: data.is_featured ? 1 : 0,
              enable_edit_price: data.enable_edit_price ? 1 : 0,
              is_buffet_item: data.is_buffet_item ? 1 : 0,
              exclude_items_buffet: [],
              up_size_buffet: []
            },
            revision: (itemData?.revision as number) || 0,
            unit_uid: data.unit_uid,
            unit_secondary_uid: data.unit_secondary_uid || null,
            item_type_uid: data.item_type_uid,
            item_class_uid: data.item_class_uid || null,
            source_uid: null,
            brand_uid: selectedBrand.id,
            city_uid: data.city_uid,
            company_uid: company.id,
            customization_uid: (itemData?.customization_uid as string) || null,
            is_fabi: 1,
            deleted: false,
            created_by: (itemData?.created_by as string) || '',
            updated_by: (itemData?.updated_by as string) || '',
            deleted_by: null,
            created_at: (itemData?.created_at as number) || 0,
            updated_at: Math.floor(Date.now() / 1000),
            deleted_at: null,
            cities: (itemData?.cities as unknown[]) || [],
            item_old: {}
          }
        : {
            unit_uid: data.unit_uid,
            ta_price: data.ots_price,
            ots_tax: data.ots_tax,
            ta_tax: data.ots_tax,
            description: data.description,
            item_id_barcode: data.item_id_barcode,
            is_eat_with: data.is_eat_with ? 1 : 0,
            time_cooking: data.time_cooking,
            is_print_label: data.is_print_label ? 1 : 0,
            is_allow_discount: data.is_allow_discount ? 1 : 0,
            is_service: data.is_service ? 1 : 0,
            unit_secondary_uid: data.unit_secondary_uid || null,
            item_class_uid: data.item_class_uid,
            extra_data: {
              price_by_source: [],
              is_virtual_item: data.is_virtual_item ? 1 : 0,
              is_item_service: data.is_item_service ? 1 : 0,
              no_update_quantity_toping: data.is_featured ? 1 : 0,
              enable_edit_price: data.enable_edit_price ? 1 : 0,
              is_buffet_item: data.is_buffet_item ? 1 : 0,
              exclude_items_buffet: [],
              up_size_buffet: []
            },
            item_type_uid: data.item_type_uid,
            item_name: data.item_name,
            city_uid: data.city_uid,
            company_uid: company.id,
            brand_uid: selectedBrand.id,
            item_id:
              data.item_id || `ITEM-${Math.random().toString(36).substring(2, 6).toUpperCase()}`,
            ots_price: data.ots_price,
            sort: data.sort || 1000
          }

      if (isUpdate && currentRow?.id) {
        updateItemMutation.mutate(requestData as unknown as UpdateItemInCityRequest)
      } else {
        createItemMutation.mutate(requestData as unknown as CreateItemInCityRequest)
      }

      onOpenChange(false)
      form.reset()
    } catch (_error) {
      // Error handling is done in the hook
    }
  }

  const handleImageChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      setImageFile(file)
    }
  }

  return (
    <>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className='max-h-[90vh] max-w-4xl overflow-y-auto sm:max-w-3xl'>
          <DialogHeader>
            <DialogTitle>
              {isUpdate ? 'Cập nhật món' : isCopy ? 'Sao chép món' : 'Tạo món'}
            </DialogTitle>
          </DialogHeader>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-6'>
              <ItemFormSections
                form={form}
                itemTypes={itemTypes}
                itemClasses={itemClasses}
                units={units}
                cities={cities}
                imageFile={_imageFile}
                onImageChange={handleImageChange}
              />

              {/* Dialog Footer */}
              <DialogFooter className='gap-2'>
                <DialogClose asChild>
                  <Button type='button' variant='outline'>
                    Hủy
                  </Button>
                </DialogClose>
                <Button type='submit'>Lưu</Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      <PriceSourceDialog
        open={openDialog}
        onOpenChange={setOpenDialog}
        storeUid={form.watch('store_uid') || ''}
        onConfirm={() => {
          // Handle price source data if needed
        }}
      />
    </>
  )
}
