import { z } from 'zod'

import { useForm } from 'react-hook-form'

import { zodResolver } from '@hookform/resolvers/zod'

import { useSourcesData } from '@/hooks/api/use-sources'

import { Button } from '@/components/ui/button'
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select'

const priceSourceSchema = z.object({
  source: z.string().min(1, 'Vui lòng chọn nguồn đơn'),
  amount: z.coerce.number().min(0, 'Số tiền phải lớn hơn hoặc bằng 0')
})

type PriceSourceFormValues = z.infer<typeof priceSourceSchema>

interface Props {
  open: boolean
  onOpenChange: (open: boolean) => void
  onConfirm?: (data: PriceSourceFormValues) => void
  storeUid: string
}

export function PriceSourceDialog({ open, onOpenChange, onConfirm, storeUid }: Props) {
  const form = useForm<PriceSourceFormValues>({
    resolver: zodResolver(priceSourceSchema),
    defaultValues: {
      source: '',
      amount: 0
    }
  })

  const onSubmit = (data: PriceSourceFormValues) => {
    onConfirm?.(data)
    onOpenChange(false)
    form.reset()
  }
  const { data: sourcesData = [] } = useSourcesData({
    store_uid: storeUid,
    enabled: open && !!storeUid
  })

  const sources = sourcesData.map(source => ({
    id: source.id,
    name: source.sourceName
  }))

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className='max-w-md'>
        <DialogHeader>
          <DialogTitle>Cấu hình giá theo nguồn</DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-4'>
            <div className='grid grid-cols-[120px_1fr] items-center gap-4'>
              {/* Nguồn đơn */}
              <FormLabel className='text-sm font-medium text-gray-600'>Nguồn đơn</FormLabel>
              <FormField
                control={form.control}
                name='source'
                render={({ field }) => (
                  <FormItem>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl>
                        <SelectTrigger className='w-full'>
                          <SelectValue placeholder='Chọn nguồn đơn' />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {sources.map(source => (
                          <SelectItem key={source.id} value={source.id}>
                            {source.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className='grid grid-cols-[120px_1fr] items-center gap-4'>
              {/* Số tiền */}
              <FormLabel className='text-sm font-medium text-gray-600'>Số tiền</FormLabel>
              <FormField
                control={form.control}
                name='amount'
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <Input
                        type='number'
                        placeholder='0'
                        {...field}
                        onChange={e => field.onChange(Number(e.target.value))}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <DialogFooter className='pt-4'>
              <DialogClose asChild>
                <Button type='button' variant='outline'>
                  Hủy
                </Button>
              </DialogClose>
              <Button type='submit'>Xác nhận</Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
