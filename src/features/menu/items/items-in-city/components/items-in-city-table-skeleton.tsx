import { Skeleton } from '@/components/ui/skeleton'

export function ItemsInCityTableSkeleton() {
  return (
    <div className='space-y-4'>
      {/* Toolbar skeleton */}
      <div className='flex items-center justify-between'>
        <div className='flex items-center space-x-2'>
          <Skeleton className='h-8 w-[250px]' />
          <Skeleton className='h-8 w-[100px]' />
          <Skeleton className='h-8 w-[100px]' />
          <Skeleton className='h-8 w-[100px]' />
        </div>
        <Skeleton className='h-8 w-[100px]' />
      </div>

      {/* Table skeleton */}
      <div className='rounded-md border'>
        {/* Header */}
        <div className='border-b p-4'>
          <div className='flex items-center space-x-4'>
            <Skeleton className='h-4 w-8' />
            <Skeleton className='h-4 w-20' />
            <Skeleton className='h-4 w-32' />
            <Skeleton className='h-4 w-20' />
            <Skeleton className='h-4 w-16' />
            <Skeleton className='h-4 w-24' />
            <Skeleton className='h-4 w-20' />
            <Skeleton className='h-4 w-16' />
            <Skeleton className='h-4 w-24' />
            <Skeleton className='h-4 w-20' />
            <Skeleton className='h-4 w-24' />
            <Skeleton className='h-4 w-24' />
            <Skeleton className='h-4 w-24' />
            <Skeleton className='h-4 w-16' />
          </div>
        </div>

        {/* Rows */}
        {Array.from({ length: 10 }).map((_, i) => (
          <div key={i} className='border-b p-4 last:border-b-0'>
            <div className='flex items-center space-x-4'>
              <Skeleton className='h-4 w-8' />
              <Skeleton className='h-4 w-20' />
              <Skeleton className='h-4 w-32' />
              <Skeleton className='h-4 w-20' />
              <Skeleton className='h-4 w-16' />
              <Skeleton className='h-4 w-24' />
              <Skeleton className='h-4 w-20' />
              <Skeleton className='h-4 w-16' />
              <Skeleton className='h-4 w-24' />
              <Skeleton className='h-4 w-20' />
              <Skeleton className='h-4 w-24' />
              <Skeleton className='h-4 w-24' />
              <Skeleton className='h-4 w-24' />
              <Skeleton className='h-4 w-16' />
            </div>
          </div>
        ))}
      </div>

      {/* Pagination skeleton */}
      <div className='flex items-center justify-between'>
        <Skeleton className='h-8 w-[200px]' />
        <div className='flex items-center space-x-2'>
          <Skeleton className='h-8 w-[100px]' />
          <Skeleton className='h-8 w-8' />
          <Skeleton className='h-8 w-8' />
        </div>
      </div>
    </div>
  )
}
