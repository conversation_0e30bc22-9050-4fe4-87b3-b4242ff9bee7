import type { UseFormReturn } from 'react-hook-form'

import type { ItemClass } from '@/types/item-class'
import type { CityData as City } from '@/types/item-removed'

import type { ItemType } from '@/lib/item-types-api'
import type { Unit } from '@/lib/units-api'

import { Checkbox } from '@/components/ui/checkbox'
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'

import type { FormValues } from './items-in-city-mutate'

interface Props {
  form: UseFormReturn<FormValues>
  itemTypes: ItemType[]
  itemClasses: ItemClass[]
  units: Unit[]
  cities: City[]
  imageFile?: File | null
  onImageChange: (event: React.ChangeEvent<HTMLInputElement>) => void
}

export function ItemBasicInfo({
  form,
  itemTypes,
  itemClasses,
  units,
  cities,
  imageFile,
  onImageChange
}: Props) {
  return (
    <div className='space-y-4'>
      <h3 className='text-md border-b pb-2 font-medium'>Chi tiết</h3>

      <div className='grid grid-cols-1 gap-4'>
        {/* Tên món */}
        <FormField
          control={form.control}
          name='item_name'
          render={({ field }) => (
            <FormItem>
              <FormLabel>Tên *</FormLabel>
              <FormControl>
                <Input placeholder='Nhập tên món' {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Giá */}
        <FormField
          control={form.control}
          name='ots_price'
          render={({ field }) => (
            <FormItem>
              <FormLabel>Giá *</FormLabel>
              <FormControl>
                <Input
                  type='number'
                  placeholder='0'
                  {...field}
                  onChange={e => field.onChange(Number(e.target.value))}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Upload ảnh */}
        <div className='space-y-2'>
          <FormLabel>Ảnh món ăn</FormLabel>
          <div className='flex h-32 w-full cursor-pointer flex-col items-center justify-center rounded-lg border-2 border-dashed border-gray-300 bg-gray-50 hover:bg-gray-100'>
            <div className='flex flex-col items-center justify-center pt-5 pb-6'>
              <svg
                className='mb-4 h-8 w-8 text-gray-500'
                aria-hidden='true'
                xmlns='http://www.w3.org/2000/svg'
                fill='none'
                viewBox='0 0 20 16'
              >
                <path
                  stroke='currentColor'
                  strokeLinecap='round'
                  strokeLinejoin='round'
                  strokeWidth='2'
                  d='M13 13h3a3 3 0 0 0 0-6h-.025A5.56 5.56 0 0 0 16 6.5 5.5 5.5 0 0 0 5.207 5.021C5.137 5.017 5.071 5 5 5a4 4 0 0 0 0 8h2.167M10 15V6m0 0L8 8m2-2 2 2'
                />
              </svg>
              <p className='mb-2 text-sm text-gray-500'>
                <span className='font-semibold'>Chọn ảnh/hình</span>
              </p>
            </div>
            <input type='file' className='hidden' accept='image/*' onChange={onImageChange} />
          </div>
          {imageFile && <p className='text-sm text-gray-600'>Đã chọn: {imageFile.name}</p>}
        </div>
      </div>

      {/* Mã barcode */}
      <FormField
        control={form.control}
        name='item_id_barcode'
        render={({ field }) => (
          <FormItem>
            <FormLabel>Mã barcode</FormLabel>
            <FormControl>
              <Input
                placeholder='Nếu bạn sử dụng tính năng scan QR thì POS hay tạo mã barcode'
                {...field}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      {/* Món ăn kèm */}
      <FormField
        control={form.control}
        name='is_eat_with'
        render={({ field }) => (
          <FormItem className='flex flex-row items-start space-y-0 space-x-3'>
            <FormControl>
              <Checkbox checked={field.value} onCheckedChange={field.onChange} />
            </FormControl>
            <div className='space-y-1 leading-none'>
              <FormLabel>Món ăn kèm</FormLabel>
            </div>
          </FormItem>
        )}
      />

      {/* Món nổi bật */}
      <FormField
        control={form.control}
        name='is_featured'
        render={({ field }) => (
          <FormItem className='flex flex-row items-start space-y-0 space-x-3'>
            <FormControl>
              <Checkbox checked={field.value} onCheckedChange={field.onChange} />
            </FormControl>
            <div className='space-y-1 leading-none'>
              <FormLabel>Món nổi bật</FormLabel>
            </div>
          </FormItem>
        )}
      />

      <div className='grid grid-cols-2 gap-4'>
        {/* Nhóm */}
        <FormField
          control={form.control}
          name='item_class_uid'
          render={({ field }) => (
            <FormItem>
              <FormLabel>Nhóm</FormLabel>
              <Select onValueChange={field.onChange} value={field.value}>
                <FormControl>
                  <SelectTrigger className='w-full'>
                    <SelectValue placeholder='Uncategory' />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {itemClasses.map(itemClass => (
                    <SelectItem key={itemClass.id} value={itemClass.id}>
                      {itemClass.item_class_name || itemClass.id}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Loại món */}
        <FormField
          control={form.control}
          name='item_type_uid'
          render={({ field }) => (
            <FormItem>
              <FormLabel>Loại món</FormLabel>
              <Select onValueChange={field.onChange} value={field.value}>
                <FormControl>
                  <SelectTrigger className='w-full'>
                    <SelectValue placeholder='None' />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {itemTypes.map(itemType => (
                    <SelectItem key={itemType.id} value={itemType.id}>
                      {itemType.item_type_name || itemType.id}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      <div className='grid grid-cols-1 gap-4'>
        {/* Mô tả */}
        <FormField
          control={form.control}
          name='description'
          render={({ field }) => (
            <FormItem>
              <FormLabel>Mô tả</FormLabel>
              <FormControl>
                <Textarea
                  placeholder='Nếu để trống thì tên món sẽ tự động làm mô tả món'
                  className='min-h-[80px]'
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Thành phố */}
        <FormField
          control={form.control}
          name='city_uid'
          render={({ field }) => (
            <FormItem>
              <FormLabel>Thành phố *</FormLabel>
              <Select onValueChange={field.onChange} value={field.value}>
                <FormControl>
                  <SelectTrigger className='w-full'>
                    <SelectValue placeholder='Chọn thành phố' />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {cities.map(city => (
                    <SelectItem key={city.id} value={city.id}>
                      {city.city_name || city.id}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* SKU */}
        <FormField
          control={form.control}
          name='item_id'
          render={({ field }) => (
            <FormItem>
              <FormLabel>SKU</FormLabel>
              <FormControl>
                <Input placeholder='Nhập SKU' {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      <div className='grid grid-cols-1 gap-4'>
        {/* Đơn vị tính */}
        <FormField
          control={form.control}
          name='unit_uid'
          render={({ field }) => (
            <FormItem>
              <FormLabel>Đơn vị tính *</FormLabel>
              <Select onValueChange={field.onChange} value={field.value}>
                <FormControl>
                  <SelectTrigger className='w-full'>
                    <SelectValue placeholder='Món' />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {units.map(unit => (
                    <SelectItem key={unit.id} value={unit.id}>
                      {unit.unit_name || unit.id}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Đơn vị tính thứ 2 */}
        <FormField
          control={form.control}
          name='unit_secondary_uid'
          render={({ field }) => (
            <FormItem>
              <FormLabel>Đơn vị tính thứ 2</FormLabel>
              <Select onValueChange={field.onChange} value={field.value}>
                <FormControl>
                  <SelectTrigger className='w-full'>
                    <SelectValue placeholder='None' />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {units.map(unit => (
                    <SelectItem key={unit.id} value={unit.id}>
                      {unit.unit_name || unit.id}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      <div className='grid grid-cols-1 gap-4'>
        {/* VAT */}
        <FormField
          control={form.control}
          name='ots_tax'
          render={({ field }) => (
            <FormItem>
              <FormLabel>VAT món ăn</FormLabel>
              <div className='flex items-center space-x-2'>
                <FormControl>
                  <Input
                    type='number'
                    placeholder='0'
                    className='w-full'
                    {...field}
                    onChange={e => field.onChange(Number(e.target.value))}
                  />
                </FormControl>
                <span>%</span>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Thời gian chế biến */}
        <FormField
          control={form.control}
          name='time_cooking'
          render={({ field }) => (
            <FormItem>
              <FormLabel>Thời gian chế biến (phút)</FormLabel>
              <FormControl>
                <Input
                  type='number'
                  placeholder='0'
                  {...field}
                  onChange={e => field.onChange(Number(e.target.value))}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>
    </div>
  )
}
