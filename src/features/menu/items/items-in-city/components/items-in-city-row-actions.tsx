import { DotsHorizontalIcon } from '@radix-ui/react-icons'

import { Row } from '@tanstack/react-table'

import { IconTrash, IconEdit } from '@tabler/icons-react'

import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuShortcut
} from '@/components/ui/dropdown-menu'

import { useItemsInCity } from '../context'
import { ItemsInCity } from '../data'

interface ItemsInCityRowActionsProps {
  row: Row<ItemsInCity>
}

export function ItemsInCityRowActions({ row }: ItemsInCityRowActionsProps) {
  const itemsInCity = row.original
  const { setOpen, setCurrentRow } = useItemsInCity()

  const handleEdit = () => {
    setCurrentRow(itemsInCity)
    setOpen('update')
  }

  const handleDelete = () => {
    setCurrentRow(itemsInCity)
    setOpen('delete')
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant='ghost' className='data-[state=open]:bg-muted flex h-8 w-8 p-0'>
          <DotsHorizontalIcon className='h-4 w-4' />
          <span className='sr-only'>Mở menu</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align='end' className='w-[160px]'>
        <DropdownMenuItem onClick={handleEdit}>
          Chỉnh sửa
          <DropdownMenuShortcut>
            <IconEdit size={16} />
          </DropdownMenuShortcut>
        </DropdownMenuItem>
        <DropdownMenuItem onClick={handleDelete} className='text-red-500 focus:text-red-500'>
          Xóa
          <DropdownMenuShortcut>
            <IconTrash size={16} className='text-red-500' />
          </DropdownMenuShortcut>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
