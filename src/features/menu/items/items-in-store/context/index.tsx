import React, { useState } from 'react'

import useDialogState from '@/hooks/use-dialog-state'

import { ItemsInStore } from '../data'

type ItemsInStoreDialogType = 'create' | 'copy' | 'update' | 'delete' | 'export' | 'import'

interface ItemsInStoreContextType {
  open: ItemsInStoreDialogType | null
  setOpen: (str: ItemsInStoreDialogType | null) => void
  currentRow: ItemsInStore | null
  setCurrentRow: React.Dispatch<React.SetStateAction<ItemsInStore | null>>
}

const ItemsInStoreContext = React.createContext<ItemsInStoreContextType | null>(null)

interface Props {
  children: React.ReactNode
}

export default function ItemsInStoreProvider({ children }: Props) {
  const [open, setOpen] = useDialogState<ItemsInStoreDialogType>(null)
  const [currentRow, setCurrentRow] = useState<ItemsInStore | null>(null)

  return (
    <ItemsInStoreContext
      value={{
        open,
        setOpen,
        currentRow,
        setCurrentRow
      }}
    >
      {children}
    </ItemsInStoreContext>
  )
}

export const useItemsInStore = () => {
  const itemsInStoreContext = React.useContext(ItemsInStoreContext)

  if (!itemsInStoreContext) {
    throw new Error('useItemsInStore has to be used within <ItemsInStoreContext>')
  }

  return itemsInStoreContext
}
