import { z } from 'zod'

export const itemsInStoreSchema = z.object({
  id: z.string(),
  item_id: z.string(),
  item_name: z.string(),
  description: z.string(),
  ots_price: z.number(),
  ots_tax: z.number(),
  ta_price: z.number(),
  ta_tax: z.number(),
  time_sale_hour_day: z.number(),
  time_sale_date_week: z.number(),
  allow_take_away: z.number(),
  is_eat_with: z.number(),
  image_path: z.string(),
  image_path_thumb: z.string(),
  item_color: z.string(),
  list_order: z.number(),
  is_service: z.number(),
  is_material: z.number(),
  active: z.number(),
  user_id: z.string(),
  is_foreign: z.number(),
  quantity_default: z.number(),
  price_change: z.number(),
  currency_type_id: z.string(),
  point: z.number(),
  is_gift: z.number(),
  is_fc: z.number(),
  show_on_web: z.number(),
  show_price_on_web: z.number(),
  cost_price: z.number(),
  is_print_label: z.number(),
  quantity_limit: z.number(),
  is_kit: z.number(),
  time_cooking: z.number(),
  item_id_barcode: z.string(),
  process_index: z.number(),
  is_allow_discount: z.number(),
  quantity_per_day: z.number(),
  item_id_eat_with: z.string(),
  is_parent: z.number(),
  is_sub: z.number(),
  item_id_mapping: z.string(),
  effective_date: z.number(),
  expire_date: z.number(),
  sort: z.number(),
  extra_data: z.object({
    is_buffet_item: z.number(),
    up_size_buffet: z.array(z.unknown()),
    is_item_service: z.number(),
    is_virtual_item: z.number(),
    price_by_source: z.array(z.unknown()),
    enable_edit_price: z.number(),
    exclude_items_buffet: z.array(z.unknown()),
    no_update_quantity_toping: z.number()
  }),
  revision: z.number(),
  unit_uid: z.string(),
  unit_secondary_uid: z.string().nullable(),
  item_type_uid: z.string(),
  item_class_uid: z.string(),
  source_uid: z.string().nullable(),
  brand_uid: z.string(),
  company_uid: z.string(),
  customization_uid: z.string(),
  is_fabi: z.number(),
  deleted: z.boolean(),
  created_by: z.string(),
  updated_by: z.string(),
  deleted_by: z.string().nullable(),
  created_at: z.number(),
  updated_at: z.number(),
  deleted_at: z.number().nullable(),
  apply_with_store: z.number(),
  store_uid: z.string(),
  city_uid: z.string()
})

export type ItemsInStore = z.infer<typeof itemsInStoreSchema>
