import { useQuery } from '@tanstack/react-query'

import { useAuthStore } from '@/stores/authStore'

import { api } from '@/lib/api'

import { QUERY_KEYS } from '@/constants/query-keys'

import type { ItemsInStore } from '../data/schema'

interface UseItemsInStoreDataOptions {
  params?: Record<string, string>
  enabled?: boolean
}

export const useItemsInStoreData = (options: UseItemsInStoreDataOptions = {}) => {
  const { params = {}, enabled = true } = options
  const { company, brands } = useAuthStore(state => state.auth)

  const selectedBrand = brands?.[0]

  const dynamicParams = {
    company_uid: company?.id || '',
    brand_uid: selectedBrand?.id || '',
    page: 1,
    reverse: 1,
    ...params
  }

  const finalParams = { ...dynamicParams }

  const hasRequiredAuth = !!(company?.id && selectedBrand?.id)

  return useQuery({
    queryKey: [QUERY_KEYS.ITEMS_IN_STORE_LIST, JSON.stringify(finalParams), JSON.stringify(params)],
    queryFn: async (): Promise<ItemsInStore[]> => {
      const queryParams = new URLSearchParams()

      queryParams.append('company_uid', finalParams.company_uid)
      queryParams.append('brand_uid', finalParams.brand_uid)
      queryParams.append('page', finalParams.page.toString())
      queryParams.append('reverse', finalParams.reverse.toString())

      if (params.apply_with_store) {
        queryParams.append('apply_with_store', params.apply_with_store)
      }

      Object.entries(params).forEach(([key, value]) => {
        if (value && value !== 'all') {
          if (key === 'item_type_uid') {
            queryParams.append('item_type_uid', value)
          } else if (key === 'store_uid') {
            queryParams.append('store_uid', value)
          } else if (key === 'time_sale_date_week') {
            queryParams.append('time_sale_date_week', value)
          } else if (key === 'active') {
            queryParams.append('active', value)
          }
        }
      })

      const response = await api.get(`/mdata/v1/items?${queryParams.toString()}`)

      if (!response.data || typeof response.data !== 'object') {
        throw new Error('Invalid response format from items API')
      }

      return (response.data.data as ItemsInStore[]) || []
    },
    enabled: enabled && hasRequiredAuth,
    staleTime: 5 * 60 * 1000,
    refetchInterval: 10 * 60 * 1000
  })
}

export type { ItemsInStore }
