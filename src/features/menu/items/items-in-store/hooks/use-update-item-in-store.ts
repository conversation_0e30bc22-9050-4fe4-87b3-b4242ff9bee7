import { useMutation, useQueryClient } from '@tanstack/react-query'

import { toast } from 'sonner'

import { api } from '@/lib/api'

import { QUERY_KEYS } from '@/constants/query-keys'

import type { ItemsInStore } from '../data'

interface UpdateItemInStoreRequest {
  city_uid: string
  ta_price: number
  ots_tax: number
  ta_tax: number
  store_uid: string
  unit_uid: string
  extra_data: {
    is_buffet_item: number
    up_size_buffet: unknown[]
    is_item_service: number
    is_virtual_item: number
    price_by_source: unknown[]
    enable_edit_price: number
    exclude_items_buffet: unknown[]
    no_update_quantity_toping: number
  }
  item_name: string
  description: string
  ots_price: number
  time_sale_hour_day: number
  time_sale_date_week: number
  allow_take_away: number
  is_eat_with: number
  image_path: string
  image_path_thumb: string
  item_color: string
  list_order: number
  is_service: number
  is_material: number
  is_foreign: number
  quantity_default: number
  price_change: number
  currency_type_id: string
  point: number
  is_gift: number
  is_fc: number
  show_on_web: number
  show_price_on_web: number
  cost_price: number
  is_print_label: number
  quantity_limit: number
  is_kit: number
  time_cooking: number
  item_id_barcode: string
  process_index: number
  is_allow_discount: number
  quantity_per_day: number
  item_id_eat_with: string
  is_parent: number
  is_sub: number
  item_id_mapping: string
  effective_date: number
  expire_date: number
  sort: number
  revision: number
  unit_secondary_uid: string | null
  item_type_uid: string
  item_class_uid: string
  source_uid: string | null
  brand_uid: string
  company_uid: string
  customization_uid: string
  is_fabi: number
  apply_with_store: number
  item_id?: string
}

export const useUpdateItemInStore = () => {
  const queryClient = useQueryClient()

  const { mutate, isPending } = useMutation({
    mutationFn: async (data: UpdateItemInStoreRequest) => {
      // Use POST method - API will create new item with new item_id
      const response = await api.post('/mdata/v1/item', data, {
        headers: {
          'Content-Type': 'application/json',
          'accept-language': 'vi',
          fabi_type: 'pos-cms',
          'x-client-timezone': '25200000'
        },
        timeout: 30000
      })

      return response.data.data || response.data
    },
    onSuccess: () => {
      // Just invalidate without refetching to preserve pagination
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.ITEMS_IN_STORE_LIST],
        refetchType: 'none'
      })

      // Manually refetch in background to update data
      setTimeout(() => {
        queryClient.refetchQueries({
          queryKey: [QUERY_KEYS.ITEMS_IN_STORE_LIST]
        })
      }, 100)

      toast.success('Cập nhật món ăn thành công!')
    },
    onError: (error: Error) => {
      toast.error(`Lỗi khi cập nhật món ăn: ${error.message}`)
    }
  })

  return { updateItemInStore: mutate, isUpdating: isPending }
}

interface UpdateItemCustomizationRequest {
  menuItem: ItemsInStore
  customization_uid: string | null
}

export const useUpdateItemCustomization = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (data: UpdateItemCustomizationRequest) => {
      // Prepare the update data with all current fields but updated customization_uid
      const updateData = {
        id: data.menuItem.id,
        item_id: data.menuItem.item_id,
        item_name: data.menuItem.item_name,
        description: data.menuItem.description,
        ots_price: data.menuItem.ots_price,
        ots_tax: data.menuItem.ots_tax,
        ta_price: data.menuItem.ta_price,
        ta_tax: data.menuItem.ta_tax,
        time_sale_hour_day: data.menuItem.time_sale_hour_day,
        time_sale_date_week: data.menuItem.time_sale_date_week,
        allow_take_away: data.menuItem.allow_take_away,
        is_eat_with: data.menuItem.is_eat_with,
        image_path: data.menuItem.image_path,
        image_path_thumb: data.menuItem.image_path_thumb,
        item_color: data.menuItem.item_color,
        list_order: data.menuItem.list_order,
        is_service: data.menuItem.is_service,
        is_material: data.menuItem.is_material,
        active: data.menuItem.active,
        user_id: data.menuItem.user_id,
        is_foreign: data.menuItem.is_foreign,
        quantity_default: data.menuItem.quantity_default,
        price_change: data.menuItem.price_change,
        currency_type_id: data.menuItem.currency_type_id,
        point: data.menuItem.point,
        is_gift: data.menuItem.is_gift,
        is_fc: data.menuItem.is_fc,
        show_on_web: data.menuItem.show_on_web,
        show_price_on_web: data.menuItem.show_price_on_web,
        cost_price: data.menuItem.cost_price,
        is_print_label: data.menuItem.is_print_label,
        quantity_limit: data.menuItem.quantity_limit,
        is_kit: data.menuItem.is_kit,
        time_cooking: data.menuItem.time_cooking,
        item_id_barcode: data.menuItem.item_id_barcode,
        process_index: data.menuItem.process_index,
        is_allow_discount: data.menuItem.is_allow_discount,
        quantity_per_day: data.menuItem.quantity_per_day,
        item_id_eat_with: data.menuItem.item_id_eat_with,
        is_parent: data.menuItem.is_parent,
        is_sub: data.menuItem.is_sub,
        item_id_mapping: data.menuItem.item_id_mapping,
        effective_date: data.menuItem.effective_date,
        expire_date: data.menuItem.expire_date,
        sort: data.menuItem.sort,
        extra_data: data.menuItem.extra_data,
        revision: data.menuItem.revision,
        unit_uid: data.menuItem.unit_uid,
        unit_secondary_uid: data.menuItem.unit_secondary_uid,
        item_type_uid: data.menuItem.item_type_uid,
        item_class_uid: data.menuItem.item_class_uid,
        source_uid: data.menuItem.source_uid,
        brand_uid: data.menuItem.brand_uid,
        company_uid: data.menuItem.company_uid,
        customization_uid: data.customization_uid, // Only this field changes
        is_fabi: data.menuItem.is_fabi,
        deleted: data.menuItem.deleted,
        created_by: data.menuItem.created_by,
        updated_by: data.menuItem.updated_by,
        deleted_by: data.menuItem.deleted_by,
        created_at: data.menuItem.created_at,
        updated_at: data.menuItem.updated_at,
        deleted_at: data.menuItem.deleted_at,
        apply_with_store: data.menuItem.apply_with_store,
        store_uid: data.menuItem.store_uid,
        city_uid: data.menuItem.city_uid
      }

      const response = await api.put('/mdata/v1/item', updateData, {
        headers: {
          'Content-Type': 'application/json;charset=UTF-8',
          'accept-language': 'vi',
          fabi_type: 'pos-cms',
          'x-client-timezone': '25200000'
        },
        timeout: 30000
      })

      return response.data.data || response.data
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.ITEMS_IN_STORE_LIST]
      })
      toast.success('Cập nhật customization thành công!')
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Có lỗi xảy ra khi cập nhật customization')
    }
  })
}
