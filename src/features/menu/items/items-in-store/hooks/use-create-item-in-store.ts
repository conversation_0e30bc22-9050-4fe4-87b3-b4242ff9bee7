import { useMutation, useQueryClient } from '@tanstack/react-query'

import { toast } from 'sonner'

import { api } from '@/lib/api'

import { QUERY_KEYS } from '@/constants/query-keys'

interface CreateItemInStoreRequest {
  city_uid?: string
  ta_price: number
  ots_tax: number
  ta_tax: number
  store_uid: string
  unit_uid: string
  extra_data: {
    price_by_source: unknown[]
    is_virtual_item: number
    is_item_service: number
    no_update_quantity_toping: number
    enable_edit_price: number
    is_buffet_item: number
    exclude_items_buffet: unknown[]
    up_size_buffet: unknown[]
  }
  item_type_uid: string
  item_name: string
  company_uid: string
  brand_uid: string
  ots_price: number
  sort: number
  item_id: string
  apply_with_store: number
}

export const useCreateItemInStore = () => {
  const queryClient = useQueryClient()

  const { mutate, isPending } = useMutation({
    mutationFn: async (data: CreateItemInStoreRequest) => {
      // Use the exact payload structure from the component
      const payload = data

      const response = await api.post('/mdata/v1/item', payload, {
        headers: {
          'Content-Type': 'application/json',
          'accept-language': 'vi',
          fabi_type: 'pos-cms',
          'x-client-timezone': '25200000'
        },
        timeout: 30000
      })

      return response.data.data || response.data
    },
    onSuccess: () => {
      // Just invalidate without refetching to preserve pagination
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.ITEMS_IN_STORE_LIST],
        refetchType: 'none'
      })

      // Manually refetch in background to update data
      setTimeout(() => {
        queryClient.refetchQueries({
          queryKey: [QUERY_KEYS.ITEMS_IN_STORE_LIST]
        })
      }, 100)

      toast.success('Tạo món ăn thành công!')
    },
    onError: (error: Error) => {
      toast.error(`Lỗi khi tạo món ăn: ${error.message}`)
    }
  })

  return { createItemInStore: mutate, isCreating: isPending }
}

export type { CreateItemInStoreRequest }
