import { useState, useMemo, useEffect } from 'react'

import { useCustomizationsData, useStoresData } from '@/hooks/api'

import { Header } from '@/components/layout/header'
import { Main } from '@/components/layout/main'
import { ProfileDropdown } from '@/components/profile-dropdown'
import { Search } from '@/components/search'
import { ThemeSwitch } from '@/components/theme-switch'

import {
  columns,
  ItemsInStoreButtons,
  ItemsInStoreTableSkeleton,
  ItemsInStoreDialogs,
  ItemsInStoreDataTable,
  CustomizationDialog
} from './components'
import ItemsInStoreProvider, { useItemsInStore } from './context'
import { ItemsInStore } from './data'
import { useItemsInStoreForTable } from './hooks'

function ItemsInStoreContent() {
  const { setOpen, setCurrentRow } = useItemsInStore()
  const [isCustomizationDialogOpen, setIsCustomizationDialogOpen] = useState(false)
  const [selectedMenuItem, setSelectedMenuItem] = useState<ItemsInStore | null>(null)
  const [selectedItemTypeUid, setSelectedItemTypeUid] = useState<string>('all')
  const [selectedStoreUid, setSelectedStoreUid] = useState<string>('all')
  const [selectedStatus, setSelectedStatus] = useState<string>('store')
  const [selectedDaysOfWeek, setSelectedDaysOfWeek] = useState<string[]>([])
  const [selectedItemStatus, setSelectedItemStatus] = useState<string>('all')

  const filterParams = useMemo(() => {
    const params: Record<string, string> = {}
    if (selectedItemTypeUid !== 'all') {
      params.item_type_uid = selectedItemTypeUid
    }
    if (selectedStoreUid !== 'all') {
      params.store_uid = selectedStoreUid
    }

    if (selectedItemStatus === 'active') {
      params.active = '1'
    } else if (selectedItemStatus === 'inactive') {
      params.active = '0'
    } else if (selectedItemStatus === 'new') {
      params.apply_with_store = '2'
    } else if (selectedItemStatus === 'modified') {
      params.apply_with_store = '1'
    }

    if (
      selectedItemStatus === 'active' ||
      selectedItemStatus === 'all' ||
      selectedItemStatus === 'inactive'
    ) {
      if (selectedStatus === 'store') {
        params.apply_with_store = '-1'
      } else if (selectedStatus === 'store_city') {
        params.apply_with_store = '1'
      }
    }

    if (selectedDaysOfWeek.length > 0) {
      params.time_sale_date_week = selectedDaysOfWeek.join(',')
    }
    return params
  }, [
    selectedItemTypeUid,
    selectedStoreUid,
    selectedStatus,
    selectedDaysOfWeek,
    selectedItemStatus
  ])

  const {
    data: menuItems = [],
    isLoading: itemsLoading,
    error: itemsError
  } = useItemsInStoreForTable({
    params: filterParams
  })
  const storeUid = selectedStoreUid !== 'all' ? selectedStoreUid : undefined

  const { data: customizations = [] } = useCustomizationsData({
    skip_limit: 'true',
    ...(storeUid ? { store_uid: storeUid } : {})
  })
  const { data: storesData = [] } = useStoresData()

  const isLoading = itemsLoading
  const error = itemsError

  useEffect(() => {
    if (storesData.length > 0 && selectedStoreUid === 'all') {
      const firstActiveStore = storesData.find(store => store.isActive)
      if (firstActiveStore) {
        setSelectedStoreUid(firstActiveStore.id)
      }
    }
  }, [storesData, selectedStoreUid])

  const handleCustomizationClick = (menuItem: ItemsInStore) => {
    try {
      setSelectedMenuItem(menuItem)
      setIsCustomizationDialogOpen(true)
    } catch (_error) {
      // Error
    }
  }

  const handleCopyClick = (menuItem: ItemsInStore) => {
    try {
      setCurrentRow(menuItem)
      setOpen('copy')
    } catch (_error) {
      // Error
    }
  }

  if (error) {
    return (
      <div className='flex items-center justify-center p-8'>
        <div className='text-center'>
          <p className='text-muted-foreground mb-2 text-sm'>Có lỗi xảy ra khi tải dữ liệu</p>
          <p className='text-muted-foreground text-xs'>
            {itemsError && `Món ăn: ${itemsError?.message || 'Lỗi không xác định'}`}
          </p>
          <button
            onClick={() => window.location.reload()}
            className='mt-4 rounded bg-blue-500 px-4 py-2 text-white hover:bg-blue-600'
          >
            Tải lại trang
          </button>
        </div>
      </div>
    )
  }

  return (
    <>
      <Header>
        <div className='ml-auto flex items-center space-x-4'>
          <Search />
          <ThemeSwitch />
          <ProfileDropdown />
        </div>
      </Header>

      <Main>
        <div className='mb-2 flex flex-wrap items-center justify-between space-y-2 gap-x-4'>
          <div>
            <h2 className='text-2xl font-bold tracking-tight'>Món ăn tại cửa hàng</h2>
            <p className='text-muted-foreground'>
              Quản lý thông tin món ăn, giá cả và cấu hình theo từng cửa hàng
            </p>
          </div>
          <ItemsInStoreButtons />
        </div>
        <div className='-mx-4 flex-1 overflow-auto px-4 py-1 lg:flex-row lg:space-y-0 lg:space-x-12'>
          {isLoading && <ItemsInStoreTableSkeleton />}
          {!isLoading && (
            <ItemsInStoreDataTable
              columns={columns}
              data={menuItems}
              onCustomizationClick={handleCustomizationClick}
              onCopyClick={handleCopyClick}
              customizations={customizations}
              selectedItemTypeUid={selectedItemTypeUid}
              onItemTypeChange={setSelectedItemTypeUid}
              selectedStoreUid={selectedStoreUid}
              onStoreChange={setSelectedStoreUid}
              selectedStatus={selectedStatus}
              onStatusChange={setSelectedStatus}
              selectedDaysOfWeek={selectedDaysOfWeek}
              onDaysOfWeekChange={setSelectedDaysOfWeek}
              selectedItemStatus={selectedItemStatus}
              onItemStatusChange={setSelectedItemStatus}
            />
          )}
        </div>
      </Main>

      <ItemsInStoreDialogs storeUid={storeUid || ''} />

      {isCustomizationDialogOpen && (
        <CustomizationDialog
          open={isCustomizationDialogOpen}
          onOpenChange={setIsCustomizationDialogOpen}
          menuItem={selectedMenuItem}
          customizations={customizations}
        />
      )}
    </>
  )
}

export default function ItemsInStorePage() {
  return (
    <ItemsInStoreProvider>
      <ItemsInStoreContent />
    </ItemsInStoreProvider>
  )
}
