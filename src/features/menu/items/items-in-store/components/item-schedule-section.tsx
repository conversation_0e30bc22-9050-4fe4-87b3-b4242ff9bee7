import type { UseFormReturn } from 'react-hook-form'

import { <PERSON><PERSON> } from '@/components/ui/button'
import { Checkbox } from '@/components/ui/checkbox'
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { Input } from '@/components/ui/input'

import type { FormValues } from './items-in-store-mutate'

interface Props {
  form: UseFormReturn<FormValues>
  setOpenDialog: (open: boolean) => void
}

const daysOfWeek = [
  { label: 'T2', value: '2' },
  { label: 'T3', value: '3' },
  { label: 'T4', value: '4' },
  { label: 'T5', value: '5' },
  { label: 'T6', value: '6' },
  { label: 'T7', value: '7' },
  { label: 'CN', value: '1' }
]

const hoursOfDay = [
  '00:00',
  '01:00',
  '02:00',
  '03:00',
  '04:00',
  '05:00',
  '06:00',
  '07:00',
  '08:00',
  '09:00',
  '10:00',
  '11:00',
  '12:00',
  '13:00',
  '14:00',
  '15:00',
  '16:00',
  '17:00',
  '18:00',
  '19:00',
  '20:00',
  '21:00',
  '22:00',
  '23:00'
]

export function ItemScheduleSection({ form, setOpenDialog }: Props) {
  return (
    <>
      {/* Cấu hình giá theo nguồn */}
      <div className='space-y-4'>
        <h3 className='border-b pb-2 text-lg font-medium'>Cấu hình giá theo nguồn</h3>
        <Button type='button' variant='outline' size='sm' onClick={() => setOpenDialog(true)}>
          Thêm nguồn
        </Button>
      </div>

      {/* Khung thời gian bán */}
      <div className='space-y-4'>
        <h3 className='border-b pb-2 text-lg font-medium'>Khung thời gian bán</h3>

        {/* Chọn ngày */}
        <div>
          <FormLabel className='text-sm font-medium'>Chọn ngày</FormLabel>
          <div className='mt-2 grid grid-cols-7 gap-2'>
            {daysOfWeek.map(day => (
              <FormField
                key={day.value}
                control={form.control}
                name='selectedDays'
                render={({ field }) => (
                  <FormItem className='flex flex-row items-center space-y-0 space-x-2'>
                    <FormControl>
                      <Checkbox
                        checked={field.value?.includes(day.value)}
                        onCheckedChange={checked => {
                          const updatedDays = checked
                            ? [...(field.value || []), day.value]
                            : (field.value || []).filter(d => d !== day.value)
                          field.onChange(updatedDays)
                        }}
                      />
                    </FormControl>
                    <FormLabel className='text-sm font-normal'>{day.label}</FormLabel>
                  </FormItem>
                )}
              />
            ))}
          </div>
        </div>

        {/* Chọn giờ */}
        <div>
          <FormLabel className='text-sm font-medium'>Chọn giờ</FormLabel>
          <div className='mt-2 grid grid-cols-7 gap-2'>
            {hoursOfDay.map(hour => (
              <FormField
                key={hour}
                control={form.control}
                name='selectedHours'
                render={({ field }) => (
                  <FormItem className='flex flex-row items-center space-y-0 space-x-2'>
                    <FormControl>
                      <Checkbox
                        checked={field.value?.includes(hour)}
                        onCheckedChange={checked => {
                          const updatedHours = checked
                            ? [...(field.value || []), hour]
                            : (field.value || []).filter(h => h !== hour)
                          field.onChange(updatedHours)
                        }}
                      />
                    </FormControl>
                    <FormLabel className='text-sm font-normal'>{hour}</FormLabel>
                  </FormItem>
                )}
              />
            ))}
          </div>
        </div>

        {/* Thứ tự hiển thị */}
        <div className='grid grid-cols-1 gap-4'>
          <FormField
            control={form.control}
            name='sort'
            render={({ field }) => (
              <FormItem>
                <FormLabel>Thứ tự hiển thị</FormLabel>
                <FormControl>
                  <Input
                    type='number'
                    placeholder='Nhập số thứ tự hiển thị'
                    {...field}
                    onChange={e => field.onChange(Number(e.target.value))}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
      </div>
    </>
  )
}
