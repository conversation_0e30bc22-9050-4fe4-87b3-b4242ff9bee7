import { useState } from 'react'

import { Download, Upload } from 'lucide-react'
import { toast } from 'sonner'
import * as XLSX from 'xlsx'

import { Button } from '@/components/ui/button'
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'

import { ImportPreviewDialog } from './excel-preview-dialog'
import type { ImportItem } from './excel-preview-dialog'

interface ImportDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function ImportDialog({ open, onOpenChange }: ImportDialogProps) {
  const [isImportPreviewOpen, setIsImportPreviewOpen] = useState(false)
  const [importData, setImportData] = useState<ImportItem[]>([])

  const handleDownloadTemplate = () => {
    const templateData = [
      {
        ID: '',
        'Mã món': '',
        'Thành phố': '',
        '<PERSON><PERSON><PERSON> hàng': '',
        Tên: '',
        Gi<PERSON>: '',
        'Trạng thái': '',
        'Mã barcode': '',
        'Món ăn kèm': '',
        'Không cập nhật số lượng món ăn kèm': '',
        'Đơn vị': '',
        Nhóm: '',
        'Tên nhóm': '',
        'Loại món': '',
        'Tên loại': '',
        'Mô tả': '',
        SKU: '',
        'VAT (%)': '',
        'Thời gian chế biến (phút)': '',
        'Cho phép sửa giá khi bán': '',
        'Cấu hình món ảo': '',
        'Cấu hình món dịch vụ': '',
        'Cấu hình món ăn là vé buffet': '',
        Giờ: '',
        Ngày: '',
        'Thứ tự': '',
        'Hình ảnh': '',
        'Công thức inQR cho máy pha trà': ''
      }
    ]

    const worksheet = XLSX.utils.json_to_sheet(templateData)
    const workbook = XLSX.utils.book_new()
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Template')

    const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-')
    const filename = `items_import_template_${timestamp}.xlsx`

    XLSX.writeFile(workbook, filename)

    toast.success(`Đã tải xuống file mẫu ${filename}`)
  }

  const handleImportFile = () => {
    const input = document.createElement('input')
    input.type = 'file'
    input.accept = '.xlsx,.xls'
    input.onchange = async e => {
      const file = (e.target as HTMLInputElement).files?.[0]
      if (!file) return

      try {
        const arrayBuffer = await file.arrayBuffer()
        const workbook = XLSX.read(arrayBuffer, { type: 'array' })
        const worksheet = workbook.Sheets[workbook.SheetNames[0]]
        const jsonData = XLSX.utils.sheet_to_json(worksheet)

        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const transformedData = jsonData.map((row: any) => ({
          id: row.ID || '',
          item_id: row['Mã món'] || '',
          city_name: row['Thành phố'] || '',
          store_name: row['Cửa hàng'] || '',
          item_name: row['Tên'] || '',
          ots_price: row['Giá'] || 0,
          active: row['Trạng thái'] === 'Hoạt động' ? 1 : 0,
          item_id_barcode: row['Mã barcode'] || '',
          is_eat_with: row['Món ăn kèm'] === 'Có' ? 1 : 0,
          no_update_quantity_toping: row['Không cập nhật số lượng món ăn kèm'] === 'Có' ? 1 : 0,
          unit_name: row['Đơn vị'] || '',
          item_type_id: row['Nhóm'] || '',
          item_type_name: row['Tên nhóm'] || '',
          item_class_id: row['Loại món'] || '',
          item_class_name: row['Tên loại'] || '',
          description: row['Mô tả'] || '',
          sku: row['SKU'] || '',
          ots_tax: row['VAT (%)'] || 0,
          time_cooking: row['Thời gian chế biến (phút)'] || 0,
          price_change: row['Cho phép sửa giá khi bán'] === 'Có' ? 1 : 0,
          is_virtual_item: row['Cấu hình món ảo'] === 'Có' ? 1 : 0,
          is_item_service: row['Cấu hình món dịch vụ'] === 'Có' ? 1 : 0,
          is_buffet_item: row['Cấu hình món ăn là vé buffet'] === 'Có' ? 1 : 0,
          time_sale_hour_day: row['Giờ'] || 0,
          time_sale_date_week: row['Ngày'] || 0,
          list_order: row['Thứ tự'] || 0,
          image_path: row['Hình ảnh'] || '',
          inqr_formula: row['Công thức inQR cho máy pha trà'] || '',
          // Store all original data for reference
          originalData: row
        }))

        setImportData(transformedData)
        setIsImportPreviewOpen(true)
        onOpenChange(false)
      } catch (_error) {
        toast.error('Có lỗi xảy ra khi đọc file Excel')
      }
    }
    input.click()
  }

  return (
    <>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className='max-w-2xl'>
          <DialogHeader className='flex flex-row items-center justify-between space-y-0 pb-4'>
            <DialogTitle className='text-xl font-semibold'>Thêm món</DialogTitle>
          </DialogHeader>

          <div className='space-y-6'>
            {/* Bước 1 */}
            <div className='rounded-lg bg-gray-50 p-4'>
              <div className='flex items-center justify-between'>
                <h3 className='text-lg font-medium'>Bước 1. Tải file mẫu</h3>
                <Button
                  variant='outline'
                  onClick={handleDownloadTemplate}
                  className='flex items-center gap-2'
                >
                  <Download className='h-4 w-4' />
                  Tải xuống
                </Button>
              </div>
            </div>

            {/* Bước 2 */}
            <div className='space-y-4'>
              <div className='rounded-lg bg-gray-50 p-4'>
                <h3 className='text-lg font-medium'>Bước 2. Thêm món vào file</h3>

                <div className='space-y-3 text-sm text-gray-600'>
                  <p>
                    <strong>Các món được thêm vào chỉ được áp dụng tại: Tutimi-Bình Lợi</strong>
                  </p>

                  <p>
                    Không được để trống các cột{' '}
                    <span className='font-medium text-blue-600'>Tên</span>.
                  </p>

                  <p>
                    Các cột còn lại có thể để trống, để gán nhóm, loại, đơn vị cho món: Nhập mã
                    nhóm, mã loại, mã đơn vị đã có vào cột{' '}
                    <span className='font-medium text-blue-600'>Nhóm</span>,{' '}
                    <span className='font-medium text-blue-600'>Loại món</span>.
                  </p>

                  <p>
                    Mã đơn vị món có thể xem trong sheet{' '}
                    <span className='font-medium text-blue-600'>Guide</span> của file mẫu.
                  </p>
                </div>
              </div>
            </div>

            {/* Bước 3 */}
            <div className='space-y-4'>
              <div className='rounded-lg bg-gray-50 p-4'>
                <h3 className='text-lg font-medium'>Bước 3. Tải file thực đơn lên</h3>

                <div className='flex items-center justify-between'>
                  <p className='text-sm text-gray-600'>
                    Sau khi đã điền đầy đủ thực đơn bạn có thể tải file lên
                  </p>
                  <Button onClick={handleImportFile} className='flex items-center gap-2'>
                    <Upload className='h-4 w-4' />
                    Tải file lên
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      <ImportPreviewDialog
        open={isImportPreviewOpen}
        onOpenChange={setIsImportPreviewOpen}
        data={importData}
      />
    </>
  )
}
