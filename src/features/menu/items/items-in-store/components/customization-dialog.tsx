'use client'

import { useEffect, useMemo } from 'react'

import { z } from 'zod'

import { useForm } from 'react-hook-form'

import { zodResolver } from '@hookform/resolvers/zod'

import type { Customization } from '@/types/customizations'
import { toast } from 'sonner'

import { useAuthStore } from '@/stores/authStore'

import { Button } from '@/components/ui/button'
import {
  <PERSON>alog,
  DialogClose,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select'

import { ItemsInStore } from '../data'
import { useUpdateItemCustomization } from '../hooks'

const customizationFormSchema = z.object({
  customization_uid: z.string().min(1, 'Vui lòng chọn customization')
})

type CustomizationFormValues = z.infer<typeof customizationFormSchema>

interface CustomizationDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  menuItem: ItemsInStore | null
  customizations: Customization[]
}

export function CustomizationDialog({
  menuItem,
  customizations,
  open,
  onOpenChange
}: CustomizationDialogProps) {
  const updateItemCustomization = useUpdateItemCustomization()
  const { company, brands } = useAuthStore(state => state.auth)
  const selectedBrand = brands?.[0]

  const currentCustomizationUid = useMemo(() => {
    return menuItem?.customization_uid || null
  }, [menuItem?.customization_uid])

  const customizationOptions = useMemo(() => {
    if (!customizations.length) return []

    const options = customizations.map(customization => ({
      id: customization.id,
      name: customization.name || customization.id,
      value: customization.id
    }))

    return [...options]
  }, [customizations])
  const form = useForm<CustomizationFormValues>({
    resolver: zodResolver(customizationFormSchema),
    defaultValues: {
      customization_uid: ''
    }
  })

  useEffect(() => {
    if (open && menuItem) {
      try {
        if (currentCustomizationUid) {
          form.reset({
            customization_uid: currentCustomizationUid
          })
        } else {
          form.reset({ customization_uid: '' })
        }
      } catch (_error) {
        toast.error('Lỗi khi load customization data')
      }
    }
  }, [open, menuItem, form, customizationOptions, currentCustomizationUid])

  const onSubmit = async (values: CustomizationFormValues) => {
    try {
      if (!menuItem?.id || !company?.id || !selectedBrand?.id) {
        throw new Error('Required data is missing')
      }

      const customizationUid = values.customization_uid === 'none' ? null : values.customization_uid

      await updateItemCustomization.mutateAsync({
        menuItem: menuItem,
        customization_uid: customizationUid
      })

      onOpenChange(false)
    } catch (_error) {
      toast.error('Lỗi khi cập nhật customization')
    }
  }

  if (!menuItem) return null

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className='max-w-md'>
        <DialogHeader>
          <DialogTitle>Cấu hình Customization</DialogTitle>
        </DialogHeader>

        <div className='space-y-4'>
          <div>
            <p className='text-sm font-medium'>Món ăn:</p>
            <p className='text-muted-foreground text-sm'>{menuItem.item_name}</p>
          </div>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-4'>
              <FormField
                control={form.control}
                name='customization_uid'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Customization</FormLabel>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl>
                        <SelectTrigger className='w-full'>
                          <SelectValue placeholder='Chọn customization' />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {customizationOptions.map(option => (
                          <SelectItem key={option.id} value={option.value}>
                            {option.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <DialogFooter>
                <DialogClose asChild>
                  <Button type='button' variant='outline'>
                    Hủy
                  </Button>
                </DialogClose>
                <Button type='submit' disabled={form.formState.isSubmitting}>
                  {form.formState.isSubmitting ? 'Đang lưu...' : 'Lưu'}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </div>
      </DialogContent>
    </Dialog>
  )
}
