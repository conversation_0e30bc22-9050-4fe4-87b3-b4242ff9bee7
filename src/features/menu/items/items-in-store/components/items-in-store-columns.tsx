'use client'

import type { ColumnDef } from '@tanstack/react-table'

import { IconCopy } from '@tabler/icons-react'

import { Settings } from 'lucide-react'

import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'

import { DataTableColumnHeader } from '@/components/data-table'

import { ItemsInStore } from '../data'
import { ItemsInStoreRowActions } from './items-in-store-row-actions'

export const columns: ColumnDef<ItemsInStore>[] = [
  {
    id: 'index',
    header: '#',
    cell: ({ row }) => <div className='w-[50px]'>{row.index + 1}</div>,
    enableSorting: false,
    enableHiding: false,
    size: 50
  },
  {
    accessorKey: 'item_id',
    header: ({ column }) => <DataTableColumnHeader column={column} title='Mã món' />,
    cell: ({ row }) => <div className='text-sm font-medium'>{row.getValue('item_id')}</div>,
    enableSorting: false,
    enableHiding: true
  },
  {
    accessorKey: 'item_name',
    header: ({ column }) => <DataTableColumnHeader column={column} title='Tên món' />,
    cell: ({ row }) => (
      <div className='max-w-[200px] truncate text-sm font-medium'>{row.getValue('item_name')}</div>
    ),
    enableSorting: false,
    enableHiding: true
  },
  {
    accessorKey: 'ots_price',
    header: ({ column }) => <DataTableColumnHeader column={column} title='Giá' />,
    cell: ({ row }) => {
      const price = row.getValue('ots_price') as number
      return (
        <div className='text-sm font-medium'>
          {new Intl.NumberFormat('vi-VN', {
            style: 'currency',
            currency: 'VND'
          }).format(price)}
        </div>
      )
    },
    enableSorting: false,
    enableHiding: true
  },
  {
    accessorKey: 'ots_tax',
    header: ({ column }) => <DataTableColumnHeader column={column} title='VAT (%)' />,
    cell: ({ row }) => <div className='text-right text-sm'>{row.getValue('ots_tax')}</div>,
    enableSorting: false,
    enableHiding: true
  },
  {
    accessorKey: 'item_type_name',
    header: ({ column }) => <DataTableColumnHeader column={column} title='Nhóm món' />,
    cell: ({ row }) => (
      <Badge variant='outline' className='text-xs'>
        {row.getValue('item_type_name')}
      </Badge>
    ),
    enableSorting: false,
    enableHiding: false
  },
  {
    accessorKey: 'item_class_name',
    header: ({ column }) => <DataTableColumnHeader column={column} title='Loại món' />,
    cell: ({ row }) =>
      row.getValue('item_class_name') && (
        <Badge variant='outline' className='text-center text-xs'>
          {row.getValue('item_class_name')}
        </Badge>
      ),
    enableSorting: false,
    enableHiding: false
  },
  {
    accessorKey: 'unit_name',
    header: ({ column }) => <DataTableColumnHeader column={column} title='Đơn vị tính' />,
    cell: ({ row }) => <div className='text-sm'>{row.getValue('unit_name')}</div>,
    enableSorting: false,
    enableHiding: true
  },
  {
    accessorKey: 'is_eat_with',
    header: ({ column }) => <DataTableColumnHeader column={column} title='Món ăn kèm' />,
    cell: ({ row }) => (
      <div className='text-sm'>{row.getValue('is_eat_with') ? 'Món ăn kèm' : 'Món chính'}</div>
    ),
    enableSorting: false,
    enableHiding: true
  },
  {
    accessorKey: 'city_name',
    header: ({ column }) => <DataTableColumnHeader column={column} title='Thành phố' />,
    cell: ({ row }) => <div className='text-sm'>{row.getValue('city_name')}</div>,
    enableSorting: false,
    enableHiding: true
  },
  {
    accessorKey: 'store_uid',
    header: ({ column }) => <DataTableColumnHeader column={column} title='Áp dụng tại' />,
    cell: ({ row }) => {
      const store_uid = row.getValue('store_uid')
      return <div className='text-sm'>{store_uid ? 'Cửa hàng' : ''}</div>
    },
    enableSorting: false,
    enableHiding: true
  },
  {
    accessorKey: 'apply_with_store',
    header: ({ column }) => <DataTableColumnHeader column={column} title='Trạng thái' />,
    cell: ({ row }) => {
      const applyWithStore = row.getValue('apply_with_store') as number
      return <div className='text-sm'>{applyWithStore === 1 ? 'Sửa từ món gốc' : 'Món mới'}</div>
    },
    enableSorting: false,
    enableHiding: false
  },
  {
    accessorKey: 'extra_data',
    header: ({ column }) => <DataTableColumnHeader column={column} title='Cấu hình buffet' />,
    cell: ({ row }) => {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const extraData = row.getValue('extra_data') as any
      const isBuffetItem = extraData?.is_buffet_item === 1
      return (
        <Badge variant={isBuffetItem ? 'default' : 'secondary'}>
          {isBuffetItem ? 'Đã cấu hình' : 'Chưa cấu hình'}
        </Badge>
      )
    },
    enableSorting: false,
    enableHiding: true
  },
  {
    accessorKey: 'customization',
    header: ({ column }) => <DataTableColumnHeader column={column} title='Customization' />,
    cell: ({ row, table }) => {
      const menuItem = row.original
      const meta = table.options.meta as {
        onCustomizationClick?: (menuItem: ItemsInStore) => void
        customizations?: Array<{ id: string; name: string }>
      }

      const currentCustomizationUid = menuItem.customization_uid
      const currentCustomization = meta?.customizations?.find(c => c.id === currentCustomizationUid)

      if (currentCustomization) {
        return (
          <div className='flex items-center gap-2'>
            <span className='text-sm font-medium'>{currentCustomization.name}</span>
            <Button
              variant={'outline'}
              size='sm'
              onClick={() => meta?.onCustomizationClick?.(menuItem)}
              className='h-6 px-2 text-xs'
            >
              <Settings className='h-3 w-3' />
            </Button>
          </div>
        )
      }

      return (
        <Button
          variant={'outline'}
          size='sm'
          onClick={() => meta?.onCustomizationClick?.(menuItem)}
          className='h-7 px-2 text-xs'
        >
          <Settings className='mr-1 h-3 w-3' />
          Cấu hình
        </Button>
      )
    },
    enableSorting: false,
    enableHiding: true
  },
  {
    id: 'copy',
    header: 'Sao chép tạo món mới',
    cell: ({ row, table }) => {
      const menuItem = row.original
      const meta = table.options.meta as {
        onCopyClick?: (menuItem: ItemsInStore) => void
      }

      return (
        <Button
          variant='ghost'
          size='sm'
          className='ml-14 h-8 w-8'
          onClick={e => {
            e.stopPropagation()
            meta?.onCopyClick?.(menuItem)
          }}
        >
          <IconCopy className='h-4 w-4' />
          <span className='sr-only'>Sao chép thiết bị {row.original.item_name}</span>
        </Button>
      )
    },
    enableSorting: false,
    enableHiding: true
  },
  {
    accessorKey: 'active',
    header: 'Thao tác',
    cell: ({ row }) => {
      const status = row.getValue('active')
      return (
        <Badge variant={status === 'active' ? 'default' : 'secondary'}>
          {status === 1 ? 'Active' : 'Deactive'}
        </Badge>
      )
    },
    enableSorting: false,
    enableHiding: true
  },
  {
    id: 'actions',
    cell: ({ row }) => <ItemsInStoreRowActions row={row} />,
    enableSorting: true,
    enableHiding: false
  }
]
