'use client'

import * as React from 'react'

import {
  ColumnDef,
  ColumnFiltersState,
  SortingState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable
} from '@tanstack/react-table'

import { ScrollArea, ScrollBar } from '@/components/ui/scroll-area'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table'

import { DataTablePagination } from '@/components/data-table'

import { ItemsInStoreTableToolbar } from './items-in-store-table-toolbar'

interface ItemsInStoreDataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[]
  data: TData[]
  onCustomizationClick?: (itemsInStore: TData) => void
  onCopyClick?: (itemsInStore: TData) => void
  customizations?: Array<{ id: string; name: string }>
  selectedItemTypeUid?: string
  onItemTypeChange?: (itemTypeUid: string) => void
  selectedItemClassUid?: string
  onItemClassChange?: (itemClassUid: string) => void
  selectedStoreUid?: string
  onStoreChange?: (storeUid: string) => void
  selectedStatus?: string
  onStatusChange?: (status: string) => void
  selectedDaysOfWeek?: string[]
  onDaysOfWeekChange?: (daysOfWeek: string[]) => void
  selectedItemStatus?: string
  onItemStatusChange?: (itemStatus: string) => void
}

export function ItemsInStoreDataTable<TData, TValue>({
  columns,
  data,
  onCustomizationClick,
  onCopyClick,
  customizations,
  selectedItemTypeUid,
  onItemTypeChange,
  selectedStoreUid,
  onStoreChange,
  selectedStatus,
  onStatusChange,
  selectedDaysOfWeek,
  onDaysOfWeekChange,
  selectedItemStatus,
  onItemStatusChange
}: ItemsInStoreDataTableProps<TData, TValue>) {
  const [rowSelection, setRowSelection] = React.useState({})
  const [columnVisibility, setColumnVisibility] = React.useState<VisibilityState>({})
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([])
  const [sorting, setSorting] = React.useState<SortingState>([])

  const table = useReactTable({
    data,
    columns,
    state: {
      sorting,
      columnVisibility,
      rowSelection,
      columnFilters
    },
    enableRowSelection: true,
    onRowSelectionChange: setRowSelection,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onColumnVisibilityChange: setColumnVisibility,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFacetedRowModel: getFacetedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues(),
    meta: {
      onCustomizationClick,
      onCopyClick,
      customizations
    }
  })

  return (
    <div className='space-y-4'>
      <ItemsInStoreTableToolbar
        table={table}
        selectedItemTypeUid={selectedItemTypeUid}
        onItemTypeChange={onItemTypeChange}
        selectedStoreUid={selectedStoreUid}
        onStoreChange={onStoreChange}
        selectedStatus={selectedStatus}
        onStatusChange={onStatusChange}
        selectedDaysOfWeek={selectedDaysOfWeek}
        onDaysOfWeekChange={onDaysOfWeekChange}
        selectedItemStatus={selectedItemStatus}
        onItemStatusChange={onItemStatusChange}
      />
      <ScrollArea className='rounded-md border'>
        <Table className='relative'>
          <TableHeader>
            {table.getHeaderGroups().map(headerGroup => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map(header => {
                  return (
                    <TableHead key={header.id} colSpan={header.colSpan}>
                      {header.isPlaceholder
                        ? null
                        : flexRender(header.column.columnDef.header, header.getContext())}
                    </TableHead>
                  )
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map(row => (
                <TableRow key={row.id} data-state={row.getIsSelected() && 'selected'}>
                  {row.getVisibleCells().map(cell => (
                    <TableCell key={cell.id}>
                      {flexRender(cell.column.columnDef.cell, cell.getContext())}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={columns.length} className='h-24 text-center'>
                  Không có dữ liệu.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
        <ScrollBar orientation='horizontal' />
      </ScrollArea>
      <DataTablePagination table={table} />
    </div>
  )
}
