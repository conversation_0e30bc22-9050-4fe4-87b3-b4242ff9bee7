'use client'

import { Table } from '@tanstack/react-table'

import { X } from 'lucide-react'

import { useItemTypesData, useStoresData } from '@/hooks/api'

import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select'

import { DataTableViewOptions } from '@/components/data-table'
import { MultiSelect } from '@/components/multi-select'

interface ItemsInStoreTableToolbarProps<TData> {
  table: Table<TData>
  selectedItemTypeUid?: string
  onItemTypeChange?: (itemTypeUid: string) => void
  selectedStoreUid?: string
  onStoreChange?: (storeUid: string) => void
  selectedDaysOfWeek?: string[]
  onDaysOfWeekChange?: (daysOfWeek: string[]) => void
  selectedStatus?: string
  onStatusChange?: (status: string) => void
  selectedItemStatus?: string
  onItemStatusChange?: (itemStatus: string) => void
}

export function ItemsInStoreTableToolbar<TData>({
  table,
  selectedItemTypeUid = 'all',
  onItemTypeChange,
  selectedStoreUid = 'all',
  onStoreChange,
  selectedDaysOfWeek,
  onDaysOfWeekChange,
  selectedStatus,
  onStatusChange,
  selectedItemStatus,
  onItemStatusChange
}: ItemsInStoreTableToolbarProps<TData>) {
  const { data: itemTypesData = [] } = useItemTypesData()
  const { data: storesData = [] } = useStoresData()

  const itemTypeOptions = itemTypesData
    .filter(itemType => itemType.active === 1)
    .map(itemType => ({
      label: itemType.item_type_name,
      value: itemType.id
    }))

  const storeOptions = storesData
    .filter(store => store.isActive)
    .map(store => ({
      label: store.name,
      value: store.id
    }))

  const applyWithOptions = [
    { label: 'Cửa hàng', value: 'store' },
    { label: 'Cửa hàng và thành phố', value: 'store_city' }
  ]

  const itemStatusOptions = [
    { label: 'Active', value: 'active' },
    { label: 'Không bán', value: 'inactive' },
    { label: 'Món mới', value: 'new' },
    { label: 'Sửa từ món gốc', value: 'modified' }
  ]

  const daysOfWeekOptions = [
    { label: 'Thứ 2', value: '2' },
    { label: 'Thứ 3', value: '3' },
    { label: 'Thứ 4', value: '4' },
    { label: 'Thứ 5', value: '5' },
    { label: 'Thứ 6', value: '6' },
    { label: 'Thứ 7', value: '7' },
    { label: 'Chủ Nhật', value: '1' }
  ]

  const isFiltered = table.getState().columnFilters.length > 0

  return (
    <div className='flex items-center justify-between'>
      <div className='flex flex-1 items-center space-x-2'>
        <Input
          placeholder='Tìm kiếm món ăn...'
          value={(table.getColumn('name')?.getFilterValue() as string) ?? ''}
          onChange={event => table.getColumn('name')?.setFilterValue(event.target.value)}
          className='h-9 w-[150px] lg:w-[250px]'
        />

        <Select value={selectedStoreUid} onValueChange={onStoreChange}>
          <SelectTrigger className='h-10 w-[180px]'>
            <SelectValue placeholder='Chọn cửa hàng' />
          </SelectTrigger>
          <SelectContent>
            {storeOptions.map(option => (
              <SelectItem key={option.value} value={option.value}>
                {option.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Select value={selectedItemTypeUid} onValueChange={onItemTypeChange}>
          <SelectTrigger className='h-10 w-[180px]'>
            <SelectValue placeholder='Chọn loại món' />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value='all'>Tất cả nhóm món</SelectItem>
            {itemTypeOptions.map(option => (
              <SelectItem key={option.value} value={option.value}>
                {option.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        <MultiSelect
          options={daysOfWeekOptions}
          value={selectedDaysOfWeek}
          onValueChange={onDaysOfWeekChange || (() => {})}
          placeholder='Chọn ngày trong tuần'
          className='min-h-9 w-[300px]'
          maxCount={1}
        />

        <Select value={selectedStatus || 'store'} onValueChange={onStatusChange}>
          <SelectTrigger className='h-10 w-[180px]'>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            {applyWithOptions.map(option => (
              <SelectItem key={option.value} value={option.value}>
                {option.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Select value={selectedItemStatus} onValueChange={onItemStatusChange}>
          <SelectTrigger className='h-10 w-[180px]'>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value='all'>Tất cả trạng thái</SelectItem>
            {itemStatusOptions.map(option => (
              <SelectItem key={option.value} value={option.value}>
                {option.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        {isFiltered && (
          <Button
            variant='ghost'
            onClick={() => table.resetColumnFilters()}
            className='h-10 px-2 lg:px-3'
          >
            Reset
            <X className='ml-2 h-4 w-4' />
          </Button>
        )}
      </div>
      <div className='flex items-center gap-2'>
        <DataTableViewOptions table={table} />
      </div>
    </div>
  )
}
