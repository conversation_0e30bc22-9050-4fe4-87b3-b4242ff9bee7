import { ConfirmDialog } from '@/components/confirm-dialog'

import { useItemsInStore } from '../context'
import { useDeleteItemInStore } from '../hooks'
import { ExportDialog } from './export-dialog'
import { ImportDialog } from './import-dialog'
import { ItemsInStoreMutate } from './items-in-store-mutate'

interface Props {
  storeUid: string
}

export function ItemsInStoreDialogs({ storeUid }: Props) {
  const { open, setOpen, currentRow, setCurrentRow } = useItemsInStore()
  const { deleteItemInStore } = useDeleteItemInStore()

  return (
    <>
      <ExportDialog
        open={open === 'export'}
        onOpenChange={isOpen => {
          if (!isOpen) {
            setOpen(null)
          }
        }}
      />

      <ImportDialog
        open={open === 'import'}
        onOpenChange={isOpen => {
          if (!isOpen) {
            setOpen(null)
          }
        }}
      />

      <ItemsInStoreMutate
        key='items-in-store-create'
        open={open === 'create'}
        onOpenChange={isOpen => {
          if (!isOpen) {
            setOpen(null)
          }
        }}
        storeUid={storeUid}
      />

      {currentRow && (
        <>
          <ItemsInStoreMutate
            key={`items-in-store-copy-${currentRow.id}`}
            open={open === 'copy'}
            onOpenChange={isOpen => {
              if (!isOpen) {
                setOpen(null)
                setTimeout(() => {
                  setCurrentRow(null)
                }, 500)
              }
            }}
            currentRow={currentRow}
            isCopyMode={true}
          />

          <ItemsInStoreMutate
            key={`items-in-store-update-${currentRow.id}`}
            open={open === 'update'}
            onOpenChange={isOpen => {
              if (!isOpen) {
                setOpen(null)
                setTimeout(() => {
                  setCurrentRow(null)
                }, 500)
              }
            }}
            currentRow={currentRow}
          />

          <ConfirmDialog
            key='items-in-store-delete'
            destructive
            open={open === 'delete'}
            onOpenChange={isOpen => {
              if (!isOpen) {
                setOpen(null)
                setTimeout(() => {
                  setCurrentRow(null)
                }, 500)
              }
            }}
            handleConfirm={async () => {
              setOpen(null)
              setTimeout(() => {
                setCurrentRow(null)
              }, 500)
              deleteItemInStore(currentRow.id)
            }}
            className='max-w-md'
            title={`Bạn có muốn xoá ?`}
            desc={<>Hành động không thể hoàn tác.</>}
            confirmText='Xoá'
          />
        </>
      )}
    </>
  )
}
