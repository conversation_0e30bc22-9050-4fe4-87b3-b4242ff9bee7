import { useEffect, useState } from 'react'

import { z } from 'zod'

import { useForm, type SubmitHandler } from 'react-hook-form'

import { zodResolver } from '@hookform/resolvers/zod'

import { useAuthStore } from '@/stores/authStore'

import { useItemTypesData, useUnitsData, useItemClassesData, useStoreData } from '@/hooks/api'

import { Button } from '@/components/ui/button'
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog'
import { Form } from '@/components/ui/form'

import { ItemsInStore } from '../data'
import { useCreateItemInStore, useUpdateItemInStore } from '../hooks'
import { ItemFormSections } from './item-details-section'
import { SourceDialog } from './source-dialog'

const formSchema = z.object({
  item_name: z.string().min(1, 'Tên món là bắt buộc'),
  ots_price: z.coerce.number().min(0, '<PERSON><PERSON><PERSON> phải lớn hơn hoặc bằng 0'),
  description: z.string().optional(),
  item_id_barcode: z.string().optional(),
  is_eat_with: z.boolean(),
  is_featured: z.boolean(),
  item_class_uid: z.string().min(1, 'Vui lòng chọn nhóm'),
  item_type_uid: z.string().min(1, 'Vui lòng chọn loại món'),
  store_uid: z.string().min(1, 'Vui lòng chọn cửa hàng'),
  item_id: z.string().optional(),
  unit_uid: z.string().min(1, 'Vui lòng chọn đơn vị tính'),
  unit_secondary_uid: z.string().optional(),
  ots_tax: z.coerce.number().min(0).max(100),
  time_cooking: z.coerce.number().min(0),
  enable_edit_price: z.boolean(),
  is_print_label: z.boolean(),
  is_allow_discount: z.boolean(),
  is_virtual_item: z.boolean(),
  is_item_service: z.boolean(),
  is_buffet_item: z.boolean(),
  inqrFormula: z.string().optional(),
  is_service: z.boolean(),
  priceBySource: z.boolean(),
  selectedDays: z.array(z.string()),
  selectedHours: z.array(z.string()),
  sort: z.coerce.number().min(0)
})

export type FormValues = z.infer<typeof formSchema>

interface Props {
  open: boolean
  onOpenChange: (open: boolean) => void
  currentRow?: ItemsInStore
  isCopyMode?: boolean
  storeUid?: string
}

export function ItemsInStoreMutate({
  open,
  onOpenChange,
  currentRow,
  isCopyMode = false,
  storeUid
}: Props) {
  const isUpdate = !!currentRow && !isCopyMode
  const isCopy = !!currentRow && isCopyMode

  const { company, brands } = useAuthStore(state => state.auth)
  const selectedBrand = brands?.[0]

  const currentStoreUid = currentRow?.store_uid || storeUid
  const [openDialog, setOpenDialog] = useState(false)

  const { data: store } = useStoreData(currentStoreUid || '', open && !!currentStoreUid)
  const { data: itemTypes = [] } = useItemTypesData({
    skip_limit: true,
    store_uid: currentStoreUid,
    enabled: open && !!currentStoreUid
  })
  const { data: units = [] } = useUnitsData()
  const { data: itemClasses = [] } = useItemClassesData({
    skip_limit: true,
    enabled: open
  })

  const { createItemInStore } = useCreateItemInStore()
  const { updateItemInStore } = useUpdateItemInStore()

  const form = useForm({
    resolver: zodResolver(formSchema),
    defaultValues: {
      item_name: '',
      ots_price: 0,
      description: '',
      item_id_barcode: '',
      is_eat_with: false,
      is_featured: false,
      item_class_uid: '',
      item_type_uid: '',
      store_uid: storeUid || '',
      item_id: '',
      unit_uid: '',
      unit_secondary_uid: '',
      ots_tax: 0,
      time_cooking: 0,
      enable_edit_price: false,
      is_print_label: false,
      is_allow_discount: false,
      is_virtual_item: false,
      is_item_service: false,
      is_buffet_item: false,
      inqrFormula: '',
      is_service: false,
      priceBySource: false,
      selectedDays: [],
      selectedHours: [],
      sort: 1000
    }
  })

  useEffect(() => {
    if (open) {
      if ((isUpdate || isCopy) && currentRow) {
        form.reset({
          item_name: isCopy ? `${currentRow.item_name} (Copy)` : currentRow.item_name || '',
          ots_price: currentRow.ots_price || 0,
          description: currentRow.description || '',
          item_id_barcode: currentRow.item_id_barcode || '',
          is_eat_with: Boolean(currentRow.is_eat_with),
          is_featured: Boolean(currentRow.extra_data?.no_update_quantity_toping),
          item_class_uid: currentRow.item_class_uid || '',
          item_type_uid: currentRow.item_type_uid || '',
          store_uid: currentStoreUid || '',
          item_id: '',
          unit_uid: currentRow.unit_uid || '',
          unit_secondary_uid: currentRow.unit_secondary_uid || '',
          ots_tax: currentRow.ots_tax || 0,
          time_cooking: currentRow.time_cooking || 0,
          enable_edit_price: Boolean(currentRow.extra_data?.enable_edit_price) || false,
          is_print_label: Boolean(currentRow.is_print_label) || false,
          is_allow_discount: Boolean(currentRow.is_allow_discount) || false,
          is_virtual_item: Boolean(currentRow.extra_data?.is_virtual_item) || false,
          is_item_service: Boolean(currentRow.extra_data?.is_item_service) || false,
          is_buffet_item: Boolean(currentRow.extra_data?.is_buffet_item) || false,
          inqrFormula: '',
          is_service: Boolean(currentRow.is_service) || false,
          priceBySource: false,
          selectedDays: [],
          selectedHours: [],
          sort: currentRow.sort || 0
        })
      } else {
        form.reset({
          item_name: '',
          ots_price: 0,
          description: '',
          item_id_barcode: '',
          is_eat_with: false,
          is_featured: false,
          item_class_uid: '',
          item_type_uid: '',
          store_uid: currentStoreUid || '',
          item_id: '',
          unit_uid: '',
          unit_secondary_uid: '',
          ots_tax: 0,
          time_cooking: 0,
          enable_edit_price: false,
          is_print_label: false,
          is_allow_discount: false,
          is_virtual_item: false,
          is_item_service: false,
          is_buffet_item: false,
          inqrFormula: '',
          is_service: false,
          priceBySource: false,
          selectedDays: [],
          selectedHours: [],
          sort: 0
        })
      }
    }
  }, [isUpdate, isCopy, currentRow, open, form, currentStoreUid])

  const onSubmit: SubmitHandler<FormValues> = async data => {
    try {
      if (!company?.id || !selectedBrand?.id) {
        return
      }

      const requestData = {
        ...data, // Spread all form fields
        // Override specific fields that need transformation
        city_uid: store?.cityId,
        ta_price: data.ots_price,
        ta_tax: data.ots_tax,
        description: data.description || '',
        item_id_barcode: data.item_id_barcode || '',
        item_id: data.item_id || `ITEM-${Math.random().toString(36).substring(2, 6).toUpperCase()}`,
        unit_secondary_uid: data.unit_secondary_uid || '',
        extra_data: {
          price_by_source: [],
          is_virtual_item: data.is_virtual_item ? 1 : 0,
          is_item_service: data.is_item_service ? 1 : 0,
          no_update_quantity_toping: data.is_featured ? 1 : 0,
          enable_edit_price: data.enable_edit_price ? 1 : 0,
          is_buffet_item: data.is_buffet_item ? 1 : 0,
          exclude_items_buffet: [],
          up_size_buffet: []
        },
        // API-specific fields
        company_uid: company.id,
        brand_uid: selectedBrand.id,
        sort: data.sort || 1000,
        apply_with_store: 2
      }

      if (isUpdate && currentRow?.id) {
        const updateData = {
          ...data,
          city_uid: currentRow.city_uid,
          ta_price: data.ots_price,
          ta_tax: data.ots_tax,
          description: data.description || '',
          item_id_barcode: data.item_id_barcode || '',
          item_id:
            data.item_id || `ITEM-${Math.random().toString(36).substring(2, 6).toUpperCase()}`,
          unit_secondary_uid: data.unit_secondary_uid || '',
          extra_data: {
            is_buffet_item: data.is_buffet_item ? 1 : 0,
            up_size_buffet: [],
            is_item_service: data.is_item_service ? 1 : 0,
            is_virtual_item: data.is_virtual_item ? 1 : 0,
            price_by_source: [],
            enable_edit_price: data.enable_edit_price ? 1 : 0,
            exclude_items_buffet: [],
            no_update_quantity_toping: 0
          },
          time_sale_hour_day: 0,
          time_sale_date_week: 0,
          allow_take_away: 1,
          is_eat_with: data.is_eat_with ? 1 : 0,
          image_path: '',
          image_path_thumb: '',
          item_color: '',
          list_order: 0,
          is_service: data.is_service ? 1 : 0,
          is_material: 0,
          is_foreign: 0,
          quantity_default: 0,
          price_change: 0,
          currency_type_id: '',
          point: 0,
          is_gift: 0,
          is_fc: 0,
          show_on_web: 0,
          show_price_on_web: 0,
          cost_price: 0,
          is_print_label: data.is_print_label ? 1 : 0,
          quantity_limit: 0,
          is_kit: 0,
          process_index: 0,
          is_allow_discount: data.is_allow_discount ? 1 : 0,
          quantity_per_day: 0,
          item_id_eat_with: '',
          is_parent: 0,
          is_sub: 0,
          item_id_mapping: '',
          effective_date: 0,
          expire_date: 0,
          revision: currentRow.revision,
          source_uid: null,
          brand_uid: selectedBrand?.id || '',
          company_uid: company?.id || '',
          customization_uid: currentRow.customization_uid || '',
          is_fabi: 1,
          apply_with_store: 2
        }
        updateItemInStore(updateData)
      } else {
        createItemInStore(requestData)
      }

      onOpenChange(false)
      form.reset()
    } catch (_error) {
      // Error handling is done in the hook
    }
  }

  return (
    <>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className='max-h-[90vh] max-w-4xl overflow-y-auto sm:max-w-3xl'>
          <DialogHeader>
            <DialogTitle>Tạo món</DialogTitle>
          </DialogHeader>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-6'>
              <ItemFormSections
                form={form}
                itemTypes={itemTypes}
                itemClasses={itemClasses}
                units={units}
                store={store}
                currentStoreUid={currentStoreUid}
                setOpenDialog={setOpenDialog}
              />

              {/* Dialog Footer */}
              <DialogFooter className='gap-2'>
                <DialogClose asChild>
                  <Button type='button' variant='outline'>
                    Hủy
                  </Button>
                </DialogClose>
                <Button type='submit'>Lưu</Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      <SourceDialog
        open={openDialog}
        onOpenChange={setOpenDialog}
        onConfirm={() => {}}
        storeUid={currentStoreUid || ''}
      />
    </>
  )
}
