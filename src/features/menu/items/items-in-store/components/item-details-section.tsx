import type { UseFormReturn } from 'react-hook-form'

import type { ItemClass } from '@/types/item-class'
import type { Store } from '@/types/store'

import type { ItemType } from '@/lib/item-types-api'
import type { Unit } from '@/lib/units-api'

import { ItemBasicInfo } from './item-basic-info'
import { ItemConfiguration } from './item-configuration'
import type { FormValues } from './items-in-store-mutate'

interface Props {
  form: UseFormReturn<FormValues>
  itemTypes: ItemType[]
  itemClasses: ItemClass[]
  units: Unit[]
  store?: Store
  currentStoreUid?: string
  setOpenDialog: (open: boolean) => void
}

export function ItemFormSections({
  form,
  itemTypes,
  itemClasses,
  units,
  store,
  currentStoreUid,
  setOpenDialog
}: Props) {
  return (
    <div className='space-y-6'>
      <ItemBasicInfo
        form={form}
        itemTypes={itemTypes}
        itemClasses={itemClasses}
        units={units}
        store={store}
        currentStoreUid={currentStoreUid}
      />

      <ItemConfiguration form={form} setOpenDialog={setOpenDialog} />
    </div>
  )
}
