import React, { useState } from 'react'
import useDialogState from '@/hooks/use-dialog-state'
import { QuantityDay } from '../data'

type QuantityDayDialogType = 'create' | 'update' | 'delete'

interface QuantityDayContextType {
  open: QuantityDayDialogType | null
  setOpen: (str: QuantityDayDialogType | null) => void
  currentRow: QuantityDay | null
  setCurrentRow: React.Dispatch<React.SetStateAction<QuantityDay | null>>
}

const QuantityDayContext = React.createContext<QuantityDayContextType | null>(
  null
)

interface Props {
  children: React.ReactNode
}

export default function QuantityDayProvider({ children }: Props) {
  const [open, setOpen] = useDialogState<QuantityDayDialogType>(null)
  const [currentRow, setCurrentRow] = useState<QuantityDay | null>(null)
  return (
    <QuantityDayContext value={{ open, setOpen, currentRow, setCurrentRow }}>
      {children}
    </QuantityDayContext>
  )
}

// eslint-disable-next-line react-refresh/only-export-components
export const useQuantityDay = () => {
  const quantityDayContext = React.useContext(QuantityDayContext)

  if (!quantityDayContext) {
    throw new Error('useQuantityDay has to be used within <QuantityDayContext>')
  }

  return quantityDayContext
}
