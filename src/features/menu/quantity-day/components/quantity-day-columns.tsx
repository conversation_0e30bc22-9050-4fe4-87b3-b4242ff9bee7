'use client'

import { format } from 'date-fns'
import type { ColumnDef } from '@tanstack/react-table'
import { Checkbox } from '@/components/ui/checkbox'
import { DataTableColumnHeader } from '@/components/data-table'
import { QuantityDay } from '../data'
import { QuantityDayRowActions } from './quantity-day-row-actions'

export const columns: ColumnDef<QuantityDay>[] = [
  {
    id: 'select',
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && 'indeterminate')
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label='Select all'
        className='translate-y-[2px]'
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label='Select row'
        className='translate-y-[2px]'
      />
    ),
    enableSorting: false,
    enableHiding: false,
    size: 50,
  },
  {
    id: 'index',
    header: '#',
    cell: ({ row }) => <div className='w-[50px]'>{row.index + 1}</div>,
    enableSorting: false,
    enableHiding: false,
    size: 50,
  },
  {
    accessorKey: 'quantity',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Số lượng' />
    ),
    cell: ({ row }) => (
      <div className='text-sm font-medium'>{row.getValue('quantity')}</div>
    ),
    enableSorting: false,
    enableHiding: true,
  },
  {
    accessorKey: 'fromDate',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Từ ngày' />
    ),
    cell: ({ row }) => {
      const date = row.getValue('fromDate') as Date
      return <div className='text-sm'>{format(date, 'dd/MM/yyyy')}</div>
    },
    enableSorting: false,
    enableHiding: true,
  },
  {
    accessorKey: 'toDate',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Đến ngày' />
    ),
    cell: ({ row }) => {
      const date = row.getValue('toDate') as Date
      return <div className='text-sm'>{format(date, 'dd/MM/yyyy')}</div>
    },
    enableSorting: false,
    enableHiding: true,
  },
  {
    accessorKey: 'time',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Thời gian' />
    ),
    cell: ({ row }) => <div className='text-sm'>{row.getValue('time')}</div>,
    enableSorting: false,
    enableHiding: true,
  },
  {
    accessorKey: 'appliedItems',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Món áp dụng' />
    ),
    cell: ({ row }) => (
      <div
        className='max-w-[200px] truncate text-sm'
        title={row.getValue('appliedItems')}
      >
        {row.getValue('appliedItems')}
      </div>
    ),
    enableSorting: false,
    enableHiding: true,
  },
  {
    id: 'actions',
    cell: ({ row }) => <QuantityDayRowActions row={row} />,
  },
]
