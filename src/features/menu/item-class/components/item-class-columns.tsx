import { ColumnDef } from '@tanstack/react-table'

import { IconTrash } from '@tabler/icons-react'

import { ItemClass } from '@/types/item-class'

import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'

export const itemClassColumns: ColumnDef<ItemClass>[] = [
  {
    accessorKey: 'id',
    header: '#',
    cell: ({ row }) => {
      const index = row.index + 1
      return <div className='w-[50px] font-medium'>{index}</div>
    },
    enableSorting: false
  },
  {
    accessorKey: 'item_class_id',
    header: 'Mã loại món',
    cell: ({ row }) => {
      const itemClass = row.original
      return <span className='font-medium'>{itemClass.item_class_id}</span>
    }
  },
  {
    accessorKey: 'item_class_name',
    header: 'Tên loại món',
    cell: ({ row }) => {
      const itemClass = row.original
      return <span className='font-medium'>{itemClass.item_class_name}</span>
    }
  },
  {
    id: 'actions',
    header: 'Thao tác',
    cell: ({ row, table }) => {
      const itemClass = row.original
      const isActive = itemClass.active === 1
      const meta = table.options.meta as {
        onToggleItemClassStatus?: (itemClass: ItemClass) => void
      }

      return (
        <Badge
          variant={isActive ? 'default' : 'destructive'}
          className={`cursor-pointer ${
            isActive
              ? 'bg-green-500 text-white hover:bg-green-600'
              : 'bg-red-500 text-white hover:bg-red-600'
          }`}
          onClick={e => {
            e.stopPropagation()
            meta?.onToggleItemClassStatus?.(itemClass)
          }}
        >
          {isActive ? 'Active' : 'Deactive'}
        </Badge>
      )
    }
  },
  {
    id: 'delete',
    header: '',
    cell: ({ row, table }) => {
      const itemClass = row.original
      const meta = table.options.meta as {
        onDeleteItemClass?: (itemClass: ItemClass) => void
      }

      return (
        <Button
          variant='ghost'
          size='sm'
          onClick={e => {
            e.stopPropagation()
            meta?.onDeleteItemClass?.(itemClass)
          }}
          className='h-8 w-8 p-0 text-red-600 hover:text-red-700'
        >
          <IconTrash className='h-4 w-4' />
          <span className='sr-only'>Xóa loại món {itemClass.item_class_name}</span>
        </Button>
      )
    }
  }
]
