import { useState, useMemo } from 'react'

import { format } from 'date-fns'

import { IconDownload, IconChevronDown } from '@tabler/icons-react'

import { RemovedItem } from '@/types/item-removed'
import { toast } from 'sonner'
import * as XLSX from 'xlsx'

import { useAuthStore } from '@/stores/authStore'

import { removedItemsApi } from '@/lib/item-removed-api'
import { getStores, type ApiStore } from '@/lib/stores-api'
import { formatCurrency } from '@/lib/utils'

import {
  useRemovedItemsData,
  useRestoreRemovedItem,
  useBulkRestoreRemovedItems,
  useExportRemovedItemsReportByStores,
  useExportRemovedItemsReport
} from '@/hooks/api'
import { useStoresData } from '@/hooks/api/use-stores'

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select'

import { ConfirmModal } from '@/components/pos'
import { Button, Input } from '@/components/ui'

import { removedItemColumns, RemovedItemDataTable } from './components'

function getErrorMessage(error: unknown): string {
  if (error instanceof Error) {
    return error.message
  }
  if (typeof error === 'string') {
    return error
  }
  if (error && typeof error === 'object' && 'message' in error) {
    return String(error.message)
  }
  return 'Đã xảy ra lỗi không xác định'
}

function generateExcelReport(data: RemovedItem[], storeDisplayName: string) {
  // Create workbook and worksheet
  const workbook = XLSX.utils.book_new()

  // Prepare data for Excel
  const excelData = []

  // Row 1: Header with store name
  excelData.push([`Món đã xóa tại cửa hàng ${storeDisplayName}`])

  // Row 2: Column headers
  excelData.push(['Mã món', 'Tên món', 'Giá', 'Người xóa', 'Thời gian xóa'])

  // Row 3+: Data rows
  data.forEach(item => {
    const itemCode = item.item_id || ''
    const itemName = item.item_name || ''
    const price = formatCurrency(item.ots_price)
    const deletedBy = item.deleted_by || ''
    const deletedAt = format(new Date(item.deleted_at * 1000), 'dd/MM/yyyy HH:mm')

    excelData.push([itemCode, itemName, price, deletedBy, deletedAt])
  })

  // Create worksheet from data
  const worksheet = XLSX.utils.aoa_to_sheet(excelData)

  // Set column widths
  worksheet['!cols'] = [
    { wch: 15 }, // Mã món
    { wch: 30 }, // Tên món
    { wch: 15 }, // Giá
    { wch: 25 }, // Người xóa
    { wch: 20 } // Thời gian xóa
  ]

  // Style the header row (merge cells for title)
  worksheet['!merges'] = [
    { s: { r: 0, c: 0 }, e: { r: 0, c: 4 } } // Merge first row across all columns
  ]

  // Add worksheet to workbook
  XLSX.utils.book_append_sheet(workbook, worksheet, 'Món đã xóa')

  // Generate Excel file and download
  const fileName = `mon-da-xoa-tai-cua-hang-${storeDisplayName.replace(/[^a-zA-Z0-9]/g, '-')}-${format(new Date(), 'dd-MM-yyyy')}.xlsx`
  XLSX.writeFile(workbook, fileName)
}

function generateMultiStoreExcelReport(
  storesWithData: Array<{
    store: ApiStore
    data: RemovedItem[]
    success: boolean
  }>
) {
  // Create workbook
  const workbook = XLSX.utils.book_new()

  storesWithData.forEach(({ store, data }) => {
    // Format store name as [fb_store_id]-[Store Name]
    const storeDisplayName = `${store.fb_store_id}-${store.store_name}`

    // Prepare data for this store's sheet
    const excelData = []

    // Row 1: Header with store name
    excelData.push([`Món đã xóa tại cửa hàng ${storeDisplayName}`])

    // Row 2: Column headers
    excelData.push(['Mã món', 'Tên món', 'Giá', 'Người xóa', 'Thời gian xóa'])

    // Row 3+: Data rows
    data.forEach(item => {
      const itemCode = item.item_id || ''
      const itemName = item.item_name || ''
      const price = formatCurrency(item.ots_price)
      const deletedBy = item.deleted_by || ''
      const deletedAt = format(new Date(item.deleted_at * 1000), 'dd/MM/yyyy HH:mm')

      excelData.push([itemCode, itemName, price, deletedBy, deletedAt])
    })

    // Create worksheet from data
    const worksheet = XLSX.utils.aoa_to_sheet(excelData)

    // Set column widths
    worksheet['!cols'] = [
      { wch: 15 }, // Mã món
      { wch: 30 }, // Tên món
      { wch: 15 }, // Giá
      { wch: 25 }, // Người xóa
      { wch: 20 } // Thời gian xóa
    ]

    // Style the header row (merge cells for title)
    worksheet['!merges'] = [
      { s: { r: 0, c: 0 }, e: { r: 0, c: 4 } } // Merge first row across all columns
    ]

    // Add worksheet to workbook with store name as sheet name
    // Limit sheet name to 31 characters (Excel limit) and remove invalid characters
    const sheetName = storeDisplayName.replace(/[\\/?*[\]]/g, '').substring(0, 31)
    XLSX.utils.book_append_sheet(workbook, worksheet, sheetName)
  })

  // Generate Excel file and download
  const fileName = `mon-da-xoa-tat-ca-cua-hang-${format(new Date(), 'dd-MM-yyyy')}.xlsx`
  XLSX.writeFile(workbook, fileName)
}

export default function RemovedItemsInStorePage() {
  const [searchTerm, setSearchTerm] = useState('')
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedStoreId, setSelectedStoreId] = useState<string>('')
  const [confirmModalOpen, setConfirmModalOpen] = useState(false)
  const [selectedItem, setSelectedItem] = useState<RemovedItem | null>(null)
  const [selectedItems, setSelectedItems] = useState<RemovedItem[]>([])
  const [clearSelection, setClearSelection] = useState(false)

  const restoreItemMutation = useRestoreRemovedItem()
  const bulkRestoreItemsMutation = useBulkRestoreRemovedItems()
  const exportReportByStoresMutation = useExportRemovedItemsReportByStores()
  const exportReportAllStoresMutation = useExportRemovedItemsReport()
  const { data: stores = [] } = useStoresData()
  const { company, brands } = useAuthStore(state => state.auth)

  // Set default selected store to first store when stores are loaded
  useMemo(() => {
    if (stores.length > 0 && !selectedStoreId) {
      setSelectedStoreId(stores[0].id)
    }
  }, [stores, selectedStoreId])

  const listStoreUid = useMemo(() => {
    return selectedStoreId ? [selectedStoreId] : []
  }, [selectedStoreId])

  const {
    data: removedItems,
    isLoading,
    error
  } = useRemovedItemsData({
    searchTerm: searchTerm || undefined,
    listStoreUid
  })

  const handleRestoreItem = (item: RemovedItem) => {
    setSelectedItem(item)
    setSelectedItems([]) // Clear bulk selection
    setConfirmModalOpen(true)
  }

  const handleConfirmRestore = async () => {
    try {
      if (selectedItems.length > 0) {
        // Bulk restore cho multiple items
        const itemUids = selectedItems.map(item => item.id)
        await bulkRestoreItemsMutation.mutateAsync(itemUids)
        toast.success(`${selectedItems.length} món đã được khôi phục thành công!`)
        setSelectedItems([])
        // Clear table selection
        setClearSelection(true)
        setTimeout(() => setClearSelection(false), 100)
      } else if (selectedItem) {
        // Single item restore
        await bulkRestoreItemsMutation.mutateAsync([selectedItem.id])
        toast.success(`Món "${selectedItem.item_name}" đã được khôi phục thành công!`)
        setSelectedItem(null)
      }

      setConfirmModalOpen(false)
    } catch (error) {
      const errorMessage = getErrorMessage(error)
      toast.error(errorMessage)
    }
  }

  const handleBulkRestore = (items: RemovedItem[]) => {
    setSelectedItems(items)
    setSelectedItem(null) // Clear single item selection
    setConfirmModalOpen(true)
  }

  const handleExportReportBySelectedStore = async () => {
    if (!selectedStoreId) {
      toast.error('Không có cửa hàng nào được chọn')
      return
    }

    const selectedStore = stores.find(s => s.id === selectedStoreId)
    if (!selectedStore) {
      toast.error('Không tìm thấy thông tin cửa hàng')
      return
    }

    try {
      // Get raw store data to access fb_store_id
      const selectedBrand = brands?.[0]
      if (!company?.id || !selectedBrand?.id) {
        toast.error('Không tìm thấy thông tin công ty hoặc thương hiệu')
        return
      }

      const rawStoresResponse = await getStores({
        company_uid: company.id,
        brand_uid: selectedBrand.id
      })

      const rawSelectedStore = rawStoresResponse.data.find(s => s.id === selectedStoreId)
      if (!rawSelectedStore) {
        toast.error('Không tìm thấy thông tin chi tiết cửa hàng')
        return
      }

      // Fetch the removed items data for the selected store using the API directly
      const removedItemsData = await removedItemsApi.getRemovedItems({
        listStoreUid: [selectedStoreId]
      })

      if (!removedItemsData || removedItemsData.length === 0) {
        toast.error('Không có dữ liệu món đã xóa để xuất báo cáo')
        return
      }

      // Format store name as [fb_store_id]-[Store Name]
      const storeDisplayName = `${rawSelectedStore.fb_store_id}-${rawSelectedStore.store_name}`

      // Generate and download the report
      generateExcelReport(removedItemsData, storeDisplayName)
      toast.success('Báo cáo theo cửa hàng đã được xuất thành công!')
    } catch (error) {
      const errorMessage = getErrorMessage(error)
      toast.error(errorMessage)
    }
  }

  const handleExportReportAllStores = async () => {
    if (stores.length === 0) {
      toast.error('Không có cửa hàng nào để xuất báo cáo')
      return
    }

    try {
      // Get raw store data to access fb_store_id for all stores
      const selectedBrand = brands?.[0]
      if (!company?.id || !selectedBrand?.id) {
        toast.error('Không tìm thấy thông tin công ty hoặc thương hiệu')
        return
      }

      const rawStoresResponse = await getStores({
        company_uid: company.id,
        brand_uid: selectedBrand.id
      })

      if (!rawStoresResponse.data || rawStoresResponse.data.length === 0) {
        toast.error('Không tìm thấy danh sách cửa hàng')
        return
      }

      // Make API calls for each store concurrently
      const storeDataPromises = rawStoresResponse.data.map(async store => {
        try {
          const removedItemsData = await removedItemsApi.getRemovedItems({
            listStoreUid: [store.id]
          })

          return {
            store,
            data: removedItemsData || [],
            success: true
          }
        } catch (error) {
          console.warn(`Failed to fetch data for store ${store.store_name}:`, error)
          return {
            store,
            data: [],
            success: false
          }
        }
      })

      // Wait for all API calls to complete
      const storeResults = await Promise.all(storeDataPromises)

      // Filter stores that have data
      const storesWithData = storeResults.filter(result => result.success && result.data.length > 0)

      if (storesWithData.length === 0) {
        toast.error('Không có dữ liệu món đã xóa từ bất kỳ cửa hàng nào')
        return
      }

      // Generate multi-sheet Excel report
      generateMultiStoreExcelReport(storesWithData)

      const totalStores = rawStoresResponse.data.length
      const storesWithDataCount = storesWithData.length
      toast.success(
        `Báo cáo tất cả cửa hàng đã được xuất thành công! (${storesWithDataCount}/${totalStores} cửa hàng có dữ liệu)`
      )
    } catch (error) {
      const errorMessage = getErrorMessage(error)
      toast.error(errorMessage)
    }
  }

  return (
    <div className='container mx-auto px-4 py-8'>
      <div className='mb-2 flex items-center justify-between'>
        <div className='flex items-center gap-4'>
          <h2 className='text-xl font-semibold'>Món đã xóa tại cửa hàng</h2>
          <Input
            placeholder='Tìm kiếm món đã xóa...'
            className='w-64'
            value={searchQuery}
            onChange={e => setSearchQuery(e.target.value)}
            onKeyDown={e => {
              if (e.key === 'Enter') {
                e.preventDefault()
                setSearchTerm(searchQuery)
              }
            }}
          />
          <Select value={selectedStoreId} onValueChange={setSelectedStoreId}>
            <SelectTrigger className='w-48'>
              <SelectValue placeholder='Chọn cửa hàng' />
            </SelectTrigger>
            <SelectContent>
              {stores.map(store => (
                <SelectItem key={store.id} value={store.id}>
                  {store.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              size='sm'
              disabled={
                exportReportByStoresMutation.isPending || exportReportAllStoresMutation.isPending
              }
            >
              <IconDownload className='mr-2 h-4 w-4' />
              {exportReportByStoresMutation.isPending || exportReportAllStoresMutation.isPending
                ? 'Đang xuất...'
                : 'Xuất báo cáo'}
              <IconChevronDown className='ml-2 h-4 w-4' />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align='end'>
            <DropdownMenuItem onClick={handleExportReportBySelectedStore}>
              <IconDownload className='mr-2 h-4 w-4' />
              Xuất báo cáo theo cửa hàng đã chọn
            </DropdownMenuItem>
            <DropdownMenuItem onClick={handleExportReportAllStores}>
              <IconDownload className='mr-2 h-4 w-4' />
              Xuất báo cáo tất cả cửa hàng
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      {error ? (
        <div className='py-8 text-center'>
          <p className='text-red-600'>{getErrorMessage(error)}</p>
        </div>
      ) : isLoading ? (
        <div className='py-8 text-center'>
          <p>Đang tải dữ liệu món đã xóa...</p>
        </div>
      ) : (
        <RemovedItemDataTable
          columns={removedItemColumns}
          data={removedItems || []}
          onRestoreItem={handleRestoreItem}
          onBulkRestore={handleBulkRestore}
          clearSelection={clearSelection}
        />
      )}

      <ConfirmModal
        open={confirmModalOpen}
        onOpenChange={setConfirmModalOpen}
        content={
          selectedItems.length > 0
            ? `Bạn có muốn khôi phục ${selectedItems.length} món đã chọn?`
            : 'Bạn có muốn khôi phục?'
        }
        confirmText='Xác nhận'
        onConfirm={handleConfirmRestore}
        isLoading={restoreItemMutation.isPending || bulkRestoreItemsMutation.isPending}
      />
    </div>
  )
}
