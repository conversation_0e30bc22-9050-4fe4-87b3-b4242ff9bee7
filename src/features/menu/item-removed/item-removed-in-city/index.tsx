import { useState, useMemo } from 'react'
import { IconDownload } from '@tabler/icons-react'
import { RemovedItem } from '@/types/item-removed'
import { toast } from 'sonner'
import { getErrorMessage } from '@/utils/error-utils'
import {
  useRemovedItemsData,
  useRestoreRemovedItem,
  useBulkRestoreRemovedItems,
  useExportRemovedItemsReport,
  useCitiesData,
} from '@/hooks/api'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { ConfirmModal } from '@/components/pos'
import { Button, Input } from '@/components/ui'
import { removedItemColumns, RemovedItemDataTable } from './components'



export default function RemovedItemsInCityPage() {
  const [searchTerm, setSearchTerm] = useState('')
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedCityId, setSelectedCityId] = useState<string>('all')
  const [confirmModalOpen, setConfirmModalOpen] = useState(false)
  const [selectedItem, setSelectedItem] = useState<RemovedItem | null>(null)
  const [selectedItems, setSelectedItems] = useState<RemovedItem[]>([])
  const [clearSelection, setClearSelection] = useState(false)

  const restoreItemMutation = useRestoreRemovedItem()
  const bulkRestoreItemsMutation = useBulkRestoreRemovedItems()
  const exportReportMutation = useExportRemovedItemsReport()
  const { data: cities = [] } = useCitiesData()

  const listCityUid = useMemo(() => {
    if (selectedCityId === 'all' || !selectedCityId) {
      return cities.map((city) => city.id)
    }
    return [selectedCityId]
  }, [selectedCityId, cities])

  const {
    data: removedItems,
    isLoading,
    error,
  } = useRemovedItemsData({
    searchTerm: searchTerm || undefined,
    listCityUid,
  })

  const handleRestoreItem = (item: RemovedItem) => {
    setSelectedItem(item)
    setSelectedItems([]) // Clear bulk selection
    setConfirmModalOpen(true)
  }

  const handleConfirmRestore = async () => {
    try {
      if (selectedItems.length > 0) {
        // Bulk restore cho multiple items
        const itemUids = selectedItems.map((item) => item.id)
        await bulkRestoreItemsMutation.mutateAsync(itemUids)
        toast.success(
          `${selectedItems.length} món đã được khôi phục thành công!`
        )
        setSelectedItems([])
        // Clear table selection
        setClearSelection(true)
        setTimeout(() => setClearSelection(false), 100)
      } else if (selectedItem) {
        // Single item restore
        await bulkRestoreItemsMutation.mutateAsync([selectedItem.id])
        toast.success(
          `Món "${selectedItem.item_name}" đã được khôi phục thành công!`
        )
        setSelectedItem(null)
      }

      setConfirmModalOpen(false)
    } catch (error) {
      const errorMessage = getErrorMessage(error)
      toast.error(errorMessage)
    }
  }

  const getCityName = (cityUid: string) => {
    const city = cities.find((c) => c.id === cityUid)
    return city?.city_name || cityUid
  }

  const handleBulkRestore = (items: RemovedItem[]) => {
    setSelectedItems(items)
    setSelectedItem(null) // Clear single item selection
    setConfirmModalOpen(true)
  }

  const handleExportReport = async () => {
    try {
      const cityUids =
        selectedCityId === 'all'
          ? cities.map((city) => city.id)
          : [selectedCityId]

      const blob = await exportReportMutation.mutateAsync(cityUids)

      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `removed-items-report-${new Date().toISOString().split('T')[0]}.xlsx`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)

      toast.success('Báo cáo đã được xuất thành công!')
    } catch (error) {
      const errorMessage = getErrorMessage(error)
      toast.error(errorMessage)
    }
  }

  return (
    <div className='container mx-auto px-4 py-8'>
      <div className='mb-2 flex items-center justify-between'>
        <div className='flex items-center gap-4'>
          <h2 className='text-xl font-semibold'>Món đã xoá</h2>
          <Input
            placeholder='Tìm kiếm món đã xóa...'
            className='w-64'
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            onKeyDown={(e) => {
              if (e.key === 'Enter') {
                e.preventDefault()
                setSearchTerm(searchQuery)
              }
            }}
          />
          <Select value={selectedCityId} onValueChange={setSelectedCityId}>
            <SelectTrigger className='w-48'>
              <SelectValue placeholder='Chọn thành phố' />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value='all'>Tất cả các thành phố</SelectItem>
              {cities.map((city) => (
                <SelectItem key={city.id} value={city.id}>
                  {city.city_name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <Button
          size='sm'
          onClick={handleExportReport}
          disabled={exportReportMutation.isPending}
        >
          <IconDownload className='mr-2 h-4 w-4' />
          {exportReportMutation.isPending ? 'Đang xuất...' : 'Xuất báo cáo'}
        </Button>
      </div>

      {error ? (
        <div className='py-8 text-center'>
          <p className='text-red-600'>{getErrorMessage(error)}</p>
        </div>
      ) : isLoading ? (
        <div className='py-8 text-center'>
          <p>Đang tải dữ liệu món đã xóa...</p>
        </div>
      ) : (
        <RemovedItemDataTable
          columns={removedItemColumns}
          data={removedItems || []}
          onRestoreItem={handleRestoreItem}
          getCityName={getCityName}
          onBulkRestore={handleBulkRestore}
          clearSelection={clearSelection}
        />
      )}

      <ConfirmModal
        open={confirmModalOpen}
        onOpenChange={setConfirmModalOpen}
        content={
          selectedItems.length > 0
            ? `Bạn có muốn khôi phục ${selectedItems.length} món đã chọn?`
            : 'Bạn có muốn khôi phục?'
        }
        confirmText='Xác nhận'
        onConfirm={handleConfirmRestore}
        isLoading={
          restoreItemMutation.isPending || bulkRestoreItemsMutation.isPending
        }
      />
    </div>
  )
}
