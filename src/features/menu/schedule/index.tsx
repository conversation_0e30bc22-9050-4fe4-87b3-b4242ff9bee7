import { useState, useMemo } from 'react'

import { Header } from '@/components/layout/header'
import { Main } from '@/components/layout/main'
import { ProfileDropdown } from '@/components/profile-dropdown'
import { Search } from '@/components/search'
import { ThemeSwitch } from '@/components/theme-switch'

import {
  columns,
  MenuScheduleButtons,
  MenuScheduleTableSkeleton,
  MenuScheduleDialogs,
  MenuScheduleDataTable
} from './components'
import MenuScheduleProvider from './context'
import { useMenuScheduleForTable } from './hooks'

function MenuScheduleContent() {
  const [selectedStatus, setSelectedStatus] = useState<string>('all')

  const filterParams = useMemo(() => {
    const params: Record<string, string> = {}

    if (selectedStatus === 'PENDING') {
      params.status = 'PENDING'
    } else if (selectedStatus === 'PROCESS') {
      params.status = 'PROCESS'
    } else if (selectedStatus === 'DONE') {
      params.status = 'DONE'
    }

    return params
  }, [selectedStatus])

  const {
    data: schedules = [],
    isLoading,
    error
  } = useMenuScheduleForTable({
    params: filterParams
  })

  if (error) {
    return (
      <div className='flex items-center justify-center p-8'>
        <div className='text-center'>
          <p className='text-muted-foreground mb-2 text-sm'>Có lỗi xảy ra khi tải dữ liệu</p>
          <p className='text-muted-foreground text-xs'>{error?.message || 'Lỗi không xác định'}</p>
          <button
            onClick={() => window.location.reload()}
            className='mt-4 rounded bg-blue-500 px-4 py-2 text-white hover:bg-blue-600'
          >
            Tải lại trang
          </button>
        </div>
      </div>
    )
  }

  return (
    <>
      <Header>
        <div className='ml-auto flex items-center space-x-4'>
          <Search />
          <ThemeSwitch />
          <ProfileDropdown />
        </div>
      </Header>

      <Main>
        <div className='mb-2 flex flex-wrap items-center justify-between space-y-2 gap-x-4'>
          <div>
            <h2 className='text-2xl font-bold tracking-tight'>Danh sách lịch</h2>
            <p className='text-muted-foreground'>
              Quản lý lịch trình thời gian áp dụng theo cửa hàng
            </p>
          </div>
          <MenuScheduleButtons />
        </div>

        <div className='-mx-4 flex-1 overflow-auto px-4 py-1 lg:flex-row lg:space-y-0 lg:space-x-12'>
          {isLoading && <MenuScheduleTableSkeleton />}
          {!isLoading && (
            <MenuScheduleDataTable
              columns={columns}
              data={schedules}
              selectedStatus={selectedStatus}
              onStatusChange={setSelectedStatus}
            />
          )}
        </div>
      </Main>

      <MenuScheduleDialogs />
    </>
  )
}

export default function MenuSchedulePage() {
  return (
    <MenuScheduleProvider>
      <MenuScheduleContent />
    </MenuScheduleProvider>
  )
}
