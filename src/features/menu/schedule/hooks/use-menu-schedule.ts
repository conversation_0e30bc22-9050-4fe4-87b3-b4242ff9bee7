import { useCitiesData, useStoresData } from '@/hooks/api'
import { useCurrentUser } from '@/hooks/use-auth'

import { useMenuScheduleData } from './use-menu-schedule-data'

interface UseMenuScheduleOptions {
  params?: Record<string, string>
  enabled?: boolean
}

export const useMenuScheduleForTable = (options: UseMenuScheduleOptions = {}) => {
  const { user, company } = useCurrentUser()

  const companyUid = company?.id || user?.company_uid || '595e8cb4-674c-49f7-adec-826b211a7ce3'

  const getSelectedBrandUid = () => {
    try {
      const selectedBrand = localStorage.getItem('pos_selected_brand')
      if (selectedBrand) {
        const parsed = JSON.parse(selectedBrand)
        const brandId = parsed.id || parsed.brandId || ''
        if (brandId) return brandId
      }

      const brandsData = localStorage.getItem('pos_brands_data')
      if (brandsData) {
        const brands = JSON.parse(brandsData)
        if (Array.isArray(brands) && brands.length > 0) {
          const activeBrand = brands.find(brand => brand.active === 1) || brands[0]
          return activeBrand.id || ''
        }
      }
    } catch {
      // Error parsing data
    }

    return 'd43a01ec-2f38-4430-a7ca-9b3324f7d39e'
  }

  const brandUid = getSelectedBrandUid()

  const enhancedOptions = {
    ...options,
    params: {
      company_uid: companyUid,
      brand_uid: brandUid,
      ...options.params
    }
  }

  const scheduleQuery = useMenuScheduleData(enhancedOptions)

  const { data: citiesData = [] } = useCitiesData()
  const { data: storesData = [] } = useStoresData()

  const transformedData =
    scheduleQuery.data?.map((schedule, index) => ({
      ...schedule,
      id: index,
      city_uid: schedule.city_uid,
      store_uid: schedule.store_uid,
      city_name:
        citiesData.find(city => city.id === schedule.city_uid)?.city_name || schedule.city_uid,
      store_name:
        storesData.find(store => store.id === schedule.store_uid)?.name || schedule.store_uid,
      time_formatted: new Date(schedule.time).toLocaleDateString('vi-VN'),
      end_time_formatted: schedule.end_time
        ? new Date(schedule.end_time).toLocaleDateString('vi-VN')
        : ''
    })) || []

  return {
    ...scheduleQuery,
    data: transformedData
  }
}
