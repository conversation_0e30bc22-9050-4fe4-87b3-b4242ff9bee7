import { useMutation, useQueryClient } from '@tanstack/react-query'

import { toast } from 'sonner'

import { menuScheduleApi } from '@/lib/menu-schedule-api'

import type { MenuSchedule } from '../data'

export const useUpdateMenuSchedule = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: Partial<MenuSchedule> }) =>
      menuScheduleApi.updateMenuSchedule(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['menu-schedule'] })
      toast('Lịch trình menu đã được cập nhật thành công')
    },
    onError: (error: Error) => {
      toast(error.message)
    }
  })
}
