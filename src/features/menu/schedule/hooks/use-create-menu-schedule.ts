import { useMutation, useQueryClient } from '@tanstack/react-query'

import { toast } from 'sonner'

import { menuScheduleApi } from '@/lib/menu-schedule-api'

import type { MenuSchedule } from '../data'

export const useCreateMenuSchedule = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data: Partial<MenuSchedule>) => menuScheduleApi.createMenuSchedule(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['menu-schedule'] })
      toast('Lịch trình menu đã được tạo thành công')
    },
    onError: (error: Error) => {
      toast(error.message)
    }
  })
}
