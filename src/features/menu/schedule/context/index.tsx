'use client'

import { createContext, useContext, useState, ReactNode } from 'react'

import type { MenuSchedule } from '../data'

type DialogType = 'create' | 'edit' | 'delete' | null

interface MenuScheduleContextType {
  open: DialogType
  setOpen: (open: DialogType) => void
  currentRow: MenuSchedule | null
  setCurrentRow: (row: MenuSchedule | null) => void
}

const MenuScheduleContext = createContext<MenuScheduleContextType | undefined>(undefined)

interface MenuScheduleProviderProps {
  children: ReactNode
}

export default function MenuScheduleProvider({ children }: MenuScheduleProviderProps) {
  const [open, setOpen] = useState<DialogType>(null)
  const [currentRow, setCurrentRow] = useState<MenuSchedule | null>(null)

  const value = {
    open,
    setOpen,
    currentRow,
    setCurrentRow
  }

  return <MenuScheduleContext.Provider value={value}>{children}</MenuScheduleContext.Provider>
}

export function useMenuSchedule() {
  const context = useContext(MenuScheduleContext)
  if (context === undefined) {
    throw new Error('useMenuSchedule must be used within a MenuScheduleProvider')
  }
  return context
}
