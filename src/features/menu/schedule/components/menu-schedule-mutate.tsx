'use client'

import { useState } from 'react'

import { z } from 'zod'

import { format } from 'date-fns'

import { useForm } from 'react-hook-form'

import { zodResolver } from '@hookform/resolvers/zod'

import { vi } from 'date-fns/locale'
import { CalendarIcon, Search, Trash2 } from 'lucide-react'

import { cn } from '@/lib/utils'

import { useCitiesData, useStoresData } from '@/hooks/api'

import { Button } from '@/components/ui/button'
import { Calendar } from '@/components/ui/calendar'
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table'

import { MenuSchedule } from '../data/schema'
import { MenuItemScheduleDialog } from './menu-item-schedule-dialog'

const formSchema = z.object({
  start_date: z.date({ required_error: 'Ngày bắt đầu là bắt buộc' }),
  end_date: z.date().optional(),
  city_uid: z.string().min(1, 'Thành phố là bắt buộc'),
  store_uid: z.string().min(1, 'Cửa hàng là bắt buộc')
})

type FormData = z.infer<typeof formSchema>

interface MenuItem {
  id: string
  code: string
  name: string
  action: 'UPDATE' | 'DELETE' | 'ADD'
}

interface MenuScheduleMutateProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  currentRow?: MenuSchedule
}

export function MenuScheduleMutate({ open, onOpenChange, currentRow }: MenuScheduleMutateProps) {
  const [menuItems, setMenuItems] = useState<MenuItem[]>([])
  const [searchQuery, setSearchQuery] = useState('')
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [isLoading, setIsLoading] = useState(false)

  const { data: citiesData = [] } = useCitiesData()
  const { data: storesData = [] } = useStoresData()

  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      start_date: currentRow?.time ? new Date(currentRow.time) : undefined,
      end_date: currentRow?.end_time ? new Date(currentRow.end_time) : undefined,
      city_uid: currentRow?.city_uid || '',
      store_uid: currentRow?.store_uid || ''
    }
  })

  const selectedCityUid = form.watch('city_uid')

  const filteredStores = selectedCityUid
    ? storesData.filter(store => store.cityId === selectedCityUid)
    : []

  const handleAddMenuItem = () => {
    setIsDialogOpen(true)
  }

  const handleDialogConfirm = (items: MenuItem[]) => {
    setMenuItems([...menuItems, ...items])
  }

  const handleRemoveMenuItem = (id: string) => {
    setMenuItems(menuItems.filter(item => item.id !== id))
  }

  const handleActionChange = (id: string, action: MenuItem['action']) => {
    setMenuItems(menuItems.map(item => (item.id === id ? { ...item, action } : item)))
  }

  const handleSubmit = async (data: FormData) => {
    setIsLoading(true)
    try {
      // Handle form submission here
      console.log('Form submitted:', { ...data, items: menuItems })
      onOpenChange(false)
    } catch (error) {
      console.error('Error submitting form:', error)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent
        className='max-h-[90vh] max-w-4xl overflow-y-auto sm:max-w-4xl'
        onInteractOutside={() => {
          // Allow closing dialog by clicking outside
          onOpenChange(false)
        }}
      >
        <DialogHeader>
          <DialogTitle>Tạo lịch thay đổi</DialogTitle>
        </DialogHeader>

        <div className='space-y-6'>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(handleSubmit)} className='space-y-6'>
              {/* Thông tin cơ bản */}
              <div className='rounded-lg border p-4'>
                <h3 className='mb-4 font-medium'>Thông tin cơ bản</h3>

                <div className='grid grid-cols-2 gap-4'>
                  <FormField
                    control={form.control}
                    name='start_date'
                    render={({ field }) => (
                      <FormItem className='flex flex-col'>
                        <FormLabel>Ngày bắt đầu *</FormLabel>
                        <Popover>
                          <PopoverTrigger asChild>
                            <FormControl>
                              <Button
                                variant={'outline'}
                                className={cn(
                                  'w-full pl-3 text-left font-normal',
                                  !field.value && 'text-muted-foreground'
                                )}
                              >
                                {field.value ? (
                                  format(field.value, 'dd/MM/yyyy', { locale: vi })
                                ) : (
                                  <span>26/07/2025 00:00</span>
                                )}
                                <CalendarIcon className='ml-auto h-4 w-4 opacity-50' />
                              </Button>
                            </FormControl>
                          </PopoverTrigger>
                          <PopoverContent className='w-auto p-0' align='start'>
                            <Calendar
                              mode='single'
                              selected={field.value}
                              onSelect={field.onChange}
                              disabled={date => date < new Date(new Date().setHours(0, 0, 0, 0))}
                              initialFocus
                            />
                          </PopoverContent>
                        </Popover>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name='end_date'
                    render={({ field }) => (
                      <FormItem className='flex flex-col'>
                        <FormLabel>Ngày kết thúc</FormLabel>
                        <Popover>
                          <PopoverTrigger asChild>
                            <FormControl>
                              <Button
                                variant={'outline'}
                                className={cn(
                                  'w-full pl-3 text-left font-normal',
                                  !field.value && 'text-muted-foreground'
                                )}
                              >
                                {field.value ? (
                                  format(field.value, 'dd/MM/yyyy', { locale: vi })
                                ) : (
                                  <span>Chọn ngày</span>
                                )}
                                <CalendarIcon className='ml-auto h-4 w-4 opacity-50' />
                              </Button>
                            </FormControl>
                          </PopoverTrigger>
                          <PopoverContent className='w-auto p-0' align='start'>
                            <Calendar
                              mode='single'
                              selected={field.value}
                              onSelect={field.onChange}
                              disabled={date => date < new Date(new Date().setHours(0, 0, 0, 0))}
                              initialFocus
                            />
                          </PopoverContent>
                        </Popover>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className='mt-4 grid grid-cols-2 gap-4'>
                  <FormField
                    control={form.control}
                    name='city_uid'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Thành phố *</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger className='w-full'>
                              <SelectValue placeholder='Hồ Chí Minh' />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {citiesData.map(city => (
                              <SelectItem key={city.id} value={city.id}>
                                {city.city_name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name='store_uid'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Cửa hàng</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger className='w-full'>
                              <SelectValue placeholder='Tulum Bình Lợi' />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {filteredStores.map(store => (
                              <SelectItem key={store.id} value={store.id}>
                                {store.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className='mt-4'>
                  <p className='text-muted-foreground text-sm'>Thức đơn thay đổi theo cửa hàng</p>
                </div>
              </div>

              {/* Món liên lịch thay đổi */}
              <div className='rounded-lg border p-4'>
                <h3 className='mb-4 font-medium'>Món liên lịch thay đổi</h3>

                <div className='mb-4 flex gap-2'>
                  <div className='flex-1'>
                    <Input
                      placeholder='Nhập tên món hoặc thao tác'
                      value={searchQuery}
                      onChange={e => setSearchQuery(e.target.value)}
                      onKeyPress={e => e.key === 'Enter' && handleAddMenuItem()}
                    />
                  </div>
                  <Button onClick={handleAddMenuItem} size='sm'>
                    <Search className='mr-2 h-4 w-4' />
                    Thêm món
                  </Button>
                </div>

                <div className='rounded-md border'>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Mã món</TableHead>
                        <TableHead>Tên món</TableHead>
                        <TableHead>Thao tác</TableHead>
                        <TableHead className='w-[50px]'></TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {menuItems.length === 0 ? (
                        <TableRow>
                          <TableCell colSpan={4} className='text-muted-foreground text-center'>
                            Chưa có món nào được thêm
                          </TableCell>
                        </TableRow>
                      ) : (
                        menuItems.map(item => (
                          <TableRow key={item.id}>
                            <TableCell className='font-medium'>{item.code}</TableCell>
                            <TableCell>{item.name}</TableCell>
                            <TableCell>
                              <Select
                                value={item.action}
                                onValueChange={(value: MenuItem['action']) =>
                                  handleActionChange(item.id, value)
                                }
                              >
                                <SelectTrigger className='w-[120px]'>
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value='UPDATE'>UPDATE</SelectItem>
                                  <SelectItem value='DELETE'>DELETE</SelectItem>
                                  <SelectItem value='ADD'>ADD</SelectItem>
                                </SelectContent>
                              </Select>
                            </TableCell>
                            <TableCell>
                              <Button
                                variant='ghost'
                                size='sm'
                                onClick={() => handleRemoveMenuItem(item.id)}
                              >
                                <Trash2 className='h-4 w-4' />
                              </Button>
                            </TableCell>
                          </TableRow>
                        ))
                      )}
                    </TableBody>
                  </Table>
                </div>
              </div>
            </form>
          </Form>

          {/* Menu Item Schedule Dialog */}
          <MenuItemScheduleDialog
            open={isDialogOpen}
            onOpenChange={setIsDialogOpen}
            onConfirm={handleDialogConfirm}
          />
        </div>

        <DialogFooter className='gap-2'>
          <Button variant='outline'>Thêm từ file</Button>
          <Button variant='outline' type='button' onClick={() => onOpenChange(false)}>
            Hủy
          </Button>
          <Button
            type='submit'
            disabled={isLoading}
            onClick={() => form.handleSubmit(handleSubmit)()}
          >
            Lưu
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
