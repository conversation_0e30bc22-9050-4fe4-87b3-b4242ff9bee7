'use client'

import { useEffect } from 'react'

import { z } from 'zod'

import { format } from 'date-fns'

import { useForm } from 'react-hook-form'

import { zodResolver } from '@hookform/resolvers/zod'

import { vi } from 'date-fns/locale'
import { CalendarIcon } from 'lucide-react'

import { cn } from '@/lib/utils'

import { useCitiesData, useStoresData } from '@/hooks/api'

import { Button } from '@/components/ui/button'
import { Calendar } from '@/components/ui/calendar'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select'

import type { MenuSchedule } from '../data'

const formSchema = z
  .object({
    time: z.date({ required_error: '<PERSON><PERSON><PERSON> bắt đầu là bắt buộc' }),
    end_time: z.date().optional(),
    city_uid: z.string().min(1, 'Thành phố là bắt buộc'),
    store_uid: z.string().min(1, 'Cửa hàng là bắt buộc')
  })
  .refine(data => !data.end_time || data.end_time >= data.time, {
    message: 'Ngày kết thúc phải sau ngày bắt đầu',
    path: ['end_time']
  })

type FormData = z.infer<typeof formSchema>

interface MenuScheduleFormProps {
  initialData?: Partial<MenuSchedule>
  onSubmit: (data: FormData) => void
  isLoading?: boolean
}

export function MenuScheduleForm({ initialData, onSubmit, isLoading }: MenuScheduleFormProps) {
  const { data: citiesData = [] } = useCitiesData()
  const { data: storesData = [] } = useStoresData()

  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      time: initialData?.time ? new Date(initialData.time) : undefined,
      end_time: initialData?.end_time ? new Date(initialData.end_time) : undefined,
      city_uid: initialData?.city_uid || '',
      store_uid: initialData?.store_uid || ''
    }
  })

  const selectedCityUid = form.watch('city_uid')

  const filteredStores = selectedCityUid
    ? storesData.filter(store => store.cityId === selectedCityUid)
    : []

  useEffect(() => {
    if (selectedCityUid && !initialData?.store_uid) {
      form.setValue('store_uid', '')
    }
  }, [selectedCityUid, form, initialData?.store_uid])

  const handleSubmit = (data: FormData) => {
    onSubmit(data)
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className='space-y-4'>
        <div className='grid grid-cols-2 gap-4'>
          <FormField
            control={form.control}
            name='time'
            render={({ field }) => (
              <FormItem className='flex flex-col'>
                <FormLabel>Từ ngày</FormLabel>
                <Popover>
                  <PopoverTrigger asChild>
                    <FormControl>
                      <Button
                        variant={'outline'}
                        className={cn(
                          'w-full pl-3 text-left font-normal',
                          !field.value && 'text-muted-foreground'
                        )}
                      >
                        {field.value ? (
                          format(field.value, 'dd/MM/yyyy', { locale: vi })
                        ) : (
                          <span>Chọn ngày</span>
                        )}
                        <CalendarIcon className='ml-auto h-4 w-4 opacity-50' />
                      </Button>
                    </FormControl>
                  </PopoverTrigger>
                  <PopoverContent className='w-auto p-0' align='start'>
                    <Calendar
                      mode='single'
                      selected={field.value}
                      onSelect={field.onChange}
                      disabled={date => date < new Date(new Date().setHours(0, 0, 0, 0))}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name='end_time'
            render={({ field }) => (
              <FormItem className='flex flex-col'>
                <FormLabel>Đến ngày (tùy chọn)</FormLabel>
                <Popover>
                  <PopoverTrigger asChild>
                    <FormControl>
                      <Button
                        variant={'outline'}
                        className={cn(
                          'w-full pl-3 text-left font-normal',
                          !field.value && 'text-muted-foreground'
                        )}
                      >
                        {field.value ? (
                          format(field.value, 'dd/MM/yyyy', { locale: vi })
                        ) : (
                          <span>Không giới hạn</span>
                        )}
                        <CalendarIcon className='ml-auto h-4 w-4 opacity-50' />
                      </Button>
                    </FormControl>
                  </PopoverTrigger>
                  <PopoverContent className='w-auto p-0' align='start'>
                    <Calendar
                      mode='single'
                      selected={field.value}
                      onSelect={field.onChange}
                      disabled={date => date < new Date(new Date().setHours(0, 0, 0, 0))}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className='grid grid-cols-2 gap-4'>
          <FormField
            control={form.control}
            name='city_uid'
            render={({ field }) => (
              <FormItem>
                <FormLabel>Thành phố</FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder='Chọn thành phố' />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {citiesData.map(city => (
                      <SelectItem key={city.id} value={city.id}>
                        {city.city_name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name='store_uid'
            render={({ field }) => (
              <FormItem>
                <FormLabel>Cửa hàng</FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder='Chọn cửa hàng' />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {filteredStores.map(store => (
                      <SelectItem key={store.id} value={store.id}>
                        {store.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className='flex justify-end space-x-2'>
          <Button type='submit' disabled={isLoading}>
            {isLoading ? 'Đang xử lý...' : initialData ? 'Cập nhật' : 'Tạo mới'}
          </Button>
        </div>
      </form>
    </Form>
  )
}
