import { Row } from '@tanstack/react-table'

import { MoreHorizontal, Pen, Trash2 } from 'lucide-react'

import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu'

import { useMenuSchedule } from '../context'
import { MenuSchedule } from '../data'

interface MenuScheduleRowActionsProps {
  row: Row<MenuSchedule>
}

export function MenuScheduleRowActions({ row }: MenuScheduleRowActionsProps) {
  const { setOpen, setCurrentRow } = useMenuSchedule()
  const schedule = row.original

  const handleEdit = () => {
    setCurrentRow(schedule)
    setOpen('edit')
  }

  const handleDelete = () => {
    setCurrentRow(schedule)
    setOpen('delete')
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant='ghost' className='data-[state=open]:bg-muted flex h-8 w-8 p-0'>
          <MoreHorizontal className='h-4 w-4' />
          <span className='sr-only'>Mở menu</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align='end' className='w-[160px]'>
        <DropdownMenuItem onClick={handleEdit}>
          <Pen className='mr-2 h-4 w-4' />
          Chỉnh sửa
        </DropdownMenuItem>
        <DropdownMenuSeparator />
        <DropdownMenuItem onClick={handleDelete} className='text-red-600'>
          <Trash2 className='mr-2 h-4 w-4' />
          Xóa
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
