'use client'

import { useState } from 'react'

import { Button } from '@/components/ui/button'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select'

interface MenuItem {
  id: string
  code: string
  name: string
  action: 'ADD' | 'UPDATE' | 'DELETE'
}

interface MenuItemScheduleDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onConfirm: (items: MenuItem[]) => void
}

export function MenuItemScheduleDialog({
  open,
  onOpenChange,
  onConfirm
}: MenuItemScheduleDialogProps) {
  const [selectedAction, setSelectedAction] = useState<string>('')

  // Mock data for demonstration - replace with actual menu items data
  const menuActions = [
    { value: 'ADD', label: 'Tạo món' },
    { value: 'UPDATE', label: 'Sửa món' },
    { value: 'DELETE', label: 'Xóa món' }
  ]

  const handleConfirm = () => {
    if (selectedAction) {
      // Create mock menu items based on selected action
      const mockItems: MenuItem[] = [
        {
          id: Date.now().toString(),
          code: `ITEM-${Date.now()}`,
          name: `Món ${selectedAction.toLowerCase()}`,
          action: selectedAction as MenuItem['action']
        }
      ]
      onConfirm(mockItems)
      onOpenChange(false)
      setSelectedAction('')
    }
  }

  const handleCancel = () => {
    onOpenChange(false)
    setSelectedAction('')
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className='sm:max-w-[600px]'>
        <DialogHeader>
          <DialogTitle className='text-center text-lg font-medium'>Tạo lịch cho món</DialogTitle>
        </DialogHeader>

        <div className='space-y-4'>
          {/* Thông tin cơ bản */}
          <div>
            <h3 className='mb-3 text-sm font-medium'>Thông tin cơ bản</h3>

            <div className='grid grid-cols-2 gap-4'>
              {/* Thao tác */}
              <div className='space-y-2'>
                <div className='bg-blue-100 p-3 text-sm font-medium text-blue-900'>Thao tác</div>
                <div className='space-y-2'>
                  <div
                    className={`cursor-pointer rounded p-2 text-sm transition-colors ${
                      selectedAction === 'ADD' ? 'bg-blue-50 text-blue-700' : 'hover:bg-gray-50'
                    }`}
                    onClick={() => setSelectedAction('ADD')}
                  >
                    Tạo món
                  </div>
                  <div
                    className={`cursor-pointer rounded p-2 text-sm transition-colors ${
                      selectedAction === 'UPDATE' ? 'bg-blue-50 text-blue-700' : 'hover:bg-gray-50'
                    }`}
                    onClick={() => setSelectedAction('UPDATE')}
                  >
                    Sửa món
                  </div>
                  <div
                    className={`cursor-pointer rounded p-2 text-sm transition-colors ${
                      selectedAction === 'DELETE' ? 'bg-blue-50 text-blue-700' : 'hover:bg-gray-50'
                    }`}
                    onClick={() => setSelectedAction('DELETE')}
                  >
                    Xóa món
                  </div>
                </div>
              </div>

              {/* Tìm kiếm */}
              <div className='space-y-2'>
                <div className='bg-gray-100 p-3 text-sm font-medium'>Tìm kiếm</div>
                <div className='space-y-2'>
                  <Select>
                    <SelectTrigger className='w-full'>
                      <SelectValue placeholder='Tìm kiếm món ăn...' />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value='pho'>Phở Bò</SelectItem>
                      <SelectItem value='bun-bo'>Bún Bò Huế</SelectItem>
                      <SelectItem value='com-tam'>Cơm Tấm</SelectItem>
                      <SelectItem value='banh-mi'>Bánh Mì</SelectItem>
                      <SelectItem value='che'>Chè</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>
          </div>

          {/* Preview selected action */}
          {selectedAction && (
            <div className='rounded-md border p-3'>
              <p className='text-muted-foreground text-sm'>
                Thao tác đã chọn:{' '}
                <span className='text-foreground font-medium'>
                  {menuActions.find(action => action.value === selectedAction)?.label}
                </span>
              </p>
            </div>
          )}
        </div>

        <DialogFooter className='gap-2'>
          <Button variant='outline' onClick={handleCancel}>
            Hủy
          </Button>
          <Button
            onClick={handleConfirm}
            disabled={!selectedAction}
            className='bg-blue-600 hover:bg-blue-700'
          >
            Xong
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
