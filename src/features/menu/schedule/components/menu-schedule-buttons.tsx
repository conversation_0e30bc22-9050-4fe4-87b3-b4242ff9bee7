import { Plus } from 'lucide-react'

import { But<PERSON> } from '@/components/ui/button'

import { useMenuSchedule } from '../context'

export function MenuScheduleButtons() {
  const { setOpen } = useMenuSchedule()

  const handleCreate = () => {
    setOpen('create')
  }

  return (
    <div className='flex items-center gap-2'>
      <Button onClick={handleCreate} size='sm' className='h-8'>
        <Plus className='mr-2 h-4 w-4' />
        Tạo lịch
      </Button>
    </div>
  )
}
