import { useState } from 'react'

import { toast } from 'sonner'

interface ApiItem {
  id: string
  item_id: string
  item_name: string
  ots_price: number
}

interface MenuItem {
  id: string
  name: string
  price: number
  code?: string
  size?: string
}

interface UseMenuItemSelectionOptions {
  onConfirm?: (newItems: MenuItem[]) => void
}

export function useMenuItemSelection(options: UseMenuItemSelectionOptions = {}) {
  const [selectedMenuItems, setSelectedMenuItems] = useState<Set<string>>(new Set())
  const [menuItemSearchTerm, setMenuItemSearchTerm] = useState('')
  const [selectedMenuSectionOpen, setSelectedMenuSectionOpen] = useState(true)
  const [remainingMenuSectionOpen, setRemainingMenuSectionOpen] = useState(true)

  const handleMenuItemToggle = (itemId: string) => {
    const newSelected = new Set(selectedMenuItems)
    if (newSelected.has(itemId)) {
      newSelected.delete(itemId)
    } else {
      newSelected.add(itemId)
    }
    setSelectedMenuItems(newSelected)
  }

  const handleConfirmMenuItems = (items: ApiItem[], currentMenuItems: MenuItem[]) => {
    console.log('🔄 DEBUG handleConfirmMenuItems:')
    console.log(
      '📥 Input items:',
      items.map(item => ({ id: item.id, name: item.item_name }))
    )
    console.log('📋 Current menuItems:', currentMenuItems)
    console.log('✅ Selected items in modal:', Array.from(selectedMenuItems))

    // Get all selected items from modal
    const selectedItems = items.filter(item => selectedMenuItems.has(item.id))
    console.log(
      '🎯 Filtered selected items:',
      selectedItems.map(item => ({ id: item.id, name: item.item_name }))
    )

    // Convert to MenuItem format
    const updatedMenuItems = selectedItems.map(item => ({
      id: item.id,
      name: item.item_name,
      price: item.ots_price,
      code: item.item_id
    }))

    console.log('📦 Final updated menuItems:', updatedMenuItems)

    // Calculate changes for toast messages
    const currentItemIds = new Set(currentMenuItems.map(item => item.id))
    const selectedItemIds = new Set(selectedItems.map(item => item.id))

    const addedItems = selectedItems.filter(item => !currentItemIds.has(item.id))
    const removedItems = currentMenuItems.filter(item => !selectedItemIds.has(item.id))

    console.log(
      '➕ Added items:',
      addedItems.map(item => ({ id: item.id, name: item.item_name }))
    )
    console.log('➖ Removed items:', removedItems)

    options.onConfirm?.(updatedMenuItems)

    // Reset state
    resetSelection()

    // Show success toast
    toast.success('Cập nhật món thành công')

    return updatedMenuItems
  }

  const resetSelection = () => {
    setSelectedMenuItems(new Set())
    setMenuItemSearchTerm('')
    setSelectedMenuSectionOpen(true)
    setRemainingMenuSectionOpen(true)
  }

  const setSelectedMenuItemsFromGroup = (groupMenuItems: MenuItem[]) => {
    console.log('🔄 DEBUG setSelectedMenuItemsFromGroup:')
    console.log('📥 Input groupMenuItems:', groupMenuItems)

    const itemIds = new Set(groupMenuItems.map(item => item.id))
    console.log('🆔 Extracted itemIds:', Array.from(itemIds))

    setSelectedMenuItems(itemIds)
    console.log('✅ Updated selectedMenuItems to:', Array.from(itemIds))
  }

  const getFilteredMenuItems = (items: ApiItem[]) => {
    return items.filter(item =>
      item.item_name.toLowerCase().includes(menuItemSearchTerm.toLowerCase())
    )
  }

  const getSelectedMenuItemsList = (items: ApiItem[]) => {
    const filtered = getFilteredMenuItems(items)
    const selected = filtered.filter(item => selectedMenuItems.has(item.id))

    console.log('📊 DEBUG getSelectedMenuItemsList:')
    console.log(
      '🔍 All filtered items:',
      filtered.map(item => ({ id: item.id, name: item.item_name }))
    )
    console.log(
      '✅ Selected items:',
      selected.map(item => ({ id: item.id, name: item.item_name }))
    )
    console.log('🎯 Current selectedMenuItems Set:', Array.from(selectedMenuItems))

    return selected
  }

  const getRemainingMenuItemsList = (items: ApiItem[]) => {
    const filtered = getFilteredMenuItems(items)
    const remaining = filtered.filter(item => !selectedMenuItems.has(item.id))

    console.log('📊 DEBUG getRemainingMenuItemsList:')
    console.log(
      '❌ Remaining items:',
      remaining.map(item => ({ id: item.id, name: item.item_name }))
    )

    return remaining
  }

  return {
    // State
    selectedMenuItems,
    menuItemSearchTerm,
    selectedMenuSectionOpen,
    remainingMenuSectionOpen,

    // Actions
    setMenuItemSearchTerm,
    setSelectedMenuSectionOpen,
    setRemainingMenuSectionOpen,
    handleMenuItemToggle,
    handleConfirmMenuItems,
    resetSelection,
    setSelectedMenuItemsFromGroup,

    // Computed
    getFilteredMenuItems,
    getSelectedMenuItemsList,
    getRemainingMenuItemsList,
    hasSelectedItems: selectedMenuItems.size > 0
  }
}
