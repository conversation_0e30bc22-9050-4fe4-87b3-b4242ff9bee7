import { useState } from 'react'

import { useNavigate } from '@tanstack/react-router'

import { ExistingCustomization } from '@/types/customizations'
import { toast } from 'sonner'

import { useCreateCustomization, useUpdateCustomization } from '@/hooks/api'

interface UseCustomizationFormOptions {
  onSuccess?: () => void
  isEdit?: boolean
  customizationId?: string
}

interface CustomizationGroup {
  id: string
  name: string
  minRequired: number
  maxAllowed: number
  items: Array<{
    id: string
    name: string
    price: number
    code?: string
    size?: string
  }>
}

export function useCustomizationForm(options: UseCustomizationFormOptions = {}) {
  const navigate = useNavigate()
  const [customizationName, setCustomizationName] = useState('')
  const [selectedStoreId, setSelectedStoreId] = useState<string>('')
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [existingCustomization, setExistingCustomization] = useState<ExistingCustomization | null>(
    null
  )

  const createCustomizationMutation = useCreateCustomization()
  const updateCustomizationMutation = useUpdateCustomization()

  const handleBack = () => {
    navigate({ to: '/menu/customization/customization-in-store' })
  }

  const handleSave = async (
    customizationGroups: CustomizationGroup[],
    selectedDishes: Set<string>,
    items: Array<{ id: string; item_id: string }>
  ) => {
    if (!customizationName.trim()) {
      toast.error('Vui lòng nhập tên customization')
      return
    }

    if (!selectedStoreId) {
      toast.error('Vui lòng chọn cửa hàng')
      return
    }

    if (customizationGroups.length === 0) {
      toast.error('Vui lòng tạo ít nhất một nhóm')
      return
    }

    if (selectedDishes.size === 0) {
      toast.error('Vui lòng chọn ít nhất một món áp dụng')
      return
    }

    setIsSubmitting(true)
    try {
      const lstItemOptions = customizationGroups.map(group => ({
        LstItem_Id: group.items.map(item => item.code || item.id),
        Min_Permitted: group.minRequired,
        Max_Permitted: group.maxAllowed,
        Name: group.name,
        id: group.id.startsWith('CUS_GROUP_')
          ? group.id
          : `CUS_GROUP_${Math.random().toString(36).substring(2, 7).toUpperCase()}`
      }))

      const groupItemCodes = customizationGroups.flatMap(group =>
        group.items.map(item => item.code || item.id)
      )

      const selectedDishCodes = Array.from(selectedDishes).map(dishId => {
        const originalItem = items.find(apiItem => apiItem.id === dishId)
        return originalItem?.item_id || dishId
      })

      const allItemIds = [...new Set([...groupItemCodes, ...selectedDishCodes])]

      const payload = {
        name: customizationName.trim(),
        storeUid: selectedStoreId, // Use storeUid instead of cityUid
        data: {
          LstItem_Options: lstItemOptions
        },
        listItem: allItemIds,
        sort: 1000,
        isUpdateSameCustomization: false
      }

      if (options.isEdit && options.customizationId) {
        // Update existing customization
        await updateCustomizationMutation.mutateAsync({
          customizationId: options.customizationId,
          existingCustomization: existingCustomization || undefined,
          ...payload
        })
        toast.success('Đã cập nhật customization thành công!')
      } else {
        // Create new customization
        await createCustomizationMutation.mutateAsync(payload)
        toast.success('Đã tạo customization thành công!')
      }

      options.onSuccess?.()
      navigate({ to: '/menu/customization/customization-in-store' })
    } catch {
      const action = options.isEdit ? 'cập nhật' : 'tạo'
      toast.error(`Lỗi khi ${action} customization. Vui lòng thử lại.`)
    } finally {
      setIsSubmitting(false)
    }
  }

  return {
    // State
    customizationName,
    selectedStoreId,
    isSubmitting,

    // Actions
    setCustomizationName,
    setSelectedStoreId,
    setExistingCustomization,
    handleBack,
    handleSave,

    // Computed
    isFormValid: customizationName.trim() && selectedStoreId
  }
}
