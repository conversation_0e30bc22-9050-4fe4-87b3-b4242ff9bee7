import { ParsedCustomizationData } from '@/types/customizations'
import { toast } from 'sonner'

export function useExcelParser() {
  const parseExcelFile = async (
    file: File,
    _stores: unknown[] = [],
    isImport = false
  ): Promise<ParsedCustomizationData[]> => {
    try {
      const XLSX = await import('xlsx')

      const data = await file.arrayBuffer()
      const workbook = XLSX.read(data, { type: 'array' })
      const sheetName = workbook.SheetNames[0]
      const worksheet = workbook.Sheets[sheetName]
      const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 })

      // Parse data according to format: Row 1: headers, Row 2+: data
      const parsedCustomizations: ParsedCustomizationData[] = []

      for (let i = 1; i < jsonData.length; i++) {
        const row = jsonData[i] as unknown[]

        if (isImport) {
          // Import format: <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> món á<PERSON> dụ<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> c<PERSON> ch<PERSON>, <PERSON><PERSON><PERSON><PERSON>ọ<PERSON>, <PERSON><PERSON> món theo nhóm
          if (row && row.length >= 1) {
            const parsed = {
              id: String(row[0] || '').trim(), // ID from Excel
              name: String(row[1] || '').trim(),
              cityName: '', // Not needed for store-based import
              storeName: String(row[2] || '').trim(), // Store name from Excel
              appliedItemCodes: String(row[3] || '').trim(),
              groupName: String(row[4] || '').trim(),
              minRequired: Number(row[5]) || 0,
              maxAllowed: Number(row[6]) || 0,
              groupItemCodes: String(row[7] || '').trim()
            }

            parsedCustomizations.push(parsed)
          }
        } else {
          if (row && row.length >= 6) {
            parsedCustomizations.push({
              id: '', // ID will be generated for export data
              name: String(row[0] || '').trim(),
              cityName: '', // Not included in store-based export
              storeName: '', // Will be populated from current store
              appliedItemCodes: String(row[1] || '').trim(),
              groupName: String(row[2] || '').trim(),
              minRequired: Number(row[3]) || 0,
              maxAllowed: Number(row[4]) || 0,
              groupItemCodes: String(row[5] || '').trim()
            })
          }
        }
      }

      return parsedCustomizations
    } catch (error) {
      toast.error('Lỗi khi đọc file Excel. Vui lòng kiểm tra định dạng file.')
      throw error
    }
  }

  const downloadTemplate = (storeName?: string) => {
    // Create template Excel file
    const templateData = [
      [
        'ID',
        'Tên',
        'Cửa hàng',
        'Mã món áp dụng',
        'Tên nhóm',
        'Yêu cầu chọn',
        'Giới hạn chọn',
        'Mã món theo nhóm'
      ],
      [
        '',
        'TEST',
        storeName || 'A Hưng',
        'ITEM-1F93,ITEM-6BY2',
        'TEST',
        '0',
        '0',
        'ITEM-1F93,ITEM-6BY2'
      ]
    ]

    // Dynamic import for xlsx
    import('xlsx').then(XLSX => {
      const workbook = XLSX.utils.book_new()
      const worksheet = XLSX.utils.aoa_to_sheet(templateData)
      XLSX.utils.book_append_sheet(workbook, worksheet, 'Template')
      XLSX.writeFile(workbook, 'create-customize-store.xlsx')
      toast.success('Đã tải file mẫu thành công!')
    })
  }

  return {
    parseExcelFile,
    downloadTemplate
  }
}
