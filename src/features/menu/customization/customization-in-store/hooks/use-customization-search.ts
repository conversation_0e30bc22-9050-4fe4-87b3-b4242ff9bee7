import { useState, useEffect } from 'react'

import { usePosStores } from '@/stores/posStore'

export function useCustomizationSearch() {
  const [searchTerm, setSearchTerm] = useState('')
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedStoreId, setSelectedStoreId] = useState<string>('')

  const { currentBrandStores } = usePosStores()

  // Initialize with first store if available and no selection made
  useEffect(() => {
    if (!selectedStoreId && currentBrandStores.length > 0) {
      setSelectedStoreId(currentBrandStores[0].id)
    }
  }, [currentBrandStores, selectedStoreId])

  // Prepare store UIDs for API call (always single store)
  const listStoreUid = selectedStoreId ? [selectedStoreId] : []

  const handleSearchSubmit = () => {
    setSearchTerm(searchQuery)
  }

  const handleSearchKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault()
      handleSearchSubmit()
    }
  }

  return {
    searchTerm,
    searchQuery,
    setSearchQuery,
    selectedStoreId,
    setSelectedStoreId,
    listStoreUid,
    stores: currentBrandStores,
    handleSearchSubmit,
    handleSearchKeyDown
  }
}
