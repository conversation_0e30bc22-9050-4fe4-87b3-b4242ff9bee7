import { toast } from 'sonner'
import { useDeleteCustomization } from '@/hooks/api'
import { Customization } from '@/types/customizations'

export function useCustomizationDelete() {
  const deleteCustomizationMutation = useDeleteCustomization()

  const handleDeleteCustomization = async (customization: Customization) => {
    try {
      await deleteCustomizationMutation.mutateAsync(customization.id)
      toast.success('Xóa customization thành công')
      return true
    } catch (error) {
      console.error('Delete customization error:', error)
      toast.error('Có lỗi xảy ra khi xóa customization')
      return false
    }
  }

  return {
    handleDeleteCustomization,
    isLoading: deleteCustomizationMutation.isPending,
  }
}
