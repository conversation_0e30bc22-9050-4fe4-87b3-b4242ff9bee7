import { PosModal } from '@/components/pos'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui'
import { Customization } from '@/types/customizations'

interface CopyCustomizationModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  selectedCustomization: Customization | null
  copyName: string
  onCopyNameChange: (name: string) => void
  onCancel: () => void
  onConfirm: () => void
  isLoading: boolean
}

export function CopyCustomizationModal({
  open,
  onOpenChange,
  selectedCustomization,
  copyName,
  onCopyNameChange,
  onCancel,
  onConfirm,
  isLoading,
}: CopyCustomizationModalProps) {
  return (
    <PosModal
      title='Sao chép customization'
      open={open}
      onOpenChange={onOpenChange}
      onCancel={onCancel}
      onConfirm={onConfirm}
      confirmText='Sao chép'
      cancelText='Hủy'
      isLoading={isLoading}
    >
      <div className='space-y-4'>
        <div>
          <Label htmlFor='copy-name'>Tên customization mới</Label>
          <Input
            id='copy-name'
            value={copyName}
            onChange={e => onCopyNameChange(e.target.value)}
            placeholder='Nhập tên customization'
            className='mt-1'
          />
        </div>
        {selectedCustomization && (
          <div className='text-sm text-gray-600'>
            Sao chép từ: <span className='font-medium'>{selectedCustomization.name}</span>
          </div>
        )}
      </div>
    </PosModal>
  )
}
