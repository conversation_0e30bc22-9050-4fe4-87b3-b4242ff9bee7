import { useNavigate } from '@tanstack/react-router'

import { Customization } from '@/types/customizations'

import { useCustomizationsData } from '@/hooks/api'

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Button,
  Input,
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui'

import {
  customizationColumns,
  CustomizationDataTable,
  DeleteCustomizationModal,
  CopyCustomizationModal,
  ExportCustomizationModal,
  ImportCustomizationModal
} from './components'
import {
  useCustomizationSearch,
  useCustomizationModals,
  useCustomizationCopy,
  useCustomizationDelete,
  useCustomizationExport,
  useCustomizationImport
} from './hooks'

export default function CustomizationInCityPage() {
  const navigate = useNavigate()

  const search = useCustomizationSearch()
  const modals = useCustomizationModals()
  const copy = useCustomizationCopy()
  const deleteHook = useCustomizationDelete()
  const exportHook = useCustomizationExport()
  const importHook = useCustomizationImport()

  const {
    data: customizations,
    isLoading,
    error
  } = useCustomizationsData({
    searchTerm: search.searchTerm || undefined,
    listCityUid: search.listCityUid
  })

  const handleCopyCustomization = (customization: Customization) => {
    copy.initializeCopyName(customization)
    modals.openCopyModal(customization)
  }

  const handleDeleteCustomization = (customization: Customization) => {
    modals.openDeleteModal(customization)
  }

  const handleConfirmCopy = async () => {
    if (!modals.selectedCustomization) return
    const success = await copy.handleConfirmCopy(modals.selectedCustomization)
    if (success) modals.closeCopyModal()
  }

  const handleConfirmDelete = async () => {
    if (!modals.selectedCustomization) return
    const success = await deleteHook.handleConfirmDelete(modals.selectedCustomization)
    if (success) modals.closeDeleteModal()
  }

  const handleExportCustomizations = () => {
    exportHook.resetExportState()
    modals.openExportModal()
  }

  const handleCloseExportModal = () => {
    exportHook.resetExportState()
    modals.closeExportModal()
  }

  const handleSaveImportedData = async () => {
    const success = await exportHook.handleSaveImportedData()
    if (success) modals.closeExportModal()
  }

  const handleImportCustomizations = () => {
    importHook.resetImportState()
    modals.openImportModal()
  }

  const handleCloseImportModal = () => {
    importHook.resetImportState()
    modals.closeImportModal()
  }

  const handleSaveImportedCustomizations = async () => {
    const success = await importHook.handleSaveImportedCustomizations()
    if (success) modals.closeImportModal()
  }

  const handleCreateCustomization = () => {
    navigate({ to: '/menu/customization/customization-in-city/detail' })
  }

  const handleEditCustomization = (customization: Customization) => {
    navigate({
      to: '/menu/customization/customization-in-city/detail/$customizationId',
      params: { customizationId: customization.id }
    })
  }

  const columns = customizationColumns({
    onCopyCustomization: handleCopyCustomization,
    onDeleteCustomization: handleDeleteCustomization
  })

  return (
    <div className='container mx-auto px-4 py-8'>
      <div className='mb-6 flex items-center justify-between'>
        <div className='flex items-center gap-4'>
          <h2 className='text-xl font-semibold'>Customization</h2>
          <Input
            placeholder='Tìm kiếm customization'
            className='w-64'
            value={search.searchQuery}
            onChange={e => search.setSearchQuery(e.target.value)}
            onKeyDown={search.handleSearchKeyDown}
          />
          <Select value={search.selectedCityId} onValueChange={search.setSelectedCityId}>
            <SelectTrigger className='w-48'>
              <SelectValue placeholder='Chọn thành phố' />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value='all'>Tất cả thành phố</SelectItem>
              {search.cities.map(city => (
                <SelectItem key={city.id} value={city.id}>
                  {city.city_name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <div className='flex items-center gap-4'>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button size='sm' variant='outline'>
                Tiện ích
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align='end'>
              <DropdownMenuItem onClick={handleExportCustomizations}>
                Xuất, Chi tiết customization
              </DropdownMenuItem>
              <DropdownMenuItem onClick={handleImportCustomizations}>
                Thêm customization từ file
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
          <Button size='sm' variant='default' onClick={handleCreateCustomization}>
            Tạo customization
          </Button>
        </div>
      </div>

      {error && (
        <div className='py-8 text-center'>
          <p className='text-red-600'>Có lỗi xảy ra khi tải customization</p>
        </div>
      )}

      {!isLoading && !error && (
        <CustomizationDataTable
          columns={columns}
          data={customizations || []}
          isLoading={isLoading}
          onRowClick={handleEditCustomization}
        />
      )}

      <DeleteCustomizationModal
        open={modals.confirmModalOpen}
        onOpenChange={modals.setConfirmModalOpen}
        selectedCustomization={modals.selectedCustomization}
        onCancel={modals.closeDeleteModal}
        onConfirm={handleConfirmDelete}
        isLoading={deleteHook.isLoading}
      />

      <CopyCustomizationModal
        open={modals.copyModalOpen}
        onOpenChange={modals.setCopyModalOpen}
        selectedCustomization={modals.selectedCustomization}
        copyName={copy.copyName}
        onCopyNameChange={copy.setCopyName}
        onCancel={() => {
          copy.resetCopyName()
          modals.closeCopyModal()
        }}
        onConfirm={handleConfirmCopy}
        isLoading={copy.isLoading}
      />

      <ExportCustomizationModal
        open={modals.exportModalOpen}
        onOpenChange={modals.setExportModalOpen}
        showParsedData={exportHook.showParsedData}
        exportCityId={exportHook.exportCityId}
        onExportCityIdChange={exportHook.setExportCityId}
        cities={exportHook.cities}
        selectedFile={exportHook.selectedFile}
        parsedData={exportHook.parsedData}
        isExporting={exportHook.isExporting}
        isSaving={exportHook.isSaving}
        onCancel={handleCloseExportModal}
        onConfirm={exportHook.showParsedData ? handleSaveImportedData : handleCloseExportModal}
        onDownloadExportFile={exportHook.handleDownloadExportFile}
        onUploadFile={exportHook.handleUploadFile}
      />

      <ImportCustomizationModal
        open={modals.importModalOpen}
        onOpenChange={modals.setImportModalOpen}
        showImportParsedData={importHook.showImportParsedData}
        importSelectedFile={importHook.importSelectedFile}
        importParsedData={importHook.importParsedData}
        isLoading={importHook.isLoading}
        onCancel={handleCloseImportModal}
        onConfirm={
          importHook.showImportParsedData
            ? handleSaveImportedCustomizations
            : handleCloseImportModal
        }
        onDownloadTemplate={importHook.handleDownloadTemplate}
        onImportFileUpload={importHook.handleImportFileUpload}
      />

      <input
        type='file'
        ref={exportHook.fileInputRef}
        onChange={exportHook.handleFileChange}
        accept='.xlsx,.xls'
        style={{ display: 'none' }}
      />
      <input
        type='file'
        ref={importHook.importFileInputRef}
        onChange={importHook.handleImportFileChange}
        accept='.xlsx,.xls'
        style={{ display: 'none' }}
      />
    </div>
  )
}
