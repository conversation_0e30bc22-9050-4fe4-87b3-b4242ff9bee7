import { X } from 'lucide-react'
import { Button } from '@/components/ui'

interface CustomizationHeaderProps {
  onBack: () => void
  onSave: () => void
  isSubmitting: boolean
  isFormValid: boolean
  title?: string
  saveButtonText?: string
  submittingText?: string
}

export function CustomizationHeader({
  onBack,
  onSave,
  isSubmitting,
  isFormValid,
  title = "Tạo customization",
  saveButtonText = "Lưu",
  submittingText = "Đang tạo..."
}: CustomizationHeaderProps) {
  return (
    <div className='mb-8'>
      <div className='mb-4 flex items-center justify-between'>
        <Button
          variant='ghost'
          size='sm'
          onClick={onBack}
          className='flex items-center'
        >
          <X className='h-4 w-4' />
        </Button>
        <Button
          type='button'
          disabled={isSubmitting || !isFormValid}
          className='min-w-[100px]'
          onClick={onSave}
        >
          {isSubmitting ? submittingText : saveButtonText}
        </Button>
      </div>

      <div className='text-center'>
        <h1 className='mb-2 text-3xl font-bold'>{title}</h1>
      </div>
    </div>
  )
}
