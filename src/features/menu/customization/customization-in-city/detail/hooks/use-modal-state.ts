import { useState } from 'react'
import { toast } from 'sonner'

export function useModalState() {
  const [createGroupModalOpen, setCreateGroupModalOpen] = useState(false)
  const [addItemModalOpen, setAddItemModalOpen] = useState(false)
  const [dishModalOpen, setDishModalOpen] = useState(false)

  const handleCloseModal = () => {
    setCreateGroupModalOpen(false)
  }

  const handleCloseAddItemModal = () => {
    setAddItemModalOpen(false)
  }

  const handleCloseDishModal = () => {
    setDishModalOpen(false)
  }

  const handleAddMenuItem = (selectedCityId: string) => {
    if (!selectedCityId) {
      toast.error('Vui lòng chọn thành phố trước')
      return false
    }
    setAddItemModalOpen(true)
    return true
  }

  const handleOpenDishModal = (isLoadingItems: boolean) => {
    if (isLoadingItems) {
      return false
    }
    setDishModalOpen(true)
    return true
  }

  return {
    // State
    createGroupModalOpen,
    addItemModalOpen,
    dishModalOpen,
    
    // Actions
    setCreateGroupModalOpen,
    setAddItemModalOpen,
    setDishModalOpen,
    handleCloseModal,
    handleCloseAddItemModal,
    handleCloseDishModal,
    handleAddMenuItem,
    handleOpenDishModal,
  }
}
