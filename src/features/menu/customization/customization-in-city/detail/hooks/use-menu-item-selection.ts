import { useState } from 'react'

import { toast } from 'sonner'

interface ApiItem {
  id: string
  item_id: string
  item_name: string
  ots_price: number
}

interface MenuItem {
  id: string
  name: string
  price: number
  code?: string
  size?: string
}

interface UseMenuItemSelectionOptions {
  onConfirm?: (newItems: MenuItem[]) => void
}

export function useMenuItemSelection(options: UseMenuItemSelectionOptions = {}) {
  const [selectedMenuItems, setSelectedMenuItems] = useState<Set<string>>(new Set())
  const [menuItemSearchTerm, setMenuItemSearchTerm] = useState('')
  const [selectedMenuSectionOpen, setSelectedMenuSectionOpen] = useState(true)
  const [remainingMenuSectionOpen, setRemainingMenuSectionOpen] = useState(true)

  const handleMenuItemToggle = (itemId: string) => {
    const newSelected = new Set(selectedMenuItems)
    if (newSelected.has(itemId)) {
      newSelected.delete(itemId)
    } else {
      newSelected.add(itemId)
    }
    setSelectedMenuItems(newSelected)
  }

  const handleConfirmMenuItems = (items: ApiItem[], currentMenuItems: MenuItem[]) => {
    const selectedItems = items.filter(item => selectedMenuItems.has(item.id))
    const existingItemIds = new Set(currentMenuItems.map(item => item.id))
    const newItems = selectedItems.filter(item => !existingItemIds.has(item.id))

    const newMenuItems = newItems.map(item => ({
      id: item.id,
      name: item.item_name,
      price: item.ots_price
    }))

    const updatedMenuItems = [...currentMenuItems, ...newMenuItems]
    options.onConfirm?.(updatedMenuItems)

    // Reset state
    resetSelection()

    if (newItems.length > 0) {
      toast.success(`Đã thêm ${newItems.length} món`)
    } else {
      toast.info('Các món đã được thêm trước đó')
    }

    return updatedMenuItems
  }

  const resetSelection = () => {
    setSelectedMenuItems(new Set())
    setMenuItemSearchTerm('')
    setSelectedMenuSectionOpen(true)
    setRemainingMenuSectionOpen(true)
  }

  const getFilteredMenuItems = (items: ApiItem[]) => {
    return items.filter(item =>
      item.item_name.toLowerCase().includes(menuItemSearchTerm.toLowerCase())
    )
  }

  const getSelectedMenuItemsList = (items: ApiItem[]) => {
    const filtered = getFilteredMenuItems(items)
    return filtered.filter(item => selectedMenuItems.has(item.id))
  }

  const getRemainingMenuItemsList = (items: ApiItem[]) => {
    const filtered = getFilteredMenuItems(items)
    return filtered.filter(item => !selectedMenuItems.has(item.id))
  }

  return {
    // State
    selectedMenuItems,
    menuItemSearchTerm,
    selectedMenuSectionOpen,
    remainingMenuSectionOpen,

    // Actions
    setMenuItemSearchTerm,
    setSelectedMenuSectionOpen,
    setRemainingMenuSectionOpen,
    handleMenuItemToggle,
    handleConfirmMenuItems,
    resetSelection,

    // Computed
    getFilteredMenuItems,
    getSelectedMenuItemsList,
    getRemainingMenuItemsList,
    hasSelectedItems: selectedMenuItems.size > 0
  }
}
