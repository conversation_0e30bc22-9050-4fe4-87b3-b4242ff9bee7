import { useEffect } from 'react'

import { useParams } from '@tanstack/react-router'

import { MissingIdError, LoadingError, LoadingSpinner } from '@/components'
import { ExistingCustomization } from '@/types/customizations'

import { useItemsData, useCustomizationById } from '@/hooks/api'
import { usePosData } from '@/hooks/use-pos-data'

import {
  Label,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Button,
  Input
} from '@/components/ui'

import {
  CreateGroupModal,
  AddMenuItemModal,
  DishSelectionModal,
  CustomizationHeader
} from './components'
import {
  useCustomizationForm,
  useGroupManagement,
  useMenuItemSelection,
  useDishSelection,
  useModalState
} from './hooks'

export default function CustomizationDetailPage() {
  const params = useParams({
    from: '/_authenticated/menu/customization/customization-in-city/detail'
  })

  const customizationId = undefined

  const customizationForm = useCustomizationForm({
    isEdit: !!customizationId,
    customizationId: customizationId || ''
  })
  const groupManagement = useGroupManagement()
  const menuItemSelection = useMenuItemSelection({
    onConfirm: newItems => groupManagement.setMenuItems(newItems)
  })
  const dishSelection = useDishSelection()
  const modalState = useModalState()

  const { cities } = usePosData()
  const {
    data: existingCustomization,
    isLoading: isLoadingCustomization,
    error: customizationError
  } = useCustomizationById(customizationId || '', !!customizationId)

  const {
    data: items = [],
    isLoading: isLoadingItems,
    error: itemsError
  } = useItemsData({
    params: {
      city_uid: customizationForm.selectedCityId,
      skip_limit: true,
      active: 1
    },
    enabled: !!customizationForm.selectedCityId
  })

  useEffect(() => {
    if (existingCustomization && !isLoadingCustomization) {
      customizationForm.setExistingCustomization(
        existingCustomization as unknown as ExistingCustomization
      )
      customizationForm.setCustomizationName(existingCustomization.name)
      customizationForm.setSelectedCityId(existingCustomization.cityUid || '')

      if (existingCustomization.data?.LstItem_Options) {
        const groups = existingCustomization.data.LstItem_Options.map(option => ({
          id: option.id,
          name: option.Name,
          minRequired: option.Min_Permitted,
          maxAllowed: option.Max_Permitted,
          items: option.LstItem_Id.map(itemId => ({
            id: itemId,
            name: itemId,
            price: 0,
            code: itemId
          }))
        }))
        groupManagement.setCustomizationGroups(groups)
      }
    }
  }, [existingCustomization, isLoadingCustomization])

  useEffect(() => {
    if (items.length > 0) {
      if (groupManagement.customizationGroups.length > 0) {
        const updatedGroups = groupManagement.customizationGroups.map(group => ({
          ...group,
          items: group.items.map(item => {
            const foundItem = items.find(apiItem => apiItem.item_id === item.code)
            return foundItem
              ? {
                  id: foundItem.id,
                  name: foundItem.item_name,
                  price: foundItem.ots_price,
                  code: foundItem.item_id
                }
              : item
          })
        }))
        groupManagement.setCustomizationGroups(updatedGroups)
      }

      if (existingCustomization?.listItem && existingCustomization.listItem.length > 0) {
        const dishIds = new Set<string>()
        existingCustomization.listItem.forEach(itemCode => {
          const foundItem = items.find(apiItem => apiItem.item_id === itemCode)
          if (foundItem) {
            dishIds.add(foundItem.id)
          }
        })
        dishSelection.setSelectedDishes(dishIds)
      }
    }
  }, [items, existingCustomization])

  // Early returns after hooks
  if (customizationId && !existingCustomization && !isLoadingCustomization && !customizationError) {
    return <MissingIdError />
  }

  if (isLoadingCustomization) {
    return <LoadingSpinner />
  }

  if (customizationError) {
    return <LoadingError description='Không thể tải thông tin customization' />
  }

  // Handle items loading error
  if (itemsError && customizationForm.selectedCityId) {
    return <LoadingError description='Không thể tải danh sách món ăn' />
  }

  const handleCreateGroup = () => {
    groupManagement.handleCreateGroup()
    modalState.setCreateGroupModalOpen(true)
  }

  const handleEditGroup = (groupId: string) => {
    groupManagement.handleEditGroup(groupId)
    modalState.setCreateGroupModalOpen(true)
  }

  const handleCloseModal = () => {
    modalState.handleCloseModal()
    groupManagement.resetGroupForm()
    menuItemSelection.resetSelection()
  }

  const handleCloseAddItemModal = () => {
    modalState.handleCloseAddItemModal()
    menuItemSelection.resetSelection()
  }

  const handleConfirmMenuItems = () => {
    menuItemSelection.handleConfirmMenuItems(items, groupManagement.menuItems)
    modalState.handleCloseAddItemModal()
  }

  const handleAddMenuItem = () => {
    modalState.handleAddMenuItem(customizationForm.selectedCityId)
  }

  const handleSaveGroup = () => {
    if (groupManagement.handleSaveGroup(items)) {
      handleCloseModal()
    }
  }

  const handleSave = async () => {
    await customizationForm.handleSave(
      groupManagement.customizationGroups,
      dishSelection.selectedDishes,
      items
    )
  }

  const selectedDishItems = dishSelection.getSelectedDishItems(items)
  const remainingDishItems = dishSelection.getRemainingDishItems(items)
  const selectedMenuItemsList = menuItemSelection.getSelectedMenuItemsList(items)
  const remainingMenuItemsList = menuItemSelection.getRemainingMenuItemsList(items)

  return (
    <div className='container mx-auto px-4 py-8'>
      <CustomizationHeader
        onBack={customizationForm.handleBack}
        onSave={handleSave}
        isSubmitting={customizationForm.isSubmitting}
        isFormValid={!!customizationForm.isFormValid}
        title={customizationId ? 'Sửa customization' : 'Tạo customization'}
        saveButtonText={customizationId ? 'Cập nhật' : 'Lưu'}
        submittingText={customizationId ? 'Đang cập nhật...' : 'Đang tạo...'}
      />

      <div className='mx-auto max-w-4xl'>
        <div className='p-6'>
          <div className='space-y-6'>
            <div className='flex items-center gap-4'>
              <Label htmlFor='customization-name' className='min-w-[200px] text-sm font-medium'>
                Tên customization <span className='text-red-500'>*</span>
              </Label>
              <Input
                id='customization-name'
                value={customizationForm.customizationName}
                onChange={e => customizationForm.setCustomizationName(e.target.value)}
                placeholder='Nhập tên customization'
                className='flex-1'
              />
            </div>

            <div className='flex items-center gap-4'>
              <Label htmlFor='city-select' className='min-w-[200px] text-sm font-medium'>
                Thành phố <span className='text-red-500'>*</span>
              </Label>
              <Select
                value={customizationForm.selectedCityId}
                onValueChange={customizationForm.setSelectedCityId}
              >
                <SelectTrigger className='flex-1'>
                  <SelectValue placeholder='Chọn thành phố' />
                </SelectTrigger>
                <SelectContent>
                  {cities.map(city => (
                    <SelectItem key={city.id} value={city.id}>
                      {city.city_name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {customizationForm.selectedCityId && (
              <div className='space-y-4 pt-6'>
                <h3 className='text-lg font-medium'>Áp dụng customization cho món</h3>
                <div
                  className={`cursor-pointer rounded-md border p-4 hover:bg-gray-50 ${
                    isLoadingItems ? 'cursor-not-allowed opacity-50' : ''
                  }`}
                  onClick={() => modalState.handleOpenDishModal(isLoadingItems)}
                >
                  <div className='flex items-center justify-between'>
                    <span className='font-medium'>Món ăn</span>
                    <span className='text-sm text-gray-500'>
                      {isLoadingItems
                        ? 'Đang tải...'
                        : `${dishSelection.selectedDishesCount} món áp dụng`}
                    </span>
                  </div>
                </div>
              </div>
            )}

            <div className='flex justify-center pt-4'>
              <Button onClick={handleCreateGroup}>Tạo nhóm</Button>
            </div>

            {groupManagement.customizationGroups.length > 0 && (
              <div className='space-y-6 pt-6'>
                <h3 className='text-lg font-medium'>Danh sách nhóm đã tạo</h3>
                {groupManagement.customizationGroups.map(group => (
                  <div key={group.id} className='space-y-3'>
                    <div className='flex items-center justify-between'>
                      <div>
                        <span className='font-medium'>{group.name}</span>
                        <span className='ml-2 text-sm text-gray-500'>
                          (Chọn từ {group.minRequired} đến {group.maxAllowed} món)
                        </span>
                      </div>
                      <div className='flex gap-2'>
                        <Button
                          variant='outline'
                          size='sm'
                          onClick={() => handleEditGroup(group.id)}
                        >
                          Sửa
                        </Button>
                        <Button
                          variant='outline'
                          size='sm'
                          onClick={() => groupManagement.handleDeleteGroup(group.id)}
                          className='text-red-600 hover:text-red-700'
                        >
                          Xóa
                        </Button>
                      </div>
                    </div>

                    <div className='grid grid-cols-2 gap-3 md:grid-cols-3 lg:grid-cols-4'>
                      {group.items.map(item => (
                        <div key={item.id} className='rounded-md border bg-gray-50 p-3 text-center'>
                          <p className='text-sm font-medium'>{item.name}</p>
                          <p className='mt-1 text-xs text-gray-500'>({item.code})</p>
                          <p className='mt-1 text-sm font-medium text-green-600'>
                            {item.price.toLocaleString('vi-VN')} ₫
                          </p>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>

      <CreateGroupModal
        open={modalState.createGroupModalOpen}
        onOpenChange={modalState.setCreateGroupModalOpen}
        onCancel={handleCloseModal}
        onConfirm={handleSaveGroup}
        onAddMenuItem={handleAddMenuItem}
        isEditing={groupManagement.isEditing}
        customizationName={customizationForm.customizationName}
        groupName={groupManagement.groupName}
        setGroupName={groupManagement.setGroupName}
        minRequired={groupManagement.minRequired}
        setMinRequired={groupManagement.setMinRequired}
        maxAllowed={groupManagement.maxAllowed}
        setMaxAllowed={groupManagement.setMaxAllowed}
        menuItems={groupManagement.menuItems}
      />

      <AddMenuItemModal
        open={modalState.addItemModalOpen}
        onOpenChange={modalState.setAddItemModalOpen}
        onCancel={handleCloseAddItemModal}
        onConfirm={handleConfirmMenuItems}
        menuItemSearchTerm={menuItemSelection.menuItemSearchTerm}
        setMenuItemSearchTerm={menuItemSelection.setMenuItemSearchTerm}
        selectedMenuSectionOpen={menuItemSelection.selectedMenuSectionOpen}
        setSelectedMenuSectionOpen={menuItemSelection.setSelectedMenuSectionOpen}
        remainingMenuSectionOpen={menuItemSelection.remainingMenuSectionOpen}
        setRemainingMenuSectionOpen={menuItemSelection.setRemainingMenuSectionOpen}
        selectedMenuItems={menuItemSelection.selectedMenuItems}
        handleMenuItemToggle={menuItemSelection.handleMenuItemToggle}
        selectedMenuItemsList={selectedMenuItemsList}
        remainingMenuItemsList={remainingMenuItemsList}
      />

      <DishSelectionModal
        open={modalState.dishModalOpen}
        onOpenChange={modalState.setDishModalOpen}
        onCancel={modalState.handleCloseDishModal}
        onConfirm={() => {
          dishSelection.handleConfirmDishSelection()
          modalState.handleCloseDishModal()
        }}
        dishSearchTerm={dishSelection.dishSearchTerm}
        setDishSearchTerm={dishSelection.setDishSearchTerm}
        selectedSectionOpen={dishSelection.selectedSectionOpen}
        setSelectedSectionOpen={dishSelection.setSelectedSectionOpen}
        remainingSectionOpen={dishSelection.remainingSectionOpen}
        setRemainingSectionOpen={dishSelection.setRemainingSectionOpen}
        selectedDishes={dishSelection.selectedDishes}
        handleDishToggle={dishSelection.handleDishToggle}
        selectedDishItems={selectedDishItems}
        remainingDishItems={remainingDishItems}
      />
    </div>
  )
}
