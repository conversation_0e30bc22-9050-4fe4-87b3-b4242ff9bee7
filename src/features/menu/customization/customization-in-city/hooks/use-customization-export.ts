import { useState, useRef } from 'react'

import { ParsedCustomizationData } from '@/types/customizations'
import { toast } from 'sonner'

import { useExportCustomizations, useBulkImportCustomizations } from '@/hooks/api'
import { usePosData } from '@/hooks/use-pos-data'

import { useExcelParser } from './use-excel-parser'

export function useCustomizationExport() {
  const [exportCityId, setExportCityId] = useState<string>('all')
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [parsedData, setParsedData] = useState<ParsedCustomizationData[]>([])
  const [showParsedData, setShowParsedData] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const { cities } = usePosData()
  const { parseExcelFile } = useExcelParser()
  const exportCustomizationsMutation = useExportCustomizations()
  const bulkImportCustomizationsMutation = useBulkImportCustomizations()

  const resetExportState = () => {
    setShowParsedData(false)
    setParsedData([])
    setSelectedFile(null)
  }

  const handleDownloadExportFile = async () => {
    try {
      // Prepare city UIDs for export based on selection
      const exportListCityUid =
        exportCityId === 'all' ? cities.map(city => city.id) : [exportCityId]

      await exportCustomizationsMutation.mutateAsync({
        listCityUid: exportListCityUid
      })

      toast.success('File đã được tải xuống thành công!')
    } catch {
      toast.error('Có lỗi xảy ra khi tải file xuống')
    }
  }

  const handleUploadFile = () => {
    fileInputRef.current?.click()
  }

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file) return

    setSelectedFile(file)

    try {
      const parsedCustomizations = await parseExcelFile(file, false)
      setParsedData(parsedCustomizations)
      setShowParsedData(true)
      toast.success(`Đã phân tích ${parsedCustomizations.length} customization từ file!`)
    } catch {
      // Error handling is done in parseExcelFile
    }
  }

  const handleSaveImportedData = async () => {
    if (parsedData.length === 0) {
      toast.error('Không có dữ liệu để lưu')
      return false
    }

    try {
      await bulkImportCustomizationsMutation.mutateAsync({
        parsedData
      })
      toast.success(`Đã tạo thành công ${parsedData.length} customization!`)
      resetExportState()
      return true
    } catch {
      toast.error('Lỗi khi tạo customization. Vui lòng thử lại.')
      return false
    }
  }

  return {
    exportCityId,
    setExportCityId,
    selectedFile,
    parsedData,
    showParsedData,
    fileInputRef,
    cities,
    resetExportState,
    handleDownloadExportFile,
    handleUploadFile,
    handleFileChange,
    handleSaveImportedData,
    isExporting: exportCustomizationsMutation.isPending,
    isSaving: bulkImportCustomizationsMutation.isPending
  }
}
