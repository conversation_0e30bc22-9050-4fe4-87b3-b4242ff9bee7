import { useState, useRef } from 'react'

import { ParsedCustomizationData } from '@/types/customizations'
import { toast } from 'sonner'

import { useBulkImportCustomizations } from '@/hooks/api'

import { useExcelParser } from './use-excel-parser'

export function useCustomizationImport() {
  const [importSelectedFile, setImportSelectedFile] = useState<File | null>(null)
  const [importParsedData, setImportParsedData] = useState<ParsedCustomizationData[]>([])
  const [showImportParsedData, setShowImportParsedData] = useState(false)
  const importFileInputRef = useRef<HTMLInputElement>(null)

  const { parseExcelFile, downloadTemplate } = useExcelParser()
  const bulkImportCustomizationsMutation = useBulkImportCustomizations()

  const resetImportState = () => {
    setShowImportParsedData(false)
    setImportParsedData([])
    setImportSelectedFile(null)
  }

  const handleDownloadTemplate = () => {
    downloadTemplate()
  }

  const handleImportFileUpload = () => {
    importFileInputRef.current?.click()
  }

  const handleImportFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file) return

    setImportSelectedFile(file)

    try {
      const parsedCustomizations = await parseExcelFile(file, true)
      setImportParsedData(parsedCustomizations)
      setShowImportParsedData(true)
      toast.success(`Đã phân tích ${parsedCustomizations.length} customization từ file!`)
    } catch {
      // Error handling is done in parseExcelFile
    }
  }

  const handleSaveImportedCustomizations = async () => {
    if (importParsedData.length === 0) {
      toast.error('Không có dữ liệu để lưu')
      return false
    }

    try {
      await bulkImportCustomizationsMutation.mutateAsync({
        parsedData: importParsedData
      })
      toast.success(`Đã tạo thành công ${importParsedData.length} customization!`)
      resetImportState()
      return true
    } catch {
      toast.error('Lỗi khi tạo customization. Vui lòng thử lại.')
      return false
    }
  }

  return {
    importSelectedFile,
    importParsedData,
    showImportParsedData,
    importFileInputRef,
    resetImportState,
    handleDownloadTemplate,
    handleImportFileUpload,
    handleImportFileChange,
    handleSaveImportedCustomizations,
    isLoading: bulkImportCustomizationsMutation.isPending
  }
}
