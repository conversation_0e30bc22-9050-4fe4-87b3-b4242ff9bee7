import { useState } from 'react'
import { Customization } from '@/types/customizations'

export function useCustomizationModals() {
  const [confirmModalOpen, setConfirmModalOpen] = useState(false)
  const [copyModalOpen, setCopyModalOpen] = useState(false)
  const [exportModalOpen, setExportModalOpen] = useState(false)
  const [importModalOpen, setImportModalOpen] = useState(false)
  const [selectedCustomization, setSelectedCustomization] =
    useState<Customization | null>(null)

  const openCopyModal = (customization: Customization) => {
    setSelectedCustomization(customization)
    setCopyModalOpen(true)
  }

  const closeCopyModal = () => {
    setCopyModalOpen(false)
    setSelectedCustomization(null)
  }

  const openDeleteModal = (customization: Customization) => {
    setSelectedCustomization(customization)
    setConfirmModalOpen(true)
  }

  const closeDeleteModal = () => {
    setConfirmModalOpen(false)
    setSelectedCustomization(null)
  }

  const openExportModal = () => {
    setExportModalOpen(true)
  }

  const closeExportModal = () => {
    setExportModalOpen(false)
  }

  const openImportModal = () => {
    setImportModalOpen(true)
  }

  const closeImportModal = () => {
    setImportModalOpen(false)
  }

  return {
    // Modal states
    confirmModalOpen,
    copyModalOpen,
    exportModalOpen,
    importModalOpen,
    selectedCustomization,
    
    // Modal actions
    openCopyModal,
    closeCopyModal,
    openDeleteModal,
    closeDeleteModal,
    openExportModal,
    closeExportModal,
    openImportModal,
    closeImportModal,
    
    // Direct setters for complex cases
    setConfirmModalOpen,
    setCopyModalOpen,
    setExportModalOpen,
    setImportModalOpen,
    setSelectedCustomization,
  }
}
