import { useState } from 'react'
import { toast } from 'sonner'
import { useCopyCustomization } from '@/hooks/api'
import { Customization } from '@/types/customizations'

export function useCustomizationCopy() {
  const [copyName, setCopyName] = useState('')
  const copyCustomizationMutation = useCopyCustomization()

  const initializeCopyName = (customization: Customization) => {
    setCopyName(`${customization.name} - Copy`)
  }

  const resetCopyName = () => {
    setCopyName('')
  }

  const handleConfirmCopy = async (customization: Customization) => {
    if (!customization || !copyName.trim()) {
      toast.error('Vui lòng nhập tên customization')
      return false
    }

    try {
      await copyCustomizationMutation.mutateAsync({
        customizationId: customization.id,
        newName: copyName.trim(),
      })
      toast.success(
        `Đã sao chép customization "${customization.name}" thành công!`
      )
      resetCopyName()
      return true
    } catch {
      toast.error('Không thể sao chép customization')
      return false
    }
  }

  return {
    copyName,
    setCopyName,
    initializeCopyName,
    resetCopyName,
    handleConfirmCopy,
    isLoading: copyCustomizationMutation.isPending,
  }
}
