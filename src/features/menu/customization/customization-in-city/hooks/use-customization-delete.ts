import { toast } from 'sonner'
import { useDeleteCustomization } from '@/hooks/api'
import { Customization } from '@/types/customizations'

export function useCustomizationDelete() {
  const deleteCustomizationMutation = useDeleteCustomization()

  const handleConfirmDelete = async (customization: Customization) => {
    if (!customization) return false

    try {
      await deleteCustomizationMutation.mutateAsync(customization.id)
      toast.success(
        `Đã xóa customization "${customization.name}" thành công!`
      )
      return true
    } catch {
      toast.error('Không thể xóa customization')
      return false
    }
  }

  return {
    handleConfirmDelete,
    isLoading: deleteCustomizationMutation.isPending,
  }
}
