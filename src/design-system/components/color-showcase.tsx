import React from 'react'
import { Badge } from '@/components/ui/badge'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { navyBlue, slate, emerald, red, amber, semanticColors } from '../tokens'

interface ColorSwatchProps {
  name: string
  color: string
  textColor?: string
  description?: string
}

const ColorSwatch: React.FC<ColorSwatchProps> = ({
  name,
  color,
  textColor = '#002347',
  description,
}) => {
  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(color)
    } catch {
      // Silently handle clipboard errors
    }
  }

  return (
    <div
      className='group relative cursor-pointer overflow-hidden rounded-lg border transition-all hover:scale-105 hover:shadow-md'
      onClick={copyToClipboard}
      style={{ backgroundColor: color }}
    >
      <div className='aspect-square w-full' />
      <div className='absolute inset-0 flex flex-col justify-end p-3'>
        <div
          className='rounded bg-white/90 p-2 backdrop-blur-sm'
          style={{ color: textColor }}
        >
          <div className='text-sm font-medium'>{name}</div>
          <div className='text-xs opacity-70'>{color.toUpperCase()}</div>
          {description && (
            <div className='mt-1 text-xs opacity-60'>{description}</div>
          )}
        </div>
      </div>
      <div className='bg-navy-blue-950/0 group-hover:bg-navy-blue-950/10 absolute inset-0 transition-colors' />
    </div>
  )
}

interface ColorPaletteProps {
  title: string
  colors: Record<string, string>
  description?: string
}

const ColorPalette: React.FC<ColorPaletteProps> = ({
  title,
  colors,
  description,
}) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle className='flex items-center gap-2'>
          {title}
          <Badge variant='outline' className='text-xs'>
            {Object.keys(colors).length} colors
          </Badge>
        </CardTitle>
        {description && <CardDescription>{description}</CardDescription>}
      </CardHeader>
      <CardContent>
        <div className='grid grid-cols-2 gap-3 sm:grid-cols-5 lg:grid-cols-10'>
          {Object.entries(colors).map(([key, value]) => (
            <ColorSwatch
              key={key}
              name={key}
              color={value}
              textColor={
                key.includes('50') || key.includes('100') || key.includes('200')
                  ? '#002347'
                  : '#ffffff'
              }
            />
          ))}
        </div>
      </CardContent>
    </Card>
  )
}

interface SemanticColorGroupProps {
  title: string
  lightColor: string
  darkColor: string
  description?: string
}

const SemanticColorGroup: React.FC<SemanticColorGroupProps> = ({
  title,
  lightColor,
  darkColor,
  description,
}) => {
  return (
    <div className='space-y-2'>
      <div className='flex items-center gap-2'>
        <h4 className='text-sm font-medium'>{title}</h4>
        {description && (
          <span className='text-muted-foreground text-xs'>({description})</span>
        )}
      </div>
      <div className='grid grid-cols-2 gap-2'>
        <ColorSwatch
          name='Light'
          color={lightColor}
          textColor={
            lightColor.includes('#f') || lightColor.includes('#e')
              ? '#002347'
              : '#ffffff'
          }
        />
        <ColorSwatch
          name='Dark'
          color={darkColor}
          textColor={
            darkColor.includes('#0') ||
            darkColor.includes('#1') ||
            darkColor.includes('#2')
              ? '#ffffff'
              : '#000000'
          }
        />
      </div>
    </div>
  )
}

export const ColorShowcase: React.FC = () => {
  return (
    <div className='space-y-8'>
      {/* Header */}
      <div className='space-y-2 text-center'>
        <h1 className='text-3xl font-bold tracking-tight'>
          Design System Colors
        </h1>
        <p className='text-muted-foreground'>
          Navy Blue (#005baa) themed color palette with light and dark mode
          support
        </p>
      </div>

      {/* Primary Brand Color */}
      <Card className='border-navy-blue-200 from-navy-blue-50 bg-gradient-to-r to-blue-50'>
        <CardHeader>
          <CardTitle className='text-navy-blue-900'>
            Primary Brand Color
          </CardTitle>
          <CardDescription>
            Navy Blue #005baa - The core color of our brand identity
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className='flex items-center justify-center p-8'>
            <div
              className='shadow-navy-lg rounded-2xl p-8 text-center'
              style={{ backgroundColor: navyBlue[600] }}
            >
              <div className='mb-2 text-2xl font-bold text-white'>
                Navy Blue
              </div>
              <div className='text-lg text-white/80'>#005baa</div>
              <div className='mt-2 text-sm text-white/60'>
                Primary Brand Color
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Color Palettes */}
      <div className='space-y-6'>
        <ColorPalette
          title='Navy Blue Palette'
          colors={navyBlue}
          description='Primary brand color variations from light to dark'
        />

        <ColorPalette
          title='Slate Palette'
          colors={slate}
          description='Neutral grays for backgrounds, text, and UI elements'
        />

        <ColorPalette
          title='Emerald Palette'
          colors={emerald}
          description='Success states and positive actions'
        />

        <ColorPalette
          title='Red Palette'
          colors={red}
          description='Error states and destructive actions'
        />

        <ColorPalette
          title='Amber Palette'
          colors={amber}
          description='Warning states and attention-getting elements'
        />
      </div>

      {/* Semantic Colors */}
      <Card>
        <CardHeader>
          <CardTitle>Semantic Colors</CardTitle>
          <CardDescription>
            Theme-aware colors that adapt to light and dark modes
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className='grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3'>
            <SemanticColorGroup
              title='Primary'
              lightColor={semanticColors.primary.light}
              darkColor={semanticColors.primary.dark}
              description='Main brand actions'
            />
            <SemanticColorGroup
              title='Secondary'
              lightColor={semanticColors.secondary.light}
              darkColor={semanticColors.secondary.dark}
              description='Secondary actions'
            />
            <SemanticColorGroup
              title='Background'
              lightColor={semanticColors.background.light}
              darkColor={semanticColors.background.dark}
              description='Page backgrounds'
            />
            <SemanticColorGroup
              title='Card'
              lightColor={semanticColors.card.light}
              darkColor={semanticColors.card.dark}
              description='Card backgrounds'
            />
            <SemanticColorGroup
              title='Muted'
              lightColor={semanticColors.muted.light}
              darkColor={semanticColors.muted.dark}
              description='Subdued elements'
            />
            <SemanticColorGroup
              title='Accent'
              lightColor={semanticColors.accent.light}
              darkColor={semanticColors.accent.dark}
              description='Highlight elements'
            />
          </div>
        </CardContent>
      </Card>

      {/* Usage Guidelines */}
      <Card>
        <CardHeader>
          <CardTitle>Usage Guidelines</CardTitle>
          <CardDescription>
            Best practices for using colors in the design system
          </CardDescription>
        </CardHeader>
        <CardContent className='prose prose-sm max-w-none'>
          <div className='grid gap-6 md:grid-cols-2'>
            <div>
              <h4 className='text-navy-blue-700 mb-2 font-semibold'>
                Accessibility
              </h4>
              <ul className='text-muted-foreground space-y-1 text-sm'>
                <li>
                  • All color combinations meet WCAG AA contrast requirements
                </li>
                <li>• Navy blue primary provides 4.5:1+ contrast on white</li>
                <li>• Dark theme colors ensure readability</li>
                <li>
                  • Status colors are distinguishable for colorblind users
                </li>
              </ul>
            </div>
            <div>
              <h4 className='text-navy-blue-700 mb-2 font-semibold'>
                Implementation
              </h4>
              <ul className='text-muted-foreground space-y-1 text-sm'>
                <li>• Use CSS custom properties for theme switching</li>
                <li>• Import semantic colors from design tokens</li>
                <li>• Prefer semantic colors over palette colors</li>
                <li>• Test in both light and dark modes</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export default ColorShowcase
