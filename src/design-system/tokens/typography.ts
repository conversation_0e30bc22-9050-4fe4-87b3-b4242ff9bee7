/**
 * Design System - Typography Tokens
 */

// Font families
export const fontFamilies = {
  sans: ['Inter', 'system-ui', 'sans-serif'],
  serif: ['Georgia', 'serif'],
  mono: ['ui-monospace', 'SFMono-Regular', 'Consolas', 'monospace'],
  display: ['Manrope', 'Inter', 'system-ui', 'sans-serif'],
} as const

// Font weights
export const fontWeights = {
  thin: '100',
  extralight: '200',
  light: '300',
  normal: '400',
  medium: '500',
  semibold: '600',
  bold: '700',
  extrabold: '800',
  darkNavy: '950',
} as const

// Font sizes with rem values
export const fontSizes = {
  xs: '0.75rem', // 12px
  sm: '0.875rem', // 14px
  base: '1rem', // 16px
  lg: '1.125rem', // 18px
  xl: '1.25rem', // 20px
  '2xl': '1.5rem', // 24px
  '3xl': '1.875rem', // 30px
  '4xl': '2.25rem', // 36px
  '5xl': '3rem', // 48px
  '6xl': '3.75rem', // 60px
  '7xl': '4.5rem', // 72px
  '8xl': '6rem', // 96px
  '9xl': '8rem', // 128px
} as const

// Line heights
export const lineHeights = {
  none: '1',
  tight: '1.25',
  snug: '1.375',
  normal: '1.5',
  relaxed: '1.625',
  loose: '2',
} as const

// Letter spacing
export const letterSpacing = {
  tighter: '-0.05em',
  tight: '-0.025em',
  normal: '0em',
  wide: '0.025em',
  wider: '0.05em',
  widest: '0.1em',
} as const

// Typography scale definitions
export const typographyScale = {
  // Display text (hero sections, landing pages)
  display: {
    '2xl': {
      fontSize: fontSizes['8xl'],
      lineHeight: lineHeights.none,
      letterSpacing: letterSpacing.tighter,
      fontWeight: fontWeights.bold,
      fontFamily: fontFamilies.display,
    },
    xl: {
      fontSize: fontSizes['7xl'],
      lineHeight: lineHeights.none,
      letterSpacing: letterSpacing.tighter,
      fontWeight: fontWeights.bold,
      fontFamily: fontFamilies.display,
    },
    lg: {
      fontSize: fontSizes['6xl'],
      lineHeight: lineHeights.none,
      letterSpacing: letterSpacing.tight,
      fontWeight: fontWeights.bold,
      fontFamily: fontFamilies.display,
    },
    md: {
      fontSize: fontSizes['5xl'],
      lineHeight: lineHeights.tight,
      letterSpacing: letterSpacing.tight,
      fontWeight: fontWeights.bold,
      fontFamily: fontFamilies.display,
    },
    sm: {
      fontSize: fontSizes['4xl'],
      lineHeight: lineHeights.tight,
      letterSpacing: letterSpacing.normal,
      fontWeight: fontWeights.semibold,
      fontFamily: fontFamilies.display,
    },
  },

  // Headings
  heading: {
    h1: {
      fontSize: fontSizes['3xl'],
      lineHeight: lineHeights.tight,
      letterSpacing: letterSpacing.tight,
      fontWeight: fontWeights.bold,
      fontFamily: fontFamilies.sans,
    },
    h2: {
      fontSize: fontSizes['2xl'],
      lineHeight: lineHeights.tight,
      letterSpacing: letterSpacing.tight,
      fontWeight: fontWeights.semibold,
      fontFamily: fontFamilies.sans,
    },
    h3: {
      fontSize: fontSizes.xl,
      lineHeight: lineHeights.snug,
      letterSpacing: letterSpacing.normal,
      fontWeight: fontWeights.semibold,
      fontFamily: fontFamilies.sans,
    },
    h4: {
      fontSize: fontSizes.lg,
      lineHeight: lineHeights.snug,
      letterSpacing: letterSpacing.normal,
      fontWeight: fontWeights.medium,
      fontFamily: fontFamilies.sans,
    },
    h5: {
      fontSize: fontSizes.base,
      lineHeight: lineHeights.normal,
      letterSpacing: letterSpacing.normal,
      fontWeight: fontWeights.medium,
      fontFamily: fontFamilies.sans,
    },
    h6: {
      fontSize: fontSizes.sm,
      lineHeight: lineHeights.normal,
      letterSpacing: letterSpacing.wide,
      fontWeight: fontWeights.medium,
      fontFamily: fontFamilies.sans,
    },
  },

  // Body text
  body: {
    lg: {
      fontSize: fontSizes.lg,
      lineHeight: lineHeights.relaxed,
      letterSpacing: letterSpacing.normal,
      fontWeight: fontWeights.normal,
      fontFamily: fontFamilies.sans,
    },
    md: {
      fontSize: fontSizes.base,
      lineHeight: lineHeights.normal,
      letterSpacing: letterSpacing.normal,
      fontWeight: fontWeights.normal,
      fontFamily: fontFamilies.sans,
    },
    sm: {
      fontSize: fontSizes.sm,
      lineHeight: lineHeights.normal,
      letterSpacing: letterSpacing.normal,
      fontWeight: fontWeights.normal,
      fontFamily: fontFamilies.sans,
    },
  },

  // Labels and captions
  label: {
    lg: {
      fontSize: fontSizes.sm,
      lineHeight: lineHeights.normal,
      letterSpacing: letterSpacing.normal,
      fontWeight: fontWeights.medium,
      fontFamily: fontFamilies.sans,
    },
    md: {
      fontSize: fontSizes.xs,
      lineHeight: lineHeights.normal,
      letterSpacing: letterSpacing.wide,
      fontWeight: fontWeights.medium,
      fontFamily: fontFamilies.sans,
    },
    sm: {
      fontSize: fontSizes.xs,
      lineHeight: lineHeights.tight,
      letterSpacing: letterSpacing.wider,
      fontWeight: fontWeights.medium,
      fontFamily: fontFamilies.sans,
    },
  },

  // Code and monospace
  code: {
    lg: {
      fontSize: fontSizes.base,
      lineHeight: lineHeights.normal,
      letterSpacing: letterSpacing.normal,
      fontWeight: fontWeights.normal,
      fontFamily: fontFamilies.mono,
    },
    md: {
      fontSize: fontSizes.sm,
      lineHeight: lineHeights.normal,
      letterSpacing: letterSpacing.normal,
      fontWeight: fontWeights.normal,
      fontFamily: fontFamilies.mono,
    },
    sm: {
      fontSize: fontSizes.xs,
      lineHeight: lineHeights.normal,
      letterSpacing: letterSpacing.normal,
      fontWeight: fontWeights.normal,
      fontFamily: fontFamilies.mono,
    },
  },
} as const

// Export types
export type FontFamily = keyof typeof fontFamilies
export type FontWeight = keyof typeof fontWeights
export type FontSize = keyof typeof fontSizes
export type LineHeight = keyof typeof lineHeights
export type LetterSpacing = keyof typeof letterSpacing
export type TypographyVariant = keyof typeof typographyScale
