/**
 * Responsive Tailwind CSS classes for reports components
 * Pure Tailwind CSS utility classes for consistent responsive design
 */

// Grid Layout Classes
export const gridClasses = {
  // Single column grid
  grid1: 'grid grid-cols-1 gap-3 sm:gap-4',

  // Two column grid
  grid2: 'grid grid-cols-1 gap-3 sm:grid-cols-2 sm:gap-4',

  // Three column grid
  grid3: 'grid grid-cols-1 gap-3 sm:grid-cols-2 lg:grid-cols-3 sm:gap-4',

  // Four column grid
  grid4: 'grid grid-cols-1 gap-3 sm:grid-cols-2 lg:grid-cols-4 sm:gap-4',

  // Five column grid
  grid5:
    'grid grid-cols-1 gap-3 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 sm:gap-4',

  // Auto responsive grid
  gridAuto:
    'grid grid-cols-1 gap-3 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 sm:gap-4',
} as const

// Container Classes
export const containerClasses = {
  // Main container
  main: 'w-full max-w-7xl mx-auto',

  // Full width container
  full: 'w-full max-w-none',

  // Constrained container
  constrained: 'w-full max-w-6xl mx-auto',
} as const

// Spacing Classes
export const spacingClasses = {
  // Vertical spacing
  spacingSm: 'space-y-2 sm:space-y-3',
  spacingMd: 'space-y-3 sm:space-y-4 lg:space-y-6',
  spacingLg: 'space-y-4 sm:space-y-6 lg:space-y-8',

  // Padding
  paddingSm: 'p-2 sm:p-3 lg:p-4',
  paddingMd: 'p-3 sm:p-4 lg:p-6',
  paddingLg: 'p-4 sm:p-6 lg:p-8',

  // Margin
  marginSm: 'm-2 sm:m-3 lg:m-4',
  marginMd: 'm-3 sm:m-4 lg:m-6',
  marginLg: 'm-4 sm:m-6 lg:m-8',
} as const

// Card Classes
export const cardClasses = {
  // Base card
  base: 'w-full min-h-0 overflow-hidden rounded-lg border bg-card text-card-foreground shadow-sm',

  // Card heights
  heightSm: 'h-48 sm:h-56',
  heightMd: 'h-64 sm:h-80',
  heightLg: 'h-80 sm:h-96 lg:h-[32rem]',

  // Card padding
  headerPadding: 'p-3 sm:p-4 lg:p-6 pb-2 sm:pb-3',
  contentPadding: 'p-3 sm:p-4 lg:p-6 pt-0',

  // Card content
  flexContent: 'flex-1 flex flex-col',
} as const

// Typography Classes
export const typographyClasses = {
  // Titles
  title: 'text-sm sm:text-base lg:text-lg font-medium',
  titleTruncate: 'text-sm sm:text-base lg:text-lg font-medium truncate',

  // Subtitles
  subtitle: 'text-xs sm:text-sm text-muted-foreground',
  subtitleTruncate: 'text-xs sm:text-sm text-muted-foreground truncate',

  // Values
  value: 'text-lg sm:text-xl lg:text-2xl font-bold',
  valueTruncate: 'text-lg sm:text-xl lg:text-2xl font-bold truncate',

  // Small values
  valueSmall: 'text-sm sm:text-base lg:text-lg font-semibold',
  valueSmallTruncate: 'text-sm sm:text-base lg:text-lg font-semibold truncate',

  // Body text
  body: 'text-sm sm:text-base',
  bodySmall: 'text-xs sm:text-sm',
} as const

// Table Classes
export const tableClasses = {
  // Table container
  container: 'w-full overflow-x-auto -mx-3 px-3 sm:mx-0 sm:px-0',

  // Table base
  base: 'w-full min-w-full',

  // Table headers
  header:
    'text-muted-foreground py-2 px-1 sm:px-2 text-xs sm:text-sm font-medium',
  headerLeft:
    'text-muted-foreground py-2 px-1 sm:px-2 text-left text-xs sm:text-sm font-medium',
  headerRight:
    'text-muted-foreground py-2 px-1 sm:px-2 text-right text-xs sm:text-sm font-medium',

  // Table cells
  cell: 'py-2 sm:py-3 px-1 sm:px-2 text-xs sm:text-sm',
  cellLeft: 'py-2 sm:py-3 px-1 sm:px-2 text-left text-xs sm:text-sm',
  cellRight: 'py-2 sm:py-3 px-1 sm:px-2 text-right text-xs sm:text-sm',

  // Table content
  cellContent: 'truncate block',
  cellContentBold: 'font-medium truncate block',
} as const

// Button Classes
export const buttonClasses = {
  // Button groups
  groupFlex: 'flex flex-wrap gap-2 sm:gap-3',
  groupGrid: 'grid grid-cols-2 gap-2 sm:flex sm:flex-wrap sm:gap-3',

  // Responsive buttons
  responsive: 'flex-1 sm:flex-none min-w-0',
  responsiveTouch: 'flex-1 sm:flex-none min-w-0 min-h-[44px]',
} as const

// Chart Classes
export const chartClasses = {
  // Chart containers
  containerSm: 'w-full h-48 sm:h-56',
  containerMd: 'w-full h-64 sm:h-80',
  containerLg: 'w-full h-80 sm:h-96',
  containerXl: 'w-full h-96 sm:h-[28rem] lg:h-[32rem]',

  // Chart responsive
  responsive: 'w-full h-full',
} as const

// Form Classes
export const formClasses = {
  // Form groups
  group: 'space-y-2',
  groupGrid: 'grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3',

  // Form controls
  control: 'w-full',
  controlResponsive: 'w-full min-w-0',

  // Labels
  label: 'text-sm font-medium',
  labelTruncate: 'text-sm font-medium truncate',
} as const

// Utility Classes
export const utilityClasses = {
  // Flex utilities
  flexCenter: 'flex items-center justify-center',
  flexBetween: 'flex items-center justify-between',
  flexCol: 'flex flex-col',
  flexColCenter: 'flex flex-col items-center justify-center',

  // Text utilities
  textTruncate: 'truncate',
  textBreak: 'break-words',
  textNoWrap: 'whitespace-nowrap',

  // Overflow utilities
  overflowHidden: 'overflow-hidden',
  overflowAuto: 'overflow-auto',
  overflowXAuto: 'overflow-x-auto',
  overflowYAuto: 'overflow-y-auto',

  // Sizing utilities
  wFull: 'w-full',
  hFull: 'h-full',
  minH0: 'min-h-0',
  minW0: 'min-w-0',

  // Position utilities
  relative: 'relative',
  absolute: 'absolute',
  fixed: 'fixed',
  sticky: 'sticky',

  // Z-index utilities
  z10: 'z-10',
  z20: 'z-20',
  z30: 'z-30',
  z40: 'z-40',
  z50: 'z-50',
} as const

// Responsive Breakpoint Utilities
export const breakpointClasses = {
  // Hide/show at breakpoints
  hiddenSm: 'hidden sm:block',
  hiddenMd: 'hidden md:block',
  hiddenLg: 'hidden lg:block',

  showSm: 'block sm:hidden',
  showMd: 'block md:hidden',
  showLg: 'block lg:hidden',

  // Responsive display
  blockSm: 'block sm:inline-block',
  inlineSm: 'inline sm:block',
  flexSm: 'flex sm:inline-flex',

  // Responsive text alignment
  textLeftSm: 'text-left sm:text-center',
  textCenterSm: 'text-center sm:text-left',
  textRightSm: 'text-right sm:text-center',
} as const

// Export all classes as a single object for easy importing
export const responsiveClasses = {
  grid: gridClasses,
  container: containerClasses,
  spacing: spacingClasses,
  card: cardClasses,
  typography: typographyClasses,
  table: tableClasses,
  button: buttonClasses,
  chart: chartClasses,
  form: formClasses,
  utility: utilityClasses,
  breakpoint: breakpointClasses,
} as const

// Type definitions for better TypeScript support
export type GridClass = keyof typeof gridClasses
export type ContainerClass = keyof typeof containerClasses
export type SpacingClass = keyof typeof spacingClasses
export type CardClass = keyof typeof cardClasses
export type TypographyClass = keyof typeof typographyClasses
export type TableClass = keyof typeof tableClasses
export type ButtonClass = keyof typeof buttonClasses
export type ChartClass = keyof typeof chartClasses
export type FormClass = keyof typeof formClasses
export type UtilityClass = keyof typeof utilityClasses
export type BreakpointClass = keyof typeof breakpointClasses
