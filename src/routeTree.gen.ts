/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

// Import Routes

import { Route as rootRoute } from './routes/__root'
import { Route as ClerkRouteImport } from './routes/clerk/route'
import { Route as AuthenticatedRouteImport } from './routes/_authenticated/route'
import { Route as AuthenticatedIndexImport } from './routes/_authenticated/index'
import { Route as AuthenticatedDesignSystemImport } from './routes/_authenticated/design-system'
import { Route as errors503Import } from './routes/(errors)/503'
import { Route as errors500Import } from './routes/(errors)/500'
import { Route as errors404Import } from './routes/(errors)/404'
import { Route as errors403Import } from './routes/(errors)/403'
import { Route as errors401Import } from './routes/(errors)/401'
import { Route as authSignUpImport } from './routes/(auth)/sign-up'
import { Route as authSignIn2Import } from './routes/(auth)/sign-in-2'
import { Route as authSignInImport } from './routes/(auth)/sign-in'
import { Route as authOtpImport } from './routes/(auth)/otp'
import { Route as authForgotPasswordImport } from './routes/(auth)/forgot-password'
import { Route as ClerkAuthenticatedRouteImport } from './routes/clerk/_authenticated/route'
import { Route as ClerkauthRouteImport } from './routes/clerk/(auth)/route'
import { Route as AuthenticatedSettingsRouteImport } from './routes/_authenticated/settings/route'
import { Route as AuthenticatedUsersIndexImport } from './routes/_authenticated/users/index'
import { Route as AuthenticatedTasksIndexImport } from './routes/_authenticated/tasks/index'
import { Route as AuthenticatedSettingsIndexImport } from './routes/_authenticated/settings/index'
import { Route as AuthenticatedSaleIndexImport } from './routes/_authenticated/sale/index'
import { Route as AuthenticatedHelpCenterIndexImport } from './routes/_authenticated/help-center/index'
import { Route as AuthenticatedChatsIndexImport } from './routes/_authenticated/chats/index'
import { Route as AuthenticatedBaoCaoIndexImport } from './routes/_authenticated/bao-cao/index'
import { Route as AuthenticatedAppsIndexImport } from './routes/_authenticated/apps/index'
import { Route as ClerkAuthenticatedUserManagementImport } from './routes/clerk/_authenticated/user-management'
import { Route as ClerkauthSignUpImport } from './routes/clerk/(auth)/sign-up'
import { Route as ClerkauthSignInImport } from './routes/clerk/(auth)/sign-in'
import { Route as AuthenticatedSettingsNotificationsImport } from './routes/_authenticated/settings/notifications'
import { Route as AuthenticatedSettingsDisplayImport } from './routes/_authenticated/settings/display'
import { Route as AuthenticatedSettingsAppearanceImport } from './routes/_authenticated/settings/appearance'
import { Route as AuthenticatedSettingsAccountImport } from './routes/_authenticated/settings/account'
import { Route as AuthenticatedSettingStoreImport } from './routes/_authenticated/setting/store'
import { Route as AuthenticatedSettingAreaImport } from './routes/_authenticated/setting/area'
import { Route as AuthenticatedSaleChannelDiscountImport } from './routes/_authenticated/sale-channel/discount'
import { Route as AuthenticatedMenuScheduleImport } from './routes/_authenticated/menu/schedule'
import { Route as AuthenticatedMenuQuantityDayImport } from './routes/_authenticated/menu/quantity-day'
import { Route as AuthenticatedMenuItemClassImport } from './routes/_authenticated/menu/item-class'
import { Route as AuthenticatedEmployeeRoleImport } from './routes/_authenticated/employee/role'
import { Route as AuthenticatedEmployeeListImport } from './routes/_authenticated/employee/list'
import { Route as AuthenticatedEmployeeDetailImport } from './routes/_authenticated/employee/detail'
import { Route as AuthenticatedDevicesTypesImport } from './routes/_authenticated/devices/types'
import { Route as AuthenticatedDevicesNewImport } from './routes/_authenticated/devices/new'
import { Route as AuthenticatedDevicesListImport } from './routes/_authenticated/devices/list'
import { Route as AuthenticatedBaoCaoKiemSoatImport } from './routes/_authenticated/bao-cao/kiem-soat'
import { Route as AuthenticatedBaoCaoDoanhThuImport } from './routes/_authenticated/bao-cao/doanh-thu'
import { Route as AuthenticatedSettingSourceIndexImport } from './routes/_authenticated/setting/source/index'
import { Route as AuthenticatedSettingPaymentMethodIndexImport } from './routes/_authenticated/setting/payment-method/index'
import { Route as AuthenticatedSettingAreaIndexImport } from './routes/_authenticated/setting/area/index'
import { Route as AuthenticatedSalesChannelChannelIndexImport } from './routes/_authenticated/sales-channel/channel/index'
import { Route as AuthenticatedSalePromotionIndexImport } from './routes/_authenticated/sale/promotion/index'
import { Route as AuthenticatedMenuItemClassIndexImport } from './routes/_authenticated/menu/item-class/index'
import { Route as AuthenticatedEmployeeRoleIndexImport } from './routes/_authenticated/employee/role/index'
import { Route as AuthenticatedBaoCaoKeToanIndexImport } from './routes/_authenticated/bao-cao/ke-toan/index'
import { Route as AuthenticatedSettingAreaDetailImport } from './routes/_authenticated/setting/area/detail'
import { Route as AuthenticatedSalesChannelChannelDetailImport } from './routes/_authenticated/sales-channel/channel/detail'
import { Route as AuthenticatedMenuItemRemovedItemRemovedInStoreImport } from './routes/_authenticated/menu/item-removed/item-removed-in-store'
import { Route as AuthenticatedMenuItemRemovedItemRemovedInCityImport } from './routes/_authenticated/menu/item-removed/item-removed-in-city'
import { Route as AuthenticatedMenuItemClassDetailImport } from './routes/_authenticated/menu/item-class/detail'
import { Route as AuthenticatedMenuCustomizationNewImport } from './routes/_authenticated/menu/customization/new'
import { Route as AuthenticatedMenuCategoryDetailImport } from './routes/_authenticated/menu/category/detail'
import { Route as AuthenticatedMenuCategoriesCategoriesInBrandImport } from './routes/_authenticated/menu/categories/categories-in-brand'
import { Route as AuthenticatedEmployeeRoleDetailImport } from './routes/_authenticated/employee/role/detail'
import { Route as AuthenticatedEmployeeDetailUserIdImport } from './routes/_authenticated/employee/detail.$userId'
import { Route as AuthenticatedBaoCaoKeToanHoaDonImport } from './routes/_authenticated/bao-cao/ke-toan/hoa-don'
import { Route as AuthenticatedBaoCaoKeToanBangKeHoaDonImport } from './routes/_authenticated/bao-cao/ke-toan/bang-ke-hoa-don'
import { Route as AuthenticatedSettingSourceDetailIndexImport } from './routes/_authenticated/setting/source/detail/index'
import { Route as AuthenticatedSettingPaymentMethodDetailIndexImport } from './routes/_authenticated/setting/payment-method/detail/index'
import { Route as AuthenticatedSettingAreaDetailIndexImport } from './routes/_authenticated/setting/area/detail/index'
import { Route as AuthenticatedMenuItemsItemsInStoreIndexImport } from './routes/_authenticated/menu/items/items-in-store/index'
import { Route as AuthenticatedMenuItemsItemsInCityIndexImport } from './routes/_authenticated/menu/items/items-in-city/index'
import { Route as AuthenticatedMenuItemClassDetailIndexImport } from './routes/_authenticated/menu/item-class/detail/index'
import { Route as AuthenticatedMenuCustomizationCustomizationInStoreIndexImport } from './routes/_authenticated/menu/customization/customization-in-store/index'
import { Route as AuthenticatedMenuCustomizationCustomizationInCityIndexImport } from './routes/_authenticated/menu/customization/customization-in-city/index'
import { Route as AuthenticatedMenuCategoryDetailIndexImport } from './routes/_authenticated/menu/category/detail/index'
import { Route as AuthenticatedSettingSourceDetailSourceIdImport } from './routes/_authenticated/setting/source/detail/$sourceId'
import { Route as AuthenticatedSettingPaymentMethodDetailPaymentMethodIdImport } from './routes/_authenticated/setting/payment-method/detail/$paymentMethodId'
import { Route as AuthenticatedSettingAreaDetailAreaIdImport } from './routes/_authenticated/setting/area/detail/$areaId'
import { Route as AuthenticatedSalesChannelChannelDetailUuidImport } from './routes/_authenticated/sales-channel/channel/detail/$uuid'
import { Route as AuthenticatedMenuItemClassDetailIdImport } from './routes/_authenticated/menu/item-class/detail/$id'
import { Route as AuthenticatedMenuCustomizationCustomizationInStoreDetailImport } from './routes/_authenticated/menu/customization/customization-in-store/detail'
import { Route as AuthenticatedMenuCustomizationCustomizationInStoreCreateImport } from './routes/_authenticated/menu/customization/customization-in-store/create'
import { Route as AuthenticatedMenuCustomizationCustomizationInCityDetailImport } from './routes/_authenticated/menu/customization/customization-in-city/detail'
import { Route as AuthenticatedMenuCategoryDetailIdImport } from './routes/_authenticated/menu/category/detail/$id'
import { Route as AuthenticatedMenuCategoriesCategoriesInBrandDetailImport } from './routes/_authenticated/menu/categories/categories-in-brand/detail'
import { Route as AuthenticatedMenuCustomizationCustomizationInStoreDetailIndexImport } from './routes/_authenticated/menu/customization/customization-in-store/detail/index'
import { Route as AuthenticatedMenuCustomizationCustomizationInStoreDetailCustomizationIdImport } from './routes/_authenticated/menu/customization/customization-in-store/detail/$customizationId'
import { Route as AuthenticatedMenuCustomizationCustomizationInCityDetailCustomizationIdImport } from './routes/_authenticated/menu/customization/customization-in-city/detail/$customizationId'
import { Route as AuthenticatedMenuCategoriesCategoriesInBrandDetailIdImport } from './routes/_authenticated/menu/categories/categories-in-brand/detail/$id'

// Create/Update Routes

const ClerkRouteRoute = ClerkRouteImport.update({
  id: '/clerk',
  path: '/clerk',
  getParentRoute: () => rootRoute,
} as any)

const AuthenticatedRouteRoute = AuthenticatedRouteImport.update({
  id: '/_authenticated',
  getParentRoute: () => rootRoute,
} as any)

const AuthenticatedIndexRoute = AuthenticatedIndexImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => AuthenticatedRouteRoute,
} as any)

const AuthenticatedDesignSystemRoute = AuthenticatedDesignSystemImport.update({
  id: '/design-system',
  path: '/design-system',
  getParentRoute: () => AuthenticatedRouteRoute,
} as any)

const errors503Route = errors503Import.update({
  id: '/(errors)/503',
  path: '/503',
  getParentRoute: () => rootRoute,
} as any)

const errors500Route = errors500Import.update({
  id: '/(errors)/500',
  path: '/500',
  getParentRoute: () => rootRoute,
} as any)

const errors404Route = errors404Import.update({
  id: '/(errors)/404',
  path: '/404',
  getParentRoute: () => rootRoute,
} as any)

const errors403Route = errors403Import.update({
  id: '/(errors)/403',
  path: '/403',
  getParentRoute: () => rootRoute,
} as any)

const errors401Route = errors401Import.update({
  id: '/(errors)/401',
  path: '/401',
  getParentRoute: () => rootRoute,
} as any)

const authSignUpRoute = authSignUpImport.update({
  id: '/(auth)/sign-up',
  path: '/sign-up',
  getParentRoute: () => rootRoute,
} as any)

const authSignIn2Route = authSignIn2Import.update({
  id: '/(auth)/sign-in-2',
  path: '/sign-in-2',
  getParentRoute: () => rootRoute,
} as any)

const authSignInRoute = authSignInImport.update({
  id: '/(auth)/sign-in',
  path: '/sign-in',
  getParentRoute: () => rootRoute,
} as any)

const authOtpRoute = authOtpImport.update({
  id: '/(auth)/otp',
  path: '/otp',
  getParentRoute: () => rootRoute,
} as any)

const authForgotPasswordRoute = authForgotPasswordImport.update({
  id: '/(auth)/forgot-password',
  path: '/forgot-password',
  getParentRoute: () => rootRoute,
} as any)

const ClerkAuthenticatedRouteRoute = ClerkAuthenticatedRouteImport.update({
  id: '/_authenticated',
  getParentRoute: () => ClerkRouteRoute,
} as any)

const ClerkauthRouteRoute = ClerkauthRouteImport.update({
  id: '/(auth)',
  getParentRoute: () => ClerkRouteRoute,
} as any)

const AuthenticatedSettingsRouteRoute = AuthenticatedSettingsRouteImport.update(
  {
    id: '/settings',
    path: '/settings',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any,
)

const AuthenticatedUsersIndexRoute = AuthenticatedUsersIndexImport.update({
  id: '/users/',
  path: '/users/',
  getParentRoute: () => AuthenticatedRouteRoute,
} as any)

const AuthenticatedTasksIndexRoute = AuthenticatedTasksIndexImport.update({
  id: '/tasks/',
  path: '/tasks/',
  getParentRoute: () => AuthenticatedRouteRoute,
} as any)

const AuthenticatedSettingsIndexRoute = AuthenticatedSettingsIndexImport.update(
  {
    id: '/',
    path: '/',
    getParentRoute: () => AuthenticatedSettingsRouteRoute,
  } as any,
)

const AuthenticatedSaleIndexRoute = AuthenticatedSaleIndexImport.update({
  id: '/sale/',
  path: '/sale/',
  getParentRoute: () => AuthenticatedRouteRoute,
} as any)

const AuthenticatedHelpCenterIndexRoute =
  AuthenticatedHelpCenterIndexImport.update({
    id: '/help-center/',
    path: '/help-center/',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedChatsIndexRoute = AuthenticatedChatsIndexImport.update({
  id: '/chats/',
  path: '/chats/',
  getParentRoute: () => AuthenticatedRouteRoute,
} as any)

const AuthenticatedBaoCaoIndexRoute = AuthenticatedBaoCaoIndexImport.update({
  id: '/bao-cao/',
  path: '/bao-cao/',
  getParentRoute: () => AuthenticatedRouteRoute,
} as any)

const AuthenticatedAppsIndexRoute = AuthenticatedAppsIndexImport.update({
  id: '/apps/',
  path: '/apps/',
  getParentRoute: () => AuthenticatedRouteRoute,
} as any)

const ClerkAuthenticatedUserManagementRoute =
  ClerkAuthenticatedUserManagementImport.update({
    id: '/user-management',
    path: '/user-management',
    getParentRoute: () => ClerkAuthenticatedRouteRoute,
  } as any)

const ClerkauthSignUpRoute = ClerkauthSignUpImport.update({
  id: '/sign-up',
  path: '/sign-up',
  getParentRoute: () => ClerkauthRouteRoute,
} as any)

const ClerkauthSignInRoute = ClerkauthSignInImport.update({
  id: '/sign-in',
  path: '/sign-in',
  getParentRoute: () => ClerkauthRouteRoute,
} as any)

const AuthenticatedSettingsNotificationsRoute =
  AuthenticatedSettingsNotificationsImport.update({
    id: '/notifications',
    path: '/notifications',
    getParentRoute: () => AuthenticatedSettingsRouteRoute,
  } as any)

const AuthenticatedSettingsDisplayRoute =
  AuthenticatedSettingsDisplayImport.update({
    id: '/display',
    path: '/display',
    getParentRoute: () => AuthenticatedSettingsRouteRoute,
  } as any)

const AuthenticatedSettingsAppearanceRoute =
  AuthenticatedSettingsAppearanceImport.update({
    id: '/appearance',
    path: '/appearance',
    getParentRoute: () => AuthenticatedSettingsRouteRoute,
  } as any)

const AuthenticatedSettingsAccountRoute =
  AuthenticatedSettingsAccountImport.update({
    id: '/account',
    path: '/account',
    getParentRoute: () => AuthenticatedSettingsRouteRoute,
  } as any)

const AuthenticatedSettingStoreRoute = AuthenticatedSettingStoreImport.update({
  id: '/setting/store',
  path: '/setting/store',
  getParentRoute: () => AuthenticatedRouteRoute,
} as any)

const AuthenticatedSettingAreaRoute = AuthenticatedSettingAreaImport.update({
  id: '/setting/area',
  path: '/setting/area',
  getParentRoute: () => AuthenticatedRouteRoute,
} as any)

const AuthenticatedSaleChannelDiscountRoute =
  AuthenticatedSaleChannelDiscountImport.update({
    id: '/sale-channel/discount',
    path: '/sale-channel/discount',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedMenuScheduleRoute = AuthenticatedMenuScheduleImport.update({
  id: '/menu/schedule',
  path: '/menu/schedule',
  getParentRoute: () => AuthenticatedRouteRoute,
} as any)

const AuthenticatedMenuQuantityDayRoute =
  AuthenticatedMenuQuantityDayImport.update({
    id: '/menu/quantity-day',
    path: '/menu/quantity-day',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedMenuItemClassRoute = AuthenticatedMenuItemClassImport.update(
  {
    id: '/menu/item-class',
    path: '/menu/item-class',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any,
)

const AuthenticatedEmployeeRoleRoute = AuthenticatedEmployeeRoleImport.update({
  id: '/employee/role',
  path: '/employee/role',
  getParentRoute: () => AuthenticatedRouteRoute,
} as any)

const AuthenticatedEmployeeListRoute = AuthenticatedEmployeeListImport.update({
  id: '/employee/list',
  path: '/employee/list',
  getParentRoute: () => AuthenticatedRouteRoute,
} as any)

const AuthenticatedEmployeeDetailRoute =
  AuthenticatedEmployeeDetailImport.update({
    id: '/employee/detail',
    path: '/employee/detail',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedDevicesTypesRoute = AuthenticatedDevicesTypesImport.update({
  id: '/devices/types',
  path: '/devices/types',
  getParentRoute: () => AuthenticatedRouteRoute,
} as any)

const AuthenticatedDevicesNewRoute = AuthenticatedDevicesNewImport.update({
  id: '/devices/new',
  path: '/devices/new',
  getParentRoute: () => AuthenticatedRouteRoute,
} as any)

const AuthenticatedDevicesListRoute = AuthenticatedDevicesListImport.update({
  id: '/devices/list',
  path: '/devices/list',
  getParentRoute: () => AuthenticatedRouteRoute,
} as any)

const AuthenticatedBaoCaoKiemSoatRoute =
  AuthenticatedBaoCaoKiemSoatImport.update({
    id: '/bao-cao/kiem-soat',
    path: '/bao-cao/kiem-soat',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedBaoCaoDoanhThuRoute =
  AuthenticatedBaoCaoDoanhThuImport.update({
    id: '/bao-cao/doanh-thu',
    path: '/bao-cao/doanh-thu',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedSettingSourceIndexRoute =
  AuthenticatedSettingSourceIndexImport.update({
    id: '/setting/source/',
    path: '/setting/source/',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedSettingPaymentMethodIndexRoute =
  AuthenticatedSettingPaymentMethodIndexImport.update({
    id: '/setting/payment-method/',
    path: '/setting/payment-method/',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedSettingAreaIndexRoute =
  AuthenticatedSettingAreaIndexImport.update({
    id: '/',
    path: '/',
    getParentRoute: () => AuthenticatedSettingAreaRoute,
  } as any)

const AuthenticatedSalesChannelChannelIndexRoute =
  AuthenticatedSalesChannelChannelIndexImport.update({
    id: '/sales-channel/channel/',
    path: '/sales-channel/channel/',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedSalePromotionIndexRoute =
  AuthenticatedSalePromotionIndexImport.update({
    id: '/sale/promotion/',
    path: '/sale/promotion/',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedMenuItemClassIndexRoute =
  AuthenticatedMenuItemClassIndexImport.update({
    id: '/',
    path: '/',
    getParentRoute: () => AuthenticatedMenuItemClassRoute,
  } as any)

const AuthenticatedEmployeeRoleIndexRoute =
  AuthenticatedEmployeeRoleIndexImport.update({
    id: '/',
    path: '/',
    getParentRoute: () => AuthenticatedEmployeeRoleRoute,
  } as any)

const AuthenticatedBaoCaoKeToanIndexRoute =
  AuthenticatedBaoCaoKeToanIndexImport.update({
    id: '/bao-cao/ke-toan/',
    path: '/bao-cao/ke-toan/',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedSettingAreaDetailRoute =
  AuthenticatedSettingAreaDetailImport.update({
    id: '/detail',
    path: '/detail',
    getParentRoute: () => AuthenticatedSettingAreaRoute,
  } as any)

const AuthenticatedSalesChannelChannelDetailRoute =
  AuthenticatedSalesChannelChannelDetailImport.update({
    id: '/sales-channel/channel/detail',
    path: '/sales-channel/channel/detail',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedMenuItemRemovedItemRemovedInStoreRoute =
  AuthenticatedMenuItemRemovedItemRemovedInStoreImport.update({
    id: '/menu/item-removed/item-removed-in-store',
    path: '/menu/item-removed/item-removed-in-store',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedMenuItemRemovedItemRemovedInCityRoute =
  AuthenticatedMenuItemRemovedItemRemovedInCityImport.update({
    id: '/menu/item-removed/item-removed-in-city',
    path: '/menu/item-removed/item-removed-in-city',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedMenuItemClassDetailRoute =
  AuthenticatedMenuItemClassDetailImport.update({
    id: '/detail',
    path: '/detail',
    getParentRoute: () => AuthenticatedMenuItemClassRoute,
  } as any)

const AuthenticatedMenuCustomizationNewRoute =
  AuthenticatedMenuCustomizationNewImport.update({
    id: '/menu/customization/new',
    path: '/menu/customization/new',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedMenuCategoryDetailRoute =
  AuthenticatedMenuCategoryDetailImport.update({
    id: '/menu/category/detail',
    path: '/menu/category/detail',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedMenuCategoriesCategoriesInBrandRoute =
  AuthenticatedMenuCategoriesCategoriesInBrandImport.update({
    id: '/menu/categories/categories-in-brand',
    path: '/menu/categories/categories-in-brand',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedEmployeeRoleDetailRoute =
  AuthenticatedEmployeeRoleDetailImport.update({
    id: '/detail',
    path: '/detail',
    getParentRoute: () => AuthenticatedEmployeeRoleRoute,
  } as any)

const AuthenticatedEmployeeDetailUserIdRoute =
  AuthenticatedEmployeeDetailUserIdImport.update({
    id: '/$userId',
    path: '/$userId',
    getParentRoute: () => AuthenticatedEmployeeDetailRoute,
  } as any)

const AuthenticatedBaoCaoKeToanHoaDonRoute =
  AuthenticatedBaoCaoKeToanHoaDonImport.update({
    id: '/bao-cao/ke-toan/hoa-don',
    path: '/bao-cao/ke-toan/hoa-don',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedBaoCaoKeToanBangKeHoaDonRoute =
  AuthenticatedBaoCaoKeToanBangKeHoaDonImport.update({
    id: '/bao-cao/ke-toan/bang-ke-hoa-don',
    path: '/bao-cao/ke-toan/bang-ke-hoa-don',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedSettingSourceDetailIndexRoute =
  AuthenticatedSettingSourceDetailIndexImport.update({
    id: '/setting/source/detail/',
    path: '/setting/source/detail/',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedSettingPaymentMethodDetailIndexRoute =
  AuthenticatedSettingPaymentMethodDetailIndexImport.update({
    id: '/setting/payment-method/detail/',
    path: '/setting/payment-method/detail/',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedSettingAreaDetailIndexRoute =
  AuthenticatedSettingAreaDetailIndexImport.update({
    id: '/',
    path: '/',
    getParentRoute: () => AuthenticatedSettingAreaDetailRoute,
  } as any)

const AuthenticatedMenuItemsItemsInStoreIndexRoute =
  AuthenticatedMenuItemsItemsInStoreIndexImport.update({
    id: '/menu/items/items-in-store/',
    path: '/menu/items/items-in-store/',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedMenuItemsItemsInCityIndexRoute =
  AuthenticatedMenuItemsItemsInCityIndexImport.update({
    id: '/menu/items/items-in-city/',
    path: '/menu/items/items-in-city/',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedMenuItemClassDetailIndexRoute =
  AuthenticatedMenuItemClassDetailIndexImport.update({
    id: '/',
    path: '/',
    getParentRoute: () => AuthenticatedMenuItemClassDetailRoute,
  } as any)

const AuthenticatedMenuCustomizationCustomizationInStoreIndexRoute =
  AuthenticatedMenuCustomizationCustomizationInStoreIndexImport.update({
    id: '/menu/customization/customization-in-store/',
    path: '/menu/customization/customization-in-store/',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedMenuCustomizationCustomizationInCityIndexRoute =
  AuthenticatedMenuCustomizationCustomizationInCityIndexImport.update({
    id: '/menu/customization/customization-in-city/',
    path: '/menu/customization/customization-in-city/',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedMenuCategoryDetailIndexRoute =
  AuthenticatedMenuCategoryDetailIndexImport.update({
    id: '/',
    path: '/',
    getParentRoute: () => AuthenticatedMenuCategoryDetailRoute,
  } as any)

const AuthenticatedSettingSourceDetailSourceIdRoute =
  AuthenticatedSettingSourceDetailSourceIdImport.update({
    id: '/setting/source/detail/$sourceId',
    path: '/setting/source/detail/$sourceId',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedSettingPaymentMethodDetailPaymentMethodIdRoute =
  AuthenticatedSettingPaymentMethodDetailPaymentMethodIdImport.update({
    id: '/setting/payment-method/detail/$paymentMethodId',
    path: '/setting/payment-method/detail/$paymentMethodId',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedSettingAreaDetailAreaIdRoute =
  AuthenticatedSettingAreaDetailAreaIdImport.update({
    id: '/$areaId',
    path: '/$areaId',
    getParentRoute: () => AuthenticatedSettingAreaDetailRoute,
  } as any)

const AuthenticatedSalesChannelChannelDetailUuidRoute =
  AuthenticatedSalesChannelChannelDetailUuidImport.update({
    id: '/$uuid',
    path: '/$uuid',
    getParentRoute: () => AuthenticatedSalesChannelChannelDetailRoute,
  } as any)

const AuthenticatedMenuItemClassDetailIdRoute =
  AuthenticatedMenuItemClassDetailIdImport.update({
    id: '/$id',
    path: '/$id',
    getParentRoute: () => AuthenticatedMenuItemClassDetailRoute,
  } as any)

const AuthenticatedMenuCustomizationCustomizationInStoreDetailRoute =
  AuthenticatedMenuCustomizationCustomizationInStoreDetailImport.update({
    id: '/menu/customization/customization-in-store/detail',
    path: '/menu/customization/customization-in-store/detail',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedMenuCustomizationCustomizationInStoreCreateRoute =
  AuthenticatedMenuCustomizationCustomizationInStoreCreateImport.update({
    id: '/menu/customization/customization-in-store/create',
    path: '/menu/customization/customization-in-store/create',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedMenuCustomizationCustomizationInCityDetailRoute =
  AuthenticatedMenuCustomizationCustomizationInCityDetailImport.update({
    id: '/menu/customization/customization-in-city/detail',
    path: '/menu/customization/customization-in-city/detail',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedMenuCategoryDetailIdRoute =
  AuthenticatedMenuCategoryDetailIdImport.update({
    id: '/$id',
    path: '/$id',
    getParentRoute: () => AuthenticatedMenuCategoryDetailRoute,
  } as any)

const AuthenticatedMenuCategoriesCategoriesInBrandDetailRoute =
  AuthenticatedMenuCategoriesCategoriesInBrandDetailImport.update({
    id: '/detail',
    path: '/detail',
    getParentRoute: () => AuthenticatedMenuCategoriesCategoriesInBrandRoute,
  } as any)

const AuthenticatedMenuCustomizationCustomizationInStoreDetailIndexRoute =
  AuthenticatedMenuCustomizationCustomizationInStoreDetailIndexImport.update({
    id: '/',
    path: '/',
    getParentRoute: () =>
      AuthenticatedMenuCustomizationCustomizationInStoreDetailRoute,
  } as any)

const AuthenticatedMenuCustomizationCustomizationInStoreDetailCustomizationIdRoute =
  AuthenticatedMenuCustomizationCustomizationInStoreDetailCustomizationIdImport.update(
    {
      id: '/$customizationId',
      path: '/$customizationId',
      getParentRoute: () =>
        AuthenticatedMenuCustomizationCustomizationInStoreDetailRoute,
    } as any,
  )

const AuthenticatedMenuCustomizationCustomizationInCityDetailCustomizationIdRoute =
  AuthenticatedMenuCustomizationCustomizationInCityDetailCustomizationIdImport.update(
    {
      id: '/$customizationId',
      path: '/$customizationId',
      getParentRoute: () =>
        AuthenticatedMenuCustomizationCustomizationInCityDetailRoute,
    } as any,
  )

const AuthenticatedMenuCategoriesCategoriesInBrandDetailIdRoute =
  AuthenticatedMenuCategoriesCategoriesInBrandDetailIdImport.update({
    id: '/$id',
    path: '/$id',
    getParentRoute: () =>
      AuthenticatedMenuCategoriesCategoriesInBrandDetailRoute,
  } as any)

// Populate the FileRoutesByPath interface

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/_authenticated': {
      id: '/_authenticated'
      path: ''
      fullPath: ''
      preLoaderRoute: typeof AuthenticatedRouteImport
      parentRoute: typeof rootRoute
    }
    '/clerk': {
      id: '/clerk'
      path: '/clerk'
      fullPath: '/clerk'
      preLoaderRoute: typeof ClerkRouteImport
      parentRoute: typeof rootRoute
    }
    '/_authenticated/settings': {
      id: '/_authenticated/settings'
      path: '/settings'
      fullPath: '/settings'
      preLoaderRoute: typeof AuthenticatedSettingsRouteImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/clerk/(auth)': {
      id: '/clerk/(auth)'
      path: '/'
      fullPath: '/clerk/'
      preLoaderRoute: typeof ClerkauthRouteImport
      parentRoute: typeof ClerkRouteImport
    }
    '/clerk/_authenticated': {
      id: '/clerk/_authenticated'
      path: ''
      fullPath: '/clerk'
      preLoaderRoute: typeof ClerkAuthenticatedRouteImport
      parentRoute: typeof ClerkRouteImport
    }
    '/(auth)/forgot-password': {
      id: '/(auth)/forgot-password'
      path: '/forgot-password'
      fullPath: '/forgot-password'
      preLoaderRoute: typeof authForgotPasswordImport
      parentRoute: typeof rootRoute
    }
    '/(auth)/otp': {
      id: '/(auth)/otp'
      path: '/otp'
      fullPath: '/otp'
      preLoaderRoute: typeof authOtpImport
      parentRoute: typeof rootRoute
    }
    '/(auth)/sign-in': {
      id: '/(auth)/sign-in'
      path: '/sign-in'
      fullPath: '/sign-in'
      preLoaderRoute: typeof authSignInImport
      parentRoute: typeof rootRoute
    }
    '/(auth)/sign-in-2': {
      id: '/(auth)/sign-in-2'
      path: '/sign-in-2'
      fullPath: '/sign-in-2'
      preLoaderRoute: typeof authSignIn2Import
      parentRoute: typeof rootRoute
    }
    '/(auth)/sign-up': {
      id: '/(auth)/sign-up'
      path: '/sign-up'
      fullPath: '/sign-up'
      preLoaderRoute: typeof authSignUpImport
      parentRoute: typeof rootRoute
    }
    '/(errors)/401': {
      id: '/(errors)/401'
      path: '/401'
      fullPath: '/401'
      preLoaderRoute: typeof errors401Import
      parentRoute: typeof rootRoute
    }
    '/(errors)/403': {
      id: '/(errors)/403'
      path: '/403'
      fullPath: '/403'
      preLoaderRoute: typeof errors403Import
      parentRoute: typeof rootRoute
    }
    '/(errors)/404': {
      id: '/(errors)/404'
      path: '/404'
      fullPath: '/404'
      preLoaderRoute: typeof errors404Import
      parentRoute: typeof rootRoute
    }
    '/(errors)/500': {
      id: '/(errors)/500'
      path: '/500'
      fullPath: '/500'
      preLoaderRoute: typeof errors500Import
      parentRoute: typeof rootRoute
    }
    '/(errors)/503': {
      id: '/(errors)/503'
      path: '/503'
      fullPath: '/503'
      preLoaderRoute: typeof errors503Import
      parentRoute: typeof rootRoute
    }
    '/_authenticated/design-system': {
      id: '/_authenticated/design-system'
      path: '/design-system'
      fullPath: '/design-system'
      preLoaderRoute: typeof AuthenticatedDesignSystemImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/': {
      id: '/_authenticated/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof AuthenticatedIndexImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/bao-cao/doanh-thu': {
      id: '/_authenticated/bao-cao/doanh-thu'
      path: '/bao-cao/doanh-thu'
      fullPath: '/bao-cao/doanh-thu'
      preLoaderRoute: typeof AuthenticatedBaoCaoDoanhThuImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/bao-cao/kiem-soat': {
      id: '/_authenticated/bao-cao/kiem-soat'
      path: '/bao-cao/kiem-soat'
      fullPath: '/bao-cao/kiem-soat'
      preLoaderRoute: typeof AuthenticatedBaoCaoKiemSoatImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/devices/list': {
      id: '/_authenticated/devices/list'
      path: '/devices/list'
      fullPath: '/devices/list'
      preLoaderRoute: typeof AuthenticatedDevicesListImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/devices/new': {
      id: '/_authenticated/devices/new'
      path: '/devices/new'
      fullPath: '/devices/new'
      preLoaderRoute: typeof AuthenticatedDevicesNewImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/devices/types': {
      id: '/_authenticated/devices/types'
      path: '/devices/types'
      fullPath: '/devices/types'
      preLoaderRoute: typeof AuthenticatedDevicesTypesImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/employee/detail': {
      id: '/_authenticated/employee/detail'
      path: '/employee/detail'
      fullPath: '/employee/detail'
      preLoaderRoute: typeof AuthenticatedEmployeeDetailImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/employee/list': {
      id: '/_authenticated/employee/list'
      path: '/employee/list'
      fullPath: '/employee/list'
      preLoaderRoute: typeof AuthenticatedEmployeeListImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/employee/role': {
      id: '/_authenticated/employee/role'
      path: '/employee/role'
      fullPath: '/employee/role'
      preLoaderRoute: typeof AuthenticatedEmployeeRoleImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/menu/item-class': {
      id: '/_authenticated/menu/item-class'
      path: '/menu/item-class'
      fullPath: '/menu/item-class'
      preLoaderRoute: typeof AuthenticatedMenuItemClassImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/menu/quantity-day': {
      id: '/_authenticated/menu/quantity-day'
      path: '/menu/quantity-day'
      fullPath: '/menu/quantity-day'
      preLoaderRoute: typeof AuthenticatedMenuQuantityDayImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/menu/schedule': {
      id: '/_authenticated/menu/schedule'
      path: '/menu/schedule'
      fullPath: '/menu/schedule'
      preLoaderRoute: typeof AuthenticatedMenuScheduleImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/sale-channel/discount': {
      id: '/_authenticated/sale-channel/discount'
      path: '/sale-channel/discount'
      fullPath: '/sale-channel/discount'
      preLoaderRoute: typeof AuthenticatedSaleChannelDiscountImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/setting/area': {
      id: '/_authenticated/setting/area'
      path: '/setting/area'
      fullPath: '/setting/area'
      preLoaderRoute: typeof AuthenticatedSettingAreaImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/setting/store': {
      id: '/_authenticated/setting/store'
      path: '/setting/store'
      fullPath: '/setting/store'
      preLoaderRoute: typeof AuthenticatedSettingStoreImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/settings/account': {
      id: '/_authenticated/settings/account'
      path: '/account'
      fullPath: '/settings/account'
      preLoaderRoute: typeof AuthenticatedSettingsAccountImport
      parentRoute: typeof AuthenticatedSettingsRouteImport
    }
    '/_authenticated/settings/appearance': {
      id: '/_authenticated/settings/appearance'
      path: '/appearance'
      fullPath: '/settings/appearance'
      preLoaderRoute: typeof AuthenticatedSettingsAppearanceImport
      parentRoute: typeof AuthenticatedSettingsRouteImport
    }
    '/_authenticated/settings/display': {
      id: '/_authenticated/settings/display'
      path: '/display'
      fullPath: '/settings/display'
      preLoaderRoute: typeof AuthenticatedSettingsDisplayImport
      parentRoute: typeof AuthenticatedSettingsRouteImport
    }
    '/_authenticated/settings/notifications': {
      id: '/_authenticated/settings/notifications'
      path: '/notifications'
      fullPath: '/settings/notifications'
      preLoaderRoute: typeof AuthenticatedSettingsNotificationsImport
      parentRoute: typeof AuthenticatedSettingsRouteImport
    }
    '/clerk/(auth)/sign-in': {
      id: '/clerk/(auth)/sign-in'
      path: '/sign-in'
      fullPath: '/clerk/sign-in'
      preLoaderRoute: typeof ClerkauthSignInImport
      parentRoute: typeof ClerkauthRouteImport
    }
    '/clerk/(auth)/sign-up': {
      id: '/clerk/(auth)/sign-up'
      path: '/sign-up'
      fullPath: '/clerk/sign-up'
      preLoaderRoute: typeof ClerkauthSignUpImport
      parentRoute: typeof ClerkauthRouteImport
    }
    '/clerk/_authenticated/user-management': {
      id: '/clerk/_authenticated/user-management'
      path: '/user-management'
      fullPath: '/clerk/user-management'
      preLoaderRoute: typeof ClerkAuthenticatedUserManagementImport
      parentRoute: typeof ClerkAuthenticatedRouteImport
    }
    '/_authenticated/apps/': {
      id: '/_authenticated/apps/'
      path: '/apps'
      fullPath: '/apps'
      preLoaderRoute: typeof AuthenticatedAppsIndexImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/bao-cao/': {
      id: '/_authenticated/bao-cao/'
      path: '/bao-cao'
      fullPath: '/bao-cao'
      preLoaderRoute: typeof AuthenticatedBaoCaoIndexImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/chats/': {
      id: '/_authenticated/chats/'
      path: '/chats'
      fullPath: '/chats'
      preLoaderRoute: typeof AuthenticatedChatsIndexImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/help-center/': {
      id: '/_authenticated/help-center/'
      path: '/help-center'
      fullPath: '/help-center'
      preLoaderRoute: typeof AuthenticatedHelpCenterIndexImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/sale/': {
      id: '/_authenticated/sale/'
      path: '/sale'
      fullPath: '/sale'
      preLoaderRoute: typeof AuthenticatedSaleIndexImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/settings/': {
      id: '/_authenticated/settings/'
      path: '/'
      fullPath: '/settings/'
      preLoaderRoute: typeof AuthenticatedSettingsIndexImport
      parentRoute: typeof AuthenticatedSettingsRouteImport
    }
    '/_authenticated/tasks/': {
      id: '/_authenticated/tasks/'
      path: '/tasks'
      fullPath: '/tasks'
      preLoaderRoute: typeof AuthenticatedTasksIndexImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/users/': {
      id: '/_authenticated/users/'
      path: '/users'
      fullPath: '/users'
      preLoaderRoute: typeof AuthenticatedUsersIndexImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/bao-cao/ke-toan/bang-ke-hoa-don': {
      id: '/_authenticated/bao-cao/ke-toan/bang-ke-hoa-don'
      path: '/bao-cao/ke-toan/bang-ke-hoa-don'
      fullPath: '/bao-cao/ke-toan/bang-ke-hoa-don'
      preLoaderRoute: typeof AuthenticatedBaoCaoKeToanBangKeHoaDonImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/bao-cao/ke-toan/hoa-don': {
      id: '/_authenticated/bao-cao/ke-toan/hoa-don'
      path: '/bao-cao/ke-toan/hoa-don'
      fullPath: '/bao-cao/ke-toan/hoa-don'
      preLoaderRoute: typeof AuthenticatedBaoCaoKeToanHoaDonImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/employee/detail/$userId': {
      id: '/_authenticated/employee/detail/$userId'
      path: '/$userId'
      fullPath: '/employee/detail/$userId'
      preLoaderRoute: typeof AuthenticatedEmployeeDetailUserIdImport
      parentRoute: typeof AuthenticatedEmployeeDetailImport
    }
    '/_authenticated/employee/role/detail': {
      id: '/_authenticated/employee/role/detail'
      path: '/detail'
      fullPath: '/employee/role/detail'
      preLoaderRoute: typeof AuthenticatedEmployeeRoleDetailImport
      parentRoute: typeof AuthenticatedEmployeeRoleImport
    }
    '/_authenticated/menu/categories/categories-in-brand': {
      id: '/_authenticated/menu/categories/categories-in-brand'
      path: '/menu/categories/categories-in-brand'
      fullPath: '/menu/categories/categories-in-brand'
      preLoaderRoute: typeof AuthenticatedMenuCategoriesCategoriesInBrandImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/menu/category/detail': {
      id: '/_authenticated/menu/category/detail'
      path: '/menu/category/detail'
      fullPath: '/menu/category/detail'
      preLoaderRoute: typeof AuthenticatedMenuCategoryDetailImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/menu/customization/new': {
      id: '/_authenticated/menu/customization/new'
      path: '/menu/customization/new'
      fullPath: '/menu/customization/new'
      preLoaderRoute: typeof AuthenticatedMenuCustomizationNewImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/menu/item-class/detail': {
      id: '/_authenticated/menu/item-class/detail'
      path: '/detail'
      fullPath: '/menu/item-class/detail'
      preLoaderRoute: typeof AuthenticatedMenuItemClassDetailImport
      parentRoute: typeof AuthenticatedMenuItemClassImport
    }
    '/_authenticated/menu/item-removed/item-removed-in-city': {
      id: '/_authenticated/menu/item-removed/item-removed-in-city'
      path: '/menu/item-removed/item-removed-in-city'
      fullPath: '/menu/item-removed/item-removed-in-city'
      preLoaderRoute: typeof AuthenticatedMenuItemRemovedItemRemovedInCityImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/menu/item-removed/item-removed-in-store': {
      id: '/_authenticated/menu/item-removed/item-removed-in-store'
      path: '/menu/item-removed/item-removed-in-store'
      fullPath: '/menu/item-removed/item-removed-in-store'
      preLoaderRoute: typeof AuthenticatedMenuItemRemovedItemRemovedInStoreImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/sales-channel/channel/detail': {
      id: '/_authenticated/sales-channel/channel/detail'
      path: '/sales-channel/channel/detail'
      fullPath: '/sales-channel/channel/detail'
      preLoaderRoute: typeof AuthenticatedSalesChannelChannelDetailImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/setting/area/detail': {
      id: '/_authenticated/setting/area/detail'
      path: '/detail'
      fullPath: '/setting/area/detail'
      preLoaderRoute: typeof AuthenticatedSettingAreaDetailImport
      parentRoute: typeof AuthenticatedSettingAreaImport
    }
    '/_authenticated/bao-cao/ke-toan/': {
      id: '/_authenticated/bao-cao/ke-toan/'
      path: '/bao-cao/ke-toan'
      fullPath: '/bao-cao/ke-toan'
      preLoaderRoute: typeof AuthenticatedBaoCaoKeToanIndexImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/employee/role/': {
      id: '/_authenticated/employee/role/'
      path: '/'
      fullPath: '/employee/role/'
      preLoaderRoute: typeof AuthenticatedEmployeeRoleIndexImport
      parentRoute: typeof AuthenticatedEmployeeRoleImport
    }
    '/_authenticated/menu/item-class/': {
      id: '/_authenticated/menu/item-class/'
      path: '/'
      fullPath: '/menu/item-class/'
      preLoaderRoute: typeof AuthenticatedMenuItemClassIndexImport
      parentRoute: typeof AuthenticatedMenuItemClassImport
    }
    '/_authenticated/sale/promotion/': {
      id: '/_authenticated/sale/promotion/'
      path: '/sale/promotion'
      fullPath: '/sale/promotion'
      preLoaderRoute: typeof AuthenticatedSalePromotionIndexImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/sales-channel/channel/': {
      id: '/_authenticated/sales-channel/channel/'
      path: '/sales-channel/channel'
      fullPath: '/sales-channel/channel'
      preLoaderRoute: typeof AuthenticatedSalesChannelChannelIndexImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/setting/area/': {
      id: '/_authenticated/setting/area/'
      path: '/'
      fullPath: '/setting/area/'
      preLoaderRoute: typeof AuthenticatedSettingAreaIndexImport
      parentRoute: typeof AuthenticatedSettingAreaImport
    }
    '/_authenticated/setting/payment-method/': {
      id: '/_authenticated/setting/payment-method/'
      path: '/setting/payment-method'
      fullPath: '/setting/payment-method'
      preLoaderRoute: typeof AuthenticatedSettingPaymentMethodIndexImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/setting/source/': {
      id: '/_authenticated/setting/source/'
      path: '/setting/source'
      fullPath: '/setting/source'
      preLoaderRoute: typeof AuthenticatedSettingSourceIndexImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/menu/categories/categories-in-brand/detail': {
      id: '/_authenticated/menu/categories/categories-in-brand/detail'
      path: '/detail'
      fullPath: '/menu/categories/categories-in-brand/detail'
      preLoaderRoute: typeof AuthenticatedMenuCategoriesCategoriesInBrandDetailImport
      parentRoute: typeof AuthenticatedMenuCategoriesCategoriesInBrandImport
    }
    '/_authenticated/menu/category/detail/$id': {
      id: '/_authenticated/menu/category/detail/$id'
      path: '/$id'
      fullPath: '/menu/category/detail/$id'
      preLoaderRoute: typeof AuthenticatedMenuCategoryDetailIdImport
      parentRoute: typeof AuthenticatedMenuCategoryDetailImport
    }
    '/_authenticated/menu/customization/customization-in-city/detail': {
      id: '/_authenticated/menu/customization/customization-in-city/detail'
      path: '/menu/customization/customization-in-city/detail'
      fullPath: '/menu/customization/customization-in-city/detail'
      preLoaderRoute: typeof AuthenticatedMenuCustomizationCustomizationInCityDetailImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/menu/customization/customization-in-store/create': {
      id: '/_authenticated/menu/customization/customization-in-store/create'
      path: '/menu/customization/customization-in-store/create'
      fullPath: '/menu/customization/customization-in-store/create'
      preLoaderRoute: typeof AuthenticatedMenuCustomizationCustomizationInStoreCreateImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/menu/customization/customization-in-store/detail': {
      id: '/_authenticated/menu/customization/customization-in-store/detail'
      path: '/menu/customization/customization-in-store/detail'
      fullPath: '/menu/customization/customization-in-store/detail'
      preLoaderRoute: typeof AuthenticatedMenuCustomizationCustomizationInStoreDetailImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/menu/item-class/detail/$id': {
      id: '/_authenticated/menu/item-class/detail/$id'
      path: '/$id'
      fullPath: '/menu/item-class/detail/$id'
      preLoaderRoute: typeof AuthenticatedMenuItemClassDetailIdImport
      parentRoute: typeof AuthenticatedMenuItemClassDetailImport
    }
    '/_authenticated/sales-channel/channel/detail/$uuid': {
      id: '/_authenticated/sales-channel/channel/detail/$uuid'
      path: '/$uuid'
      fullPath: '/sales-channel/channel/detail/$uuid'
      preLoaderRoute: typeof AuthenticatedSalesChannelChannelDetailUuidImport
      parentRoute: typeof AuthenticatedSalesChannelChannelDetailImport
    }
    '/_authenticated/setting/area/detail/$areaId': {
      id: '/_authenticated/setting/area/detail/$areaId'
      path: '/$areaId'
      fullPath: '/setting/area/detail/$areaId'
      preLoaderRoute: typeof AuthenticatedSettingAreaDetailAreaIdImport
      parentRoute: typeof AuthenticatedSettingAreaDetailImport
    }
    '/_authenticated/setting/payment-method/detail/$paymentMethodId': {
      id: '/_authenticated/setting/payment-method/detail/$paymentMethodId'
      path: '/setting/payment-method/detail/$paymentMethodId'
      fullPath: '/setting/payment-method/detail/$paymentMethodId'
      preLoaderRoute: typeof AuthenticatedSettingPaymentMethodDetailPaymentMethodIdImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/setting/source/detail/$sourceId': {
      id: '/_authenticated/setting/source/detail/$sourceId'
      path: '/setting/source/detail/$sourceId'
      fullPath: '/setting/source/detail/$sourceId'
      preLoaderRoute: typeof AuthenticatedSettingSourceDetailSourceIdImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/menu/category/detail/': {
      id: '/_authenticated/menu/category/detail/'
      path: '/'
      fullPath: '/menu/category/detail/'
      preLoaderRoute: typeof AuthenticatedMenuCategoryDetailIndexImport
      parentRoute: typeof AuthenticatedMenuCategoryDetailImport
    }
    '/_authenticated/menu/customization/customization-in-city/': {
      id: '/_authenticated/menu/customization/customization-in-city/'
      path: '/menu/customization/customization-in-city'
      fullPath: '/menu/customization/customization-in-city'
      preLoaderRoute: typeof AuthenticatedMenuCustomizationCustomizationInCityIndexImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/menu/customization/customization-in-store/': {
      id: '/_authenticated/menu/customization/customization-in-store/'
      path: '/menu/customization/customization-in-store'
      fullPath: '/menu/customization/customization-in-store'
      preLoaderRoute: typeof AuthenticatedMenuCustomizationCustomizationInStoreIndexImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/menu/item-class/detail/': {
      id: '/_authenticated/menu/item-class/detail/'
      path: '/'
      fullPath: '/menu/item-class/detail/'
      preLoaderRoute: typeof AuthenticatedMenuItemClassDetailIndexImport
      parentRoute: typeof AuthenticatedMenuItemClassDetailImport
    }
    '/_authenticated/menu/items/items-in-city/': {
      id: '/_authenticated/menu/items/items-in-city/'
      path: '/menu/items/items-in-city'
      fullPath: '/menu/items/items-in-city'
      preLoaderRoute: typeof AuthenticatedMenuItemsItemsInCityIndexImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/menu/items/items-in-store/': {
      id: '/_authenticated/menu/items/items-in-store/'
      path: '/menu/items/items-in-store'
      fullPath: '/menu/items/items-in-store'
      preLoaderRoute: typeof AuthenticatedMenuItemsItemsInStoreIndexImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/setting/area/detail/': {
      id: '/_authenticated/setting/area/detail/'
      path: '/'
      fullPath: '/setting/area/detail/'
      preLoaderRoute: typeof AuthenticatedSettingAreaDetailIndexImport
      parentRoute: typeof AuthenticatedSettingAreaDetailImport
    }
    '/_authenticated/setting/payment-method/detail/': {
      id: '/_authenticated/setting/payment-method/detail/'
      path: '/setting/payment-method/detail'
      fullPath: '/setting/payment-method/detail'
      preLoaderRoute: typeof AuthenticatedSettingPaymentMethodDetailIndexImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/setting/source/detail/': {
      id: '/_authenticated/setting/source/detail/'
      path: '/setting/source/detail'
      fullPath: '/setting/source/detail'
      preLoaderRoute: typeof AuthenticatedSettingSourceDetailIndexImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/menu/categories/categories-in-brand/detail/$id': {
      id: '/_authenticated/menu/categories/categories-in-brand/detail/$id'
      path: '/$id'
      fullPath: '/menu/categories/categories-in-brand/detail/$id'
      preLoaderRoute: typeof AuthenticatedMenuCategoriesCategoriesInBrandDetailIdImport
      parentRoute: typeof AuthenticatedMenuCategoriesCategoriesInBrandDetailImport
    }
    '/_authenticated/menu/customization/customization-in-city/detail/$customizationId': {
      id: '/_authenticated/menu/customization/customization-in-city/detail/$customizationId'
      path: '/$customizationId'
      fullPath: '/menu/customization/customization-in-city/detail/$customizationId'
      preLoaderRoute: typeof AuthenticatedMenuCustomizationCustomizationInCityDetailCustomizationIdImport
      parentRoute: typeof AuthenticatedMenuCustomizationCustomizationInCityDetailImport
    }
    '/_authenticated/menu/customization/customization-in-store/detail/$customizationId': {
      id: '/_authenticated/menu/customization/customization-in-store/detail/$customizationId'
      path: '/$customizationId'
      fullPath: '/menu/customization/customization-in-store/detail/$customizationId'
      preLoaderRoute: typeof AuthenticatedMenuCustomizationCustomizationInStoreDetailCustomizationIdImport
      parentRoute: typeof AuthenticatedMenuCustomizationCustomizationInStoreDetailImport
    }
    '/_authenticated/menu/customization/customization-in-store/detail/': {
      id: '/_authenticated/menu/customization/customization-in-store/detail/'
      path: '/'
      fullPath: '/menu/customization/customization-in-store/detail/'
      preLoaderRoute: typeof AuthenticatedMenuCustomizationCustomizationInStoreDetailIndexImport
      parentRoute: typeof AuthenticatedMenuCustomizationCustomizationInStoreDetailImport
    }
  }
}

// Create and export the route tree

interface AuthenticatedSettingsRouteRouteChildren {
  AuthenticatedSettingsAccountRoute: typeof AuthenticatedSettingsAccountRoute
  AuthenticatedSettingsAppearanceRoute: typeof AuthenticatedSettingsAppearanceRoute
  AuthenticatedSettingsDisplayRoute: typeof AuthenticatedSettingsDisplayRoute
  AuthenticatedSettingsNotificationsRoute: typeof AuthenticatedSettingsNotificationsRoute
  AuthenticatedSettingsIndexRoute: typeof AuthenticatedSettingsIndexRoute
}

const AuthenticatedSettingsRouteRouteChildren: AuthenticatedSettingsRouteRouteChildren =
  {
    AuthenticatedSettingsAccountRoute: AuthenticatedSettingsAccountRoute,
    AuthenticatedSettingsAppearanceRoute: AuthenticatedSettingsAppearanceRoute,
    AuthenticatedSettingsDisplayRoute: AuthenticatedSettingsDisplayRoute,
    AuthenticatedSettingsNotificationsRoute:
      AuthenticatedSettingsNotificationsRoute,
    AuthenticatedSettingsIndexRoute: AuthenticatedSettingsIndexRoute,
  }

const AuthenticatedSettingsRouteRouteWithChildren =
  AuthenticatedSettingsRouteRoute._addFileChildren(
    AuthenticatedSettingsRouteRouteChildren,
  )

interface AuthenticatedEmployeeDetailRouteChildren {
  AuthenticatedEmployeeDetailUserIdRoute: typeof AuthenticatedEmployeeDetailUserIdRoute
}

const AuthenticatedEmployeeDetailRouteChildren: AuthenticatedEmployeeDetailRouteChildren =
  {
    AuthenticatedEmployeeDetailUserIdRoute:
      AuthenticatedEmployeeDetailUserIdRoute,
  }

const AuthenticatedEmployeeDetailRouteWithChildren =
  AuthenticatedEmployeeDetailRoute._addFileChildren(
    AuthenticatedEmployeeDetailRouteChildren,
  )

interface AuthenticatedEmployeeRoleRouteChildren {
  AuthenticatedEmployeeRoleDetailRoute: typeof AuthenticatedEmployeeRoleDetailRoute
  AuthenticatedEmployeeRoleIndexRoute: typeof AuthenticatedEmployeeRoleIndexRoute
}

const AuthenticatedEmployeeRoleRouteChildren: AuthenticatedEmployeeRoleRouteChildren =
  {
    AuthenticatedEmployeeRoleDetailRoute: AuthenticatedEmployeeRoleDetailRoute,
    AuthenticatedEmployeeRoleIndexRoute: AuthenticatedEmployeeRoleIndexRoute,
  }

const AuthenticatedEmployeeRoleRouteWithChildren =
  AuthenticatedEmployeeRoleRoute._addFileChildren(
    AuthenticatedEmployeeRoleRouteChildren,
  )

interface AuthenticatedMenuItemClassDetailRouteChildren {
  AuthenticatedMenuItemClassDetailIdRoute: typeof AuthenticatedMenuItemClassDetailIdRoute
  AuthenticatedMenuItemClassDetailIndexRoute: typeof AuthenticatedMenuItemClassDetailIndexRoute
}

const AuthenticatedMenuItemClassDetailRouteChildren: AuthenticatedMenuItemClassDetailRouteChildren =
  {
    AuthenticatedMenuItemClassDetailIdRoute:
      AuthenticatedMenuItemClassDetailIdRoute,
    AuthenticatedMenuItemClassDetailIndexRoute:
      AuthenticatedMenuItemClassDetailIndexRoute,
  }

const AuthenticatedMenuItemClassDetailRouteWithChildren =
  AuthenticatedMenuItemClassDetailRoute._addFileChildren(
    AuthenticatedMenuItemClassDetailRouteChildren,
  )

interface AuthenticatedMenuItemClassRouteChildren {
  AuthenticatedMenuItemClassDetailRoute: typeof AuthenticatedMenuItemClassDetailRouteWithChildren
  AuthenticatedMenuItemClassIndexRoute: typeof AuthenticatedMenuItemClassIndexRoute
}

const AuthenticatedMenuItemClassRouteChildren: AuthenticatedMenuItemClassRouteChildren =
  {
    AuthenticatedMenuItemClassDetailRoute:
      AuthenticatedMenuItemClassDetailRouteWithChildren,
    AuthenticatedMenuItemClassIndexRoute: AuthenticatedMenuItemClassIndexRoute,
  }

const AuthenticatedMenuItemClassRouteWithChildren =
  AuthenticatedMenuItemClassRoute._addFileChildren(
    AuthenticatedMenuItemClassRouteChildren,
  )

interface AuthenticatedSettingAreaDetailRouteChildren {
  AuthenticatedSettingAreaDetailAreaIdRoute: typeof AuthenticatedSettingAreaDetailAreaIdRoute
  AuthenticatedSettingAreaDetailIndexRoute: typeof AuthenticatedSettingAreaDetailIndexRoute
}

const AuthenticatedSettingAreaDetailRouteChildren: AuthenticatedSettingAreaDetailRouteChildren =
  {
    AuthenticatedSettingAreaDetailAreaIdRoute:
      AuthenticatedSettingAreaDetailAreaIdRoute,
    AuthenticatedSettingAreaDetailIndexRoute:
      AuthenticatedSettingAreaDetailIndexRoute,
  }

const AuthenticatedSettingAreaDetailRouteWithChildren =
  AuthenticatedSettingAreaDetailRoute._addFileChildren(
    AuthenticatedSettingAreaDetailRouteChildren,
  )

interface AuthenticatedSettingAreaRouteChildren {
  AuthenticatedSettingAreaDetailRoute: typeof AuthenticatedSettingAreaDetailRouteWithChildren
  AuthenticatedSettingAreaIndexRoute: typeof AuthenticatedSettingAreaIndexRoute
}

const AuthenticatedSettingAreaRouteChildren: AuthenticatedSettingAreaRouteChildren =
  {
    AuthenticatedSettingAreaDetailRoute:
      AuthenticatedSettingAreaDetailRouteWithChildren,
    AuthenticatedSettingAreaIndexRoute: AuthenticatedSettingAreaIndexRoute,
  }

const AuthenticatedSettingAreaRouteWithChildren =
  AuthenticatedSettingAreaRoute._addFileChildren(
    AuthenticatedSettingAreaRouteChildren,
  )

interface AuthenticatedMenuCategoriesCategoriesInBrandDetailRouteChildren {
  AuthenticatedMenuCategoriesCategoriesInBrandDetailIdRoute: typeof AuthenticatedMenuCategoriesCategoriesInBrandDetailIdRoute
}

const AuthenticatedMenuCategoriesCategoriesInBrandDetailRouteChildren: AuthenticatedMenuCategoriesCategoriesInBrandDetailRouteChildren =
  {
    AuthenticatedMenuCategoriesCategoriesInBrandDetailIdRoute:
      AuthenticatedMenuCategoriesCategoriesInBrandDetailIdRoute,
  }

const AuthenticatedMenuCategoriesCategoriesInBrandDetailRouteWithChildren =
  AuthenticatedMenuCategoriesCategoriesInBrandDetailRoute._addFileChildren(
    AuthenticatedMenuCategoriesCategoriesInBrandDetailRouteChildren,
  )

interface AuthenticatedMenuCategoriesCategoriesInBrandRouteChildren {
  AuthenticatedMenuCategoriesCategoriesInBrandDetailRoute: typeof AuthenticatedMenuCategoriesCategoriesInBrandDetailRouteWithChildren
}

const AuthenticatedMenuCategoriesCategoriesInBrandRouteChildren: AuthenticatedMenuCategoriesCategoriesInBrandRouteChildren =
  {
    AuthenticatedMenuCategoriesCategoriesInBrandDetailRoute:
      AuthenticatedMenuCategoriesCategoriesInBrandDetailRouteWithChildren,
  }

const AuthenticatedMenuCategoriesCategoriesInBrandRouteWithChildren =
  AuthenticatedMenuCategoriesCategoriesInBrandRoute._addFileChildren(
    AuthenticatedMenuCategoriesCategoriesInBrandRouteChildren,
  )

interface AuthenticatedMenuCategoryDetailRouteChildren {
  AuthenticatedMenuCategoryDetailIdRoute: typeof AuthenticatedMenuCategoryDetailIdRoute
  AuthenticatedMenuCategoryDetailIndexRoute: typeof AuthenticatedMenuCategoryDetailIndexRoute
}

const AuthenticatedMenuCategoryDetailRouteChildren: AuthenticatedMenuCategoryDetailRouteChildren =
  {
    AuthenticatedMenuCategoryDetailIdRoute:
      AuthenticatedMenuCategoryDetailIdRoute,
    AuthenticatedMenuCategoryDetailIndexRoute:
      AuthenticatedMenuCategoryDetailIndexRoute,
  }

const AuthenticatedMenuCategoryDetailRouteWithChildren =
  AuthenticatedMenuCategoryDetailRoute._addFileChildren(
    AuthenticatedMenuCategoryDetailRouteChildren,
  )

interface AuthenticatedSalesChannelChannelDetailRouteChildren {
  AuthenticatedSalesChannelChannelDetailUuidRoute: typeof AuthenticatedSalesChannelChannelDetailUuidRoute
}

const AuthenticatedSalesChannelChannelDetailRouteChildren: AuthenticatedSalesChannelChannelDetailRouteChildren =
  {
    AuthenticatedSalesChannelChannelDetailUuidRoute:
      AuthenticatedSalesChannelChannelDetailUuidRoute,
  }

const AuthenticatedSalesChannelChannelDetailRouteWithChildren =
  AuthenticatedSalesChannelChannelDetailRoute._addFileChildren(
    AuthenticatedSalesChannelChannelDetailRouteChildren,
  )

interface AuthenticatedMenuCustomizationCustomizationInCityDetailRouteChildren {
  AuthenticatedMenuCustomizationCustomizationInCityDetailCustomizationIdRoute: typeof AuthenticatedMenuCustomizationCustomizationInCityDetailCustomizationIdRoute
}

const AuthenticatedMenuCustomizationCustomizationInCityDetailRouteChildren: AuthenticatedMenuCustomizationCustomizationInCityDetailRouteChildren =
  {
    AuthenticatedMenuCustomizationCustomizationInCityDetailCustomizationIdRoute:
      AuthenticatedMenuCustomizationCustomizationInCityDetailCustomizationIdRoute,
  }

const AuthenticatedMenuCustomizationCustomizationInCityDetailRouteWithChildren =
  AuthenticatedMenuCustomizationCustomizationInCityDetailRoute._addFileChildren(
    AuthenticatedMenuCustomizationCustomizationInCityDetailRouteChildren,
  )

interface AuthenticatedMenuCustomizationCustomizationInStoreDetailRouteChildren {
  AuthenticatedMenuCustomizationCustomizationInStoreDetailCustomizationIdRoute: typeof AuthenticatedMenuCustomizationCustomizationInStoreDetailCustomizationIdRoute
  AuthenticatedMenuCustomizationCustomizationInStoreDetailIndexRoute: typeof AuthenticatedMenuCustomizationCustomizationInStoreDetailIndexRoute
}

const AuthenticatedMenuCustomizationCustomizationInStoreDetailRouteChildren: AuthenticatedMenuCustomizationCustomizationInStoreDetailRouteChildren =
  {
    AuthenticatedMenuCustomizationCustomizationInStoreDetailCustomizationIdRoute:
      AuthenticatedMenuCustomizationCustomizationInStoreDetailCustomizationIdRoute,
    AuthenticatedMenuCustomizationCustomizationInStoreDetailIndexRoute:
      AuthenticatedMenuCustomizationCustomizationInStoreDetailIndexRoute,
  }

const AuthenticatedMenuCustomizationCustomizationInStoreDetailRouteWithChildren =
  AuthenticatedMenuCustomizationCustomizationInStoreDetailRoute._addFileChildren(
    AuthenticatedMenuCustomizationCustomizationInStoreDetailRouteChildren,
  )

interface AuthenticatedRouteRouteChildren {
  AuthenticatedSettingsRouteRoute: typeof AuthenticatedSettingsRouteRouteWithChildren
  AuthenticatedDesignSystemRoute: typeof AuthenticatedDesignSystemRoute
  AuthenticatedIndexRoute: typeof AuthenticatedIndexRoute
  AuthenticatedBaoCaoDoanhThuRoute: typeof AuthenticatedBaoCaoDoanhThuRoute
  AuthenticatedBaoCaoKiemSoatRoute: typeof AuthenticatedBaoCaoKiemSoatRoute
  AuthenticatedDevicesListRoute: typeof AuthenticatedDevicesListRoute
  AuthenticatedDevicesNewRoute: typeof AuthenticatedDevicesNewRoute
  AuthenticatedDevicesTypesRoute: typeof AuthenticatedDevicesTypesRoute
  AuthenticatedEmployeeDetailRoute: typeof AuthenticatedEmployeeDetailRouteWithChildren
  AuthenticatedEmployeeListRoute: typeof AuthenticatedEmployeeListRoute
  AuthenticatedEmployeeRoleRoute: typeof AuthenticatedEmployeeRoleRouteWithChildren
  AuthenticatedMenuItemClassRoute: typeof AuthenticatedMenuItemClassRouteWithChildren
  AuthenticatedMenuQuantityDayRoute: typeof AuthenticatedMenuQuantityDayRoute
  AuthenticatedMenuScheduleRoute: typeof AuthenticatedMenuScheduleRoute
  AuthenticatedSaleChannelDiscountRoute: typeof AuthenticatedSaleChannelDiscountRoute
  AuthenticatedSettingAreaRoute: typeof AuthenticatedSettingAreaRouteWithChildren
  AuthenticatedSettingStoreRoute: typeof AuthenticatedSettingStoreRoute
  AuthenticatedAppsIndexRoute: typeof AuthenticatedAppsIndexRoute
  AuthenticatedBaoCaoIndexRoute: typeof AuthenticatedBaoCaoIndexRoute
  AuthenticatedChatsIndexRoute: typeof AuthenticatedChatsIndexRoute
  AuthenticatedHelpCenterIndexRoute: typeof AuthenticatedHelpCenterIndexRoute
  AuthenticatedSaleIndexRoute: typeof AuthenticatedSaleIndexRoute
  AuthenticatedTasksIndexRoute: typeof AuthenticatedTasksIndexRoute
  AuthenticatedUsersIndexRoute: typeof AuthenticatedUsersIndexRoute
  AuthenticatedBaoCaoKeToanBangKeHoaDonRoute: typeof AuthenticatedBaoCaoKeToanBangKeHoaDonRoute
  AuthenticatedBaoCaoKeToanHoaDonRoute: typeof AuthenticatedBaoCaoKeToanHoaDonRoute
  AuthenticatedMenuCategoriesCategoriesInBrandRoute: typeof AuthenticatedMenuCategoriesCategoriesInBrandRouteWithChildren
  AuthenticatedMenuCategoryDetailRoute: typeof AuthenticatedMenuCategoryDetailRouteWithChildren
  AuthenticatedMenuCustomizationNewRoute: typeof AuthenticatedMenuCustomizationNewRoute
  AuthenticatedMenuItemRemovedItemRemovedInCityRoute: typeof AuthenticatedMenuItemRemovedItemRemovedInCityRoute
  AuthenticatedMenuItemRemovedItemRemovedInStoreRoute: typeof AuthenticatedMenuItemRemovedItemRemovedInStoreRoute
  AuthenticatedSalesChannelChannelDetailRoute: typeof AuthenticatedSalesChannelChannelDetailRouteWithChildren
  AuthenticatedBaoCaoKeToanIndexRoute: typeof AuthenticatedBaoCaoKeToanIndexRoute
  AuthenticatedSalePromotionIndexRoute: typeof AuthenticatedSalePromotionIndexRoute
  AuthenticatedSalesChannelChannelIndexRoute: typeof AuthenticatedSalesChannelChannelIndexRoute
  AuthenticatedSettingPaymentMethodIndexRoute: typeof AuthenticatedSettingPaymentMethodIndexRoute
  AuthenticatedSettingSourceIndexRoute: typeof AuthenticatedSettingSourceIndexRoute
  AuthenticatedMenuCustomizationCustomizationInCityDetailRoute: typeof AuthenticatedMenuCustomizationCustomizationInCityDetailRouteWithChildren
  AuthenticatedMenuCustomizationCustomizationInStoreCreateRoute: typeof AuthenticatedMenuCustomizationCustomizationInStoreCreateRoute
  AuthenticatedMenuCustomizationCustomizationInStoreDetailRoute: typeof AuthenticatedMenuCustomizationCustomizationInStoreDetailRouteWithChildren
  AuthenticatedSettingPaymentMethodDetailPaymentMethodIdRoute: typeof AuthenticatedSettingPaymentMethodDetailPaymentMethodIdRoute
  AuthenticatedSettingSourceDetailSourceIdRoute: typeof AuthenticatedSettingSourceDetailSourceIdRoute
  AuthenticatedMenuCustomizationCustomizationInCityIndexRoute: typeof AuthenticatedMenuCustomizationCustomizationInCityIndexRoute
  AuthenticatedMenuCustomizationCustomizationInStoreIndexRoute: typeof AuthenticatedMenuCustomizationCustomizationInStoreIndexRoute
  AuthenticatedMenuItemsItemsInCityIndexRoute: typeof AuthenticatedMenuItemsItemsInCityIndexRoute
  AuthenticatedMenuItemsItemsInStoreIndexRoute: typeof AuthenticatedMenuItemsItemsInStoreIndexRoute
  AuthenticatedSettingPaymentMethodDetailIndexRoute: typeof AuthenticatedSettingPaymentMethodDetailIndexRoute
  AuthenticatedSettingSourceDetailIndexRoute: typeof AuthenticatedSettingSourceDetailIndexRoute
}

const AuthenticatedRouteRouteChildren: AuthenticatedRouteRouteChildren = {
  AuthenticatedSettingsRouteRoute: AuthenticatedSettingsRouteRouteWithChildren,
  AuthenticatedDesignSystemRoute: AuthenticatedDesignSystemRoute,
  AuthenticatedIndexRoute: AuthenticatedIndexRoute,
  AuthenticatedBaoCaoDoanhThuRoute: AuthenticatedBaoCaoDoanhThuRoute,
  AuthenticatedBaoCaoKiemSoatRoute: AuthenticatedBaoCaoKiemSoatRoute,
  AuthenticatedDevicesListRoute: AuthenticatedDevicesListRoute,
  AuthenticatedDevicesNewRoute: AuthenticatedDevicesNewRoute,
  AuthenticatedDevicesTypesRoute: AuthenticatedDevicesTypesRoute,
  AuthenticatedEmployeeDetailRoute:
    AuthenticatedEmployeeDetailRouteWithChildren,
  AuthenticatedEmployeeListRoute: AuthenticatedEmployeeListRoute,
  AuthenticatedEmployeeRoleRoute: AuthenticatedEmployeeRoleRouteWithChildren,
  AuthenticatedMenuItemClassRoute: AuthenticatedMenuItemClassRouteWithChildren,
  AuthenticatedMenuQuantityDayRoute: AuthenticatedMenuQuantityDayRoute,
  AuthenticatedMenuScheduleRoute: AuthenticatedMenuScheduleRoute,
  AuthenticatedSaleChannelDiscountRoute: AuthenticatedSaleChannelDiscountRoute,
  AuthenticatedSettingAreaRoute: AuthenticatedSettingAreaRouteWithChildren,
  AuthenticatedSettingStoreRoute: AuthenticatedSettingStoreRoute,
  AuthenticatedAppsIndexRoute: AuthenticatedAppsIndexRoute,
  AuthenticatedBaoCaoIndexRoute: AuthenticatedBaoCaoIndexRoute,
  AuthenticatedChatsIndexRoute: AuthenticatedChatsIndexRoute,
  AuthenticatedHelpCenterIndexRoute: AuthenticatedHelpCenterIndexRoute,
  AuthenticatedSaleIndexRoute: AuthenticatedSaleIndexRoute,
  AuthenticatedTasksIndexRoute: AuthenticatedTasksIndexRoute,
  AuthenticatedUsersIndexRoute: AuthenticatedUsersIndexRoute,
  AuthenticatedBaoCaoKeToanBangKeHoaDonRoute:
    AuthenticatedBaoCaoKeToanBangKeHoaDonRoute,
  AuthenticatedBaoCaoKeToanHoaDonRoute: AuthenticatedBaoCaoKeToanHoaDonRoute,
  AuthenticatedMenuCategoriesCategoriesInBrandRoute:
    AuthenticatedMenuCategoriesCategoriesInBrandRouteWithChildren,
  AuthenticatedMenuCategoryDetailRoute:
    AuthenticatedMenuCategoryDetailRouteWithChildren,
  AuthenticatedMenuCustomizationNewRoute:
    AuthenticatedMenuCustomizationNewRoute,
  AuthenticatedMenuItemRemovedItemRemovedInCityRoute:
    AuthenticatedMenuItemRemovedItemRemovedInCityRoute,
  AuthenticatedMenuItemRemovedItemRemovedInStoreRoute:
    AuthenticatedMenuItemRemovedItemRemovedInStoreRoute,
  AuthenticatedSalesChannelChannelDetailRoute:
    AuthenticatedSalesChannelChannelDetailRouteWithChildren,
  AuthenticatedBaoCaoKeToanIndexRoute: AuthenticatedBaoCaoKeToanIndexRoute,
  AuthenticatedSalePromotionIndexRoute: AuthenticatedSalePromotionIndexRoute,
  AuthenticatedSalesChannelChannelIndexRoute:
    AuthenticatedSalesChannelChannelIndexRoute,
  AuthenticatedSettingPaymentMethodIndexRoute:
    AuthenticatedSettingPaymentMethodIndexRoute,
  AuthenticatedSettingSourceIndexRoute: AuthenticatedSettingSourceIndexRoute,
  AuthenticatedMenuCustomizationCustomizationInCityDetailRoute:
    AuthenticatedMenuCustomizationCustomizationInCityDetailRouteWithChildren,
  AuthenticatedMenuCustomizationCustomizationInStoreCreateRoute:
    AuthenticatedMenuCustomizationCustomizationInStoreCreateRoute,
  AuthenticatedMenuCustomizationCustomizationInStoreDetailRoute:
    AuthenticatedMenuCustomizationCustomizationInStoreDetailRouteWithChildren,
  AuthenticatedSettingPaymentMethodDetailPaymentMethodIdRoute:
    AuthenticatedSettingPaymentMethodDetailPaymentMethodIdRoute,
  AuthenticatedSettingSourceDetailSourceIdRoute:
    AuthenticatedSettingSourceDetailSourceIdRoute,
  AuthenticatedMenuCustomizationCustomizationInCityIndexRoute:
    AuthenticatedMenuCustomizationCustomizationInCityIndexRoute,
  AuthenticatedMenuCustomizationCustomizationInStoreIndexRoute:
    AuthenticatedMenuCustomizationCustomizationInStoreIndexRoute,
  AuthenticatedMenuItemsItemsInCityIndexRoute:
    AuthenticatedMenuItemsItemsInCityIndexRoute,
  AuthenticatedMenuItemsItemsInStoreIndexRoute:
    AuthenticatedMenuItemsItemsInStoreIndexRoute,
  AuthenticatedSettingPaymentMethodDetailIndexRoute:
    AuthenticatedSettingPaymentMethodDetailIndexRoute,
  AuthenticatedSettingSourceDetailIndexRoute:
    AuthenticatedSettingSourceDetailIndexRoute,
}

const AuthenticatedRouteRouteWithChildren =
  AuthenticatedRouteRoute._addFileChildren(AuthenticatedRouteRouteChildren)

interface ClerkauthRouteRouteChildren {
  ClerkauthSignInRoute: typeof ClerkauthSignInRoute
  ClerkauthSignUpRoute: typeof ClerkauthSignUpRoute
}

const ClerkauthRouteRouteChildren: ClerkauthRouteRouteChildren = {
  ClerkauthSignInRoute: ClerkauthSignInRoute,
  ClerkauthSignUpRoute: ClerkauthSignUpRoute,
}

const ClerkauthRouteRouteWithChildren = ClerkauthRouteRoute._addFileChildren(
  ClerkauthRouteRouteChildren,
)

interface ClerkAuthenticatedRouteRouteChildren {
  ClerkAuthenticatedUserManagementRoute: typeof ClerkAuthenticatedUserManagementRoute
}

const ClerkAuthenticatedRouteRouteChildren: ClerkAuthenticatedRouteRouteChildren =
  {
    ClerkAuthenticatedUserManagementRoute:
      ClerkAuthenticatedUserManagementRoute,
  }

const ClerkAuthenticatedRouteRouteWithChildren =
  ClerkAuthenticatedRouteRoute._addFileChildren(
    ClerkAuthenticatedRouteRouteChildren,
  )

interface ClerkRouteRouteChildren {
  ClerkauthRouteRoute: typeof ClerkauthRouteRouteWithChildren
  ClerkAuthenticatedRouteRoute: typeof ClerkAuthenticatedRouteRouteWithChildren
}

const ClerkRouteRouteChildren: ClerkRouteRouteChildren = {
  ClerkauthRouteRoute: ClerkauthRouteRouteWithChildren,
  ClerkAuthenticatedRouteRoute: ClerkAuthenticatedRouteRouteWithChildren,
}

const ClerkRouteRouteWithChildren = ClerkRouteRoute._addFileChildren(
  ClerkRouteRouteChildren,
)

export interface FileRoutesByFullPath {
  '': typeof AuthenticatedRouteRouteWithChildren
  '/clerk': typeof ClerkAuthenticatedRouteRouteWithChildren
  '/settings': typeof AuthenticatedSettingsRouteRouteWithChildren
  '/clerk/': typeof ClerkauthRouteRouteWithChildren
  '/forgot-password': typeof authForgotPasswordRoute
  '/otp': typeof authOtpRoute
  '/sign-in': typeof authSignInRoute
  '/sign-in-2': typeof authSignIn2Route
  '/sign-up': typeof authSignUpRoute
  '/401': typeof errors401Route
  '/403': typeof errors403Route
  '/404': typeof errors404Route
  '/500': typeof errors500Route
  '/503': typeof errors503Route
  '/design-system': typeof AuthenticatedDesignSystemRoute
  '/': typeof AuthenticatedIndexRoute
  '/bao-cao/doanh-thu': typeof AuthenticatedBaoCaoDoanhThuRoute
  '/bao-cao/kiem-soat': typeof AuthenticatedBaoCaoKiemSoatRoute
  '/devices/list': typeof AuthenticatedDevicesListRoute
  '/devices/new': typeof AuthenticatedDevicesNewRoute
  '/devices/types': typeof AuthenticatedDevicesTypesRoute
  '/employee/detail': typeof AuthenticatedEmployeeDetailRouteWithChildren
  '/employee/list': typeof AuthenticatedEmployeeListRoute
  '/employee/role': typeof AuthenticatedEmployeeRoleRouteWithChildren
  '/menu/item-class': typeof AuthenticatedMenuItemClassRouteWithChildren
  '/menu/quantity-day': typeof AuthenticatedMenuQuantityDayRoute
  '/menu/schedule': typeof AuthenticatedMenuScheduleRoute
  '/sale-channel/discount': typeof AuthenticatedSaleChannelDiscountRoute
  '/setting/area': typeof AuthenticatedSettingAreaRouteWithChildren
  '/setting/store': typeof AuthenticatedSettingStoreRoute
  '/settings/account': typeof AuthenticatedSettingsAccountRoute
  '/settings/appearance': typeof AuthenticatedSettingsAppearanceRoute
  '/settings/display': typeof AuthenticatedSettingsDisplayRoute
  '/settings/notifications': typeof AuthenticatedSettingsNotificationsRoute
  '/clerk/sign-in': typeof ClerkauthSignInRoute
  '/clerk/sign-up': typeof ClerkauthSignUpRoute
  '/clerk/user-management': typeof ClerkAuthenticatedUserManagementRoute
  '/apps': typeof AuthenticatedAppsIndexRoute
  '/bao-cao': typeof AuthenticatedBaoCaoIndexRoute
  '/chats': typeof AuthenticatedChatsIndexRoute
  '/help-center': typeof AuthenticatedHelpCenterIndexRoute
  '/sale': typeof AuthenticatedSaleIndexRoute
  '/settings/': typeof AuthenticatedSettingsIndexRoute
  '/tasks': typeof AuthenticatedTasksIndexRoute
  '/users': typeof AuthenticatedUsersIndexRoute
  '/bao-cao/ke-toan/bang-ke-hoa-don': typeof AuthenticatedBaoCaoKeToanBangKeHoaDonRoute
  '/bao-cao/ke-toan/hoa-don': typeof AuthenticatedBaoCaoKeToanHoaDonRoute
  '/employee/detail/$userId': typeof AuthenticatedEmployeeDetailUserIdRoute
  '/employee/role/detail': typeof AuthenticatedEmployeeRoleDetailRoute
  '/menu/categories/categories-in-brand': typeof AuthenticatedMenuCategoriesCategoriesInBrandRouteWithChildren
  '/menu/category/detail': typeof AuthenticatedMenuCategoryDetailRouteWithChildren
  '/menu/customization/new': typeof AuthenticatedMenuCustomizationNewRoute
  '/menu/item-class/detail': typeof AuthenticatedMenuItemClassDetailRouteWithChildren
  '/menu/item-removed/item-removed-in-city': typeof AuthenticatedMenuItemRemovedItemRemovedInCityRoute
  '/menu/item-removed/item-removed-in-store': typeof AuthenticatedMenuItemRemovedItemRemovedInStoreRoute
  '/sales-channel/channel/detail': typeof AuthenticatedSalesChannelChannelDetailRouteWithChildren
  '/setting/area/detail': typeof AuthenticatedSettingAreaDetailRouteWithChildren
  '/bao-cao/ke-toan': typeof AuthenticatedBaoCaoKeToanIndexRoute
  '/employee/role/': typeof AuthenticatedEmployeeRoleIndexRoute
  '/menu/item-class/': typeof AuthenticatedMenuItemClassIndexRoute
  '/sale/promotion': typeof AuthenticatedSalePromotionIndexRoute
  '/sales-channel/channel': typeof AuthenticatedSalesChannelChannelIndexRoute
  '/setting/area/': typeof AuthenticatedSettingAreaIndexRoute
  '/setting/payment-method': typeof AuthenticatedSettingPaymentMethodIndexRoute
  '/setting/source': typeof AuthenticatedSettingSourceIndexRoute
  '/menu/categories/categories-in-brand/detail': typeof AuthenticatedMenuCategoriesCategoriesInBrandDetailRouteWithChildren
  '/menu/category/detail/$id': typeof AuthenticatedMenuCategoryDetailIdRoute
  '/menu/customization/customization-in-city/detail': typeof AuthenticatedMenuCustomizationCustomizationInCityDetailRouteWithChildren
  '/menu/customization/customization-in-store/create': typeof AuthenticatedMenuCustomizationCustomizationInStoreCreateRoute
  '/menu/customization/customization-in-store/detail': typeof AuthenticatedMenuCustomizationCustomizationInStoreDetailRouteWithChildren
  '/menu/item-class/detail/$id': typeof AuthenticatedMenuItemClassDetailIdRoute
  '/sales-channel/channel/detail/$uuid': typeof AuthenticatedSalesChannelChannelDetailUuidRoute
  '/setting/area/detail/$areaId': typeof AuthenticatedSettingAreaDetailAreaIdRoute
  '/setting/payment-method/detail/$paymentMethodId': typeof AuthenticatedSettingPaymentMethodDetailPaymentMethodIdRoute
  '/setting/source/detail/$sourceId': typeof AuthenticatedSettingSourceDetailSourceIdRoute
  '/menu/category/detail/': typeof AuthenticatedMenuCategoryDetailIndexRoute
  '/menu/customization/customization-in-city': typeof AuthenticatedMenuCustomizationCustomizationInCityIndexRoute
  '/menu/customization/customization-in-store': typeof AuthenticatedMenuCustomizationCustomizationInStoreIndexRoute
  '/menu/item-class/detail/': typeof AuthenticatedMenuItemClassDetailIndexRoute
  '/menu/items/items-in-city': typeof AuthenticatedMenuItemsItemsInCityIndexRoute
  '/menu/items/items-in-store': typeof AuthenticatedMenuItemsItemsInStoreIndexRoute
  '/setting/area/detail/': typeof AuthenticatedSettingAreaDetailIndexRoute
  '/setting/payment-method/detail': typeof AuthenticatedSettingPaymentMethodDetailIndexRoute
  '/setting/source/detail': typeof AuthenticatedSettingSourceDetailIndexRoute
  '/menu/categories/categories-in-brand/detail/$id': typeof AuthenticatedMenuCategoriesCategoriesInBrandDetailIdRoute
  '/menu/customization/customization-in-city/detail/$customizationId': typeof AuthenticatedMenuCustomizationCustomizationInCityDetailCustomizationIdRoute
  '/menu/customization/customization-in-store/detail/$customizationId': typeof AuthenticatedMenuCustomizationCustomizationInStoreDetailCustomizationIdRoute
  '/menu/customization/customization-in-store/detail/': typeof AuthenticatedMenuCustomizationCustomizationInStoreDetailIndexRoute
}

export interface FileRoutesByTo {
  '/clerk': typeof ClerkAuthenticatedRouteRouteWithChildren
  '/forgot-password': typeof authForgotPasswordRoute
  '/otp': typeof authOtpRoute
  '/sign-in': typeof authSignInRoute
  '/sign-in-2': typeof authSignIn2Route
  '/sign-up': typeof authSignUpRoute
  '/401': typeof errors401Route
  '/403': typeof errors403Route
  '/404': typeof errors404Route
  '/500': typeof errors500Route
  '/503': typeof errors503Route
  '/design-system': typeof AuthenticatedDesignSystemRoute
  '/': typeof AuthenticatedIndexRoute
  '/bao-cao/doanh-thu': typeof AuthenticatedBaoCaoDoanhThuRoute
  '/bao-cao/kiem-soat': typeof AuthenticatedBaoCaoKiemSoatRoute
  '/devices/list': typeof AuthenticatedDevicesListRoute
  '/devices/new': typeof AuthenticatedDevicesNewRoute
  '/devices/types': typeof AuthenticatedDevicesTypesRoute
  '/employee/detail': typeof AuthenticatedEmployeeDetailRouteWithChildren
  '/employee/list': typeof AuthenticatedEmployeeListRoute
  '/menu/quantity-day': typeof AuthenticatedMenuQuantityDayRoute
  '/menu/schedule': typeof AuthenticatedMenuScheduleRoute
  '/sale-channel/discount': typeof AuthenticatedSaleChannelDiscountRoute
  '/setting/store': typeof AuthenticatedSettingStoreRoute
  '/settings/account': typeof AuthenticatedSettingsAccountRoute
  '/settings/appearance': typeof AuthenticatedSettingsAppearanceRoute
  '/settings/display': typeof AuthenticatedSettingsDisplayRoute
  '/settings/notifications': typeof AuthenticatedSettingsNotificationsRoute
  '/clerk/sign-in': typeof ClerkauthSignInRoute
  '/clerk/sign-up': typeof ClerkauthSignUpRoute
  '/clerk/user-management': typeof ClerkAuthenticatedUserManagementRoute
  '/apps': typeof AuthenticatedAppsIndexRoute
  '/bao-cao': typeof AuthenticatedBaoCaoIndexRoute
  '/chats': typeof AuthenticatedChatsIndexRoute
  '/help-center': typeof AuthenticatedHelpCenterIndexRoute
  '/sale': typeof AuthenticatedSaleIndexRoute
  '/settings': typeof AuthenticatedSettingsIndexRoute
  '/tasks': typeof AuthenticatedTasksIndexRoute
  '/users': typeof AuthenticatedUsersIndexRoute
  '/bao-cao/ke-toan/bang-ke-hoa-don': typeof AuthenticatedBaoCaoKeToanBangKeHoaDonRoute
  '/bao-cao/ke-toan/hoa-don': typeof AuthenticatedBaoCaoKeToanHoaDonRoute
  '/employee/detail/$userId': typeof AuthenticatedEmployeeDetailUserIdRoute
  '/employee/role/detail': typeof AuthenticatedEmployeeRoleDetailRoute
  '/menu/categories/categories-in-brand': typeof AuthenticatedMenuCategoriesCategoriesInBrandRouteWithChildren
  '/menu/customization/new': typeof AuthenticatedMenuCustomizationNewRoute
  '/menu/item-removed/item-removed-in-city': typeof AuthenticatedMenuItemRemovedItemRemovedInCityRoute
  '/menu/item-removed/item-removed-in-store': typeof AuthenticatedMenuItemRemovedItemRemovedInStoreRoute
  '/sales-channel/channel/detail': typeof AuthenticatedSalesChannelChannelDetailRouteWithChildren
  '/bao-cao/ke-toan': typeof AuthenticatedBaoCaoKeToanIndexRoute
  '/employee/role': typeof AuthenticatedEmployeeRoleIndexRoute
  '/menu/item-class': typeof AuthenticatedMenuItemClassIndexRoute
  '/sale/promotion': typeof AuthenticatedSalePromotionIndexRoute
  '/sales-channel/channel': typeof AuthenticatedSalesChannelChannelIndexRoute
  '/setting/area': typeof AuthenticatedSettingAreaIndexRoute
  '/setting/payment-method': typeof AuthenticatedSettingPaymentMethodIndexRoute
  '/setting/source': typeof AuthenticatedSettingSourceIndexRoute
  '/menu/categories/categories-in-brand/detail': typeof AuthenticatedMenuCategoriesCategoriesInBrandDetailRouteWithChildren
  '/menu/category/detail/$id': typeof AuthenticatedMenuCategoryDetailIdRoute
  '/menu/customization/customization-in-city/detail': typeof AuthenticatedMenuCustomizationCustomizationInCityDetailRouteWithChildren
  '/menu/customization/customization-in-store/create': typeof AuthenticatedMenuCustomizationCustomizationInStoreCreateRoute
  '/menu/item-class/detail/$id': typeof AuthenticatedMenuItemClassDetailIdRoute
  '/sales-channel/channel/detail/$uuid': typeof AuthenticatedSalesChannelChannelDetailUuidRoute
  '/setting/area/detail/$areaId': typeof AuthenticatedSettingAreaDetailAreaIdRoute
  '/setting/payment-method/detail/$paymentMethodId': typeof AuthenticatedSettingPaymentMethodDetailPaymentMethodIdRoute
  '/setting/source/detail/$sourceId': typeof AuthenticatedSettingSourceDetailSourceIdRoute
  '/menu/category/detail': typeof AuthenticatedMenuCategoryDetailIndexRoute
  '/menu/customization/customization-in-city': typeof AuthenticatedMenuCustomizationCustomizationInCityIndexRoute
  '/menu/customization/customization-in-store': typeof AuthenticatedMenuCustomizationCustomizationInStoreIndexRoute
  '/menu/item-class/detail': typeof AuthenticatedMenuItemClassDetailIndexRoute
  '/menu/items/items-in-city': typeof AuthenticatedMenuItemsItemsInCityIndexRoute
  '/menu/items/items-in-store': typeof AuthenticatedMenuItemsItemsInStoreIndexRoute
  '/setting/area/detail': typeof AuthenticatedSettingAreaDetailIndexRoute
  '/setting/payment-method/detail': typeof AuthenticatedSettingPaymentMethodDetailIndexRoute
  '/setting/source/detail': typeof AuthenticatedSettingSourceDetailIndexRoute
  '/menu/categories/categories-in-brand/detail/$id': typeof AuthenticatedMenuCategoriesCategoriesInBrandDetailIdRoute
  '/menu/customization/customization-in-city/detail/$customizationId': typeof AuthenticatedMenuCustomizationCustomizationInCityDetailCustomizationIdRoute
  '/menu/customization/customization-in-store/detail/$customizationId': typeof AuthenticatedMenuCustomizationCustomizationInStoreDetailCustomizationIdRoute
  '/menu/customization/customization-in-store/detail': typeof AuthenticatedMenuCustomizationCustomizationInStoreDetailIndexRoute
}

export interface FileRoutesById {
  __root__: typeof rootRoute
  '/_authenticated': typeof AuthenticatedRouteRouteWithChildren
  '/clerk': typeof ClerkRouteRouteWithChildren
  '/_authenticated/settings': typeof AuthenticatedSettingsRouteRouteWithChildren
  '/clerk/(auth)': typeof ClerkauthRouteRouteWithChildren
  '/clerk/_authenticated': typeof ClerkAuthenticatedRouteRouteWithChildren
  '/(auth)/forgot-password': typeof authForgotPasswordRoute
  '/(auth)/otp': typeof authOtpRoute
  '/(auth)/sign-in': typeof authSignInRoute
  '/(auth)/sign-in-2': typeof authSignIn2Route
  '/(auth)/sign-up': typeof authSignUpRoute
  '/(errors)/401': typeof errors401Route
  '/(errors)/403': typeof errors403Route
  '/(errors)/404': typeof errors404Route
  '/(errors)/500': typeof errors500Route
  '/(errors)/503': typeof errors503Route
  '/_authenticated/design-system': typeof AuthenticatedDesignSystemRoute
  '/_authenticated/': typeof AuthenticatedIndexRoute
  '/_authenticated/bao-cao/doanh-thu': typeof AuthenticatedBaoCaoDoanhThuRoute
  '/_authenticated/bao-cao/kiem-soat': typeof AuthenticatedBaoCaoKiemSoatRoute
  '/_authenticated/devices/list': typeof AuthenticatedDevicesListRoute
  '/_authenticated/devices/new': typeof AuthenticatedDevicesNewRoute
  '/_authenticated/devices/types': typeof AuthenticatedDevicesTypesRoute
  '/_authenticated/employee/detail': typeof AuthenticatedEmployeeDetailRouteWithChildren
  '/_authenticated/employee/list': typeof AuthenticatedEmployeeListRoute
  '/_authenticated/employee/role': typeof AuthenticatedEmployeeRoleRouteWithChildren
  '/_authenticated/menu/item-class': typeof AuthenticatedMenuItemClassRouteWithChildren
  '/_authenticated/menu/quantity-day': typeof AuthenticatedMenuQuantityDayRoute
  '/_authenticated/menu/schedule': typeof AuthenticatedMenuScheduleRoute
  '/_authenticated/sale-channel/discount': typeof AuthenticatedSaleChannelDiscountRoute
  '/_authenticated/setting/area': typeof AuthenticatedSettingAreaRouteWithChildren
  '/_authenticated/setting/store': typeof AuthenticatedSettingStoreRoute
  '/_authenticated/settings/account': typeof AuthenticatedSettingsAccountRoute
  '/_authenticated/settings/appearance': typeof AuthenticatedSettingsAppearanceRoute
  '/_authenticated/settings/display': typeof AuthenticatedSettingsDisplayRoute
  '/_authenticated/settings/notifications': typeof AuthenticatedSettingsNotificationsRoute
  '/clerk/(auth)/sign-in': typeof ClerkauthSignInRoute
  '/clerk/(auth)/sign-up': typeof ClerkauthSignUpRoute
  '/clerk/_authenticated/user-management': typeof ClerkAuthenticatedUserManagementRoute
  '/_authenticated/apps/': typeof AuthenticatedAppsIndexRoute
  '/_authenticated/bao-cao/': typeof AuthenticatedBaoCaoIndexRoute
  '/_authenticated/chats/': typeof AuthenticatedChatsIndexRoute
  '/_authenticated/help-center/': typeof AuthenticatedHelpCenterIndexRoute
  '/_authenticated/sale/': typeof AuthenticatedSaleIndexRoute
  '/_authenticated/settings/': typeof AuthenticatedSettingsIndexRoute
  '/_authenticated/tasks/': typeof AuthenticatedTasksIndexRoute
  '/_authenticated/users/': typeof AuthenticatedUsersIndexRoute
  '/_authenticated/bao-cao/ke-toan/bang-ke-hoa-don': typeof AuthenticatedBaoCaoKeToanBangKeHoaDonRoute
  '/_authenticated/bao-cao/ke-toan/hoa-don': typeof AuthenticatedBaoCaoKeToanHoaDonRoute
  '/_authenticated/employee/detail/$userId': typeof AuthenticatedEmployeeDetailUserIdRoute
  '/_authenticated/employee/role/detail': typeof AuthenticatedEmployeeRoleDetailRoute
  '/_authenticated/menu/categories/categories-in-brand': typeof AuthenticatedMenuCategoriesCategoriesInBrandRouteWithChildren
  '/_authenticated/menu/category/detail': typeof AuthenticatedMenuCategoryDetailRouteWithChildren
  '/_authenticated/menu/customization/new': typeof AuthenticatedMenuCustomizationNewRoute
  '/_authenticated/menu/item-class/detail': typeof AuthenticatedMenuItemClassDetailRouteWithChildren
  '/_authenticated/menu/item-removed/item-removed-in-city': typeof AuthenticatedMenuItemRemovedItemRemovedInCityRoute
  '/_authenticated/menu/item-removed/item-removed-in-store': typeof AuthenticatedMenuItemRemovedItemRemovedInStoreRoute
  '/_authenticated/sales-channel/channel/detail': typeof AuthenticatedSalesChannelChannelDetailRouteWithChildren
  '/_authenticated/setting/area/detail': typeof AuthenticatedSettingAreaDetailRouteWithChildren
  '/_authenticated/bao-cao/ke-toan/': typeof AuthenticatedBaoCaoKeToanIndexRoute
  '/_authenticated/employee/role/': typeof AuthenticatedEmployeeRoleIndexRoute
  '/_authenticated/menu/item-class/': typeof AuthenticatedMenuItemClassIndexRoute
  '/_authenticated/sale/promotion/': typeof AuthenticatedSalePromotionIndexRoute
  '/_authenticated/sales-channel/channel/': typeof AuthenticatedSalesChannelChannelIndexRoute
  '/_authenticated/setting/area/': typeof AuthenticatedSettingAreaIndexRoute
  '/_authenticated/setting/payment-method/': typeof AuthenticatedSettingPaymentMethodIndexRoute
  '/_authenticated/setting/source/': typeof AuthenticatedSettingSourceIndexRoute
  '/_authenticated/menu/categories/categories-in-brand/detail': typeof AuthenticatedMenuCategoriesCategoriesInBrandDetailRouteWithChildren
  '/_authenticated/menu/category/detail/$id': typeof AuthenticatedMenuCategoryDetailIdRoute
  '/_authenticated/menu/customization/customization-in-city/detail': typeof AuthenticatedMenuCustomizationCustomizationInCityDetailRouteWithChildren
  '/_authenticated/menu/customization/customization-in-store/create': typeof AuthenticatedMenuCustomizationCustomizationInStoreCreateRoute
  '/_authenticated/menu/customization/customization-in-store/detail': typeof AuthenticatedMenuCustomizationCustomizationInStoreDetailRouteWithChildren
  '/_authenticated/menu/item-class/detail/$id': typeof AuthenticatedMenuItemClassDetailIdRoute
  '/_authenticated/sales-channel/channel/detail/$uuid': typeof AuthenticatedSalesChannelChannelDetailUuidRoute
  '/_authenticated/setting/area/detail/$areaId': typeof AuthenticatedSettingAreaDetailAreaIdRoute
  '/_authenticated/setting/payment-method/detail/$paymentMethodId': typeof AuthenticatedSettingPaymentMethodDetailPaymentMethodIdRoute
  '/_authenticated/setting/source/detail/$sourceId': typeof AuthenticatedSettingSourceDetailSourceIdRoute
  '/_authenticated/menu/category/detail/': typeof AuthenticatedMenuCategoryDetailIndexRoute
  '/_authenticated/menu/customization/customization-in-city/': typeof AuthenticatedMenuCustomizationCustomizationInCityIndexRoute
  '/_authenticated/menu/customization/customization-in-store/': typeof AuthenticatedMenuCustomizationCustomizationInStoreIndexRoute
  '/_authenticated/menu/item-class/detail/': typeof AuthenticatedMenuItemClassDetailIndexRoute
  '/_authenticated/menu/items/items-in-city/': typeof AuthenticatedMenuItemsItemsInCityIndexRoute
  '/_authenticated/menu/items/items-in-store/': typeof AuthenticatedMenuItemsItemsInStoreIndexRoute
  '/_authenticated/setting/area/detail/': typeof AuthenticatedSettingAreaDetailIndexRoute
  '/_authenticated/setting/payment-method/detail/': typeof AuthenticatedSettingPaymentMethodDetailIndexRoute
  '/_authenticated/setting/source/detail/': typeof AuthenticatedSettingSourceDetailIndexRoute
  '/_authenticated/menu/categories/categories-in-brand/detail/$id': typeof AuthenticatedMenuCategoriesCategoriesInBrandDetailIdRoute
  '/_authenticated/menu/customization/customization-in-city/detail/$customizationId': typeof AuthenticatedMenuCustomizationCustomizationInCityDetailCustomizationIdRoute
  '/_authenticated/menu/customization/customization-in-store/detail/$customizationId': typeof AuthenticatedMenuCustomizationCustomizationInStoreDetailCustomizationIdRoute
  '/_authenticated/menu/customization/customization-in-store/detail/': typeof AuthenticatedMenuCustomizationCustomizationInStoreDetailIndexRoute
}

export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | ''
    | '/clerk'
    | '/settings'
    | '/clerk/'
    | '/forgot-password'
    | '/otp'
    | '/sign-in'
    | '/sign-in-2'
    | '/sign-up'
    | '/401'
    | '/403'
    | '/404'
    | '/500'
    | '/503'
    | '/design-system'
    | '/'
    | '/bao-cao/doanh-thu'
    | '/bao-cao/kiem-soat'
    | '/devices/list'
    | '/devices/new'
    | '/devices/types'
    | '/employee/detail'
    | '/employee/list'
    | '/employee/role'
    | '/menu/item-class'
    | '/menu/quantity-day'
    | '/menu/schedule'
    | '/sale-channel/discount'
    | '/setting/area'
    | '/setting/store'
    | '/settings/account'
    | '/settings/appearance'
    | '/settings/display'
    | '/settings/notifications'
    | '/clerk/sign-in'
    | '/clerk/sign-up'
    | '/clerk/user-management'
    | '/apps'
    | '/bao-cao'
    | '/chats'
    | '/help-center'
    | '/sale'
    | '/settings/'
    | '/tasks'
    | '/users'
    | '/bao-cao/ke-toan/bang-ke-hoa-don'
    | '/bao-cao/ke-toan/hoa-don'
    | '/employee/detail/$userId'
    | '/employee/role/detail'
    | '/menu/categories/categories-in-brand'
    | '/menu/category/detail'
    | '/menu/customization/new'
    | '/menu/item-class/detail'
    | '/menu/item-removed/item-removed-in-city'
    | '/menu/item-removed/item-removed-in-store'
    | '/sales-channel/channel/detail'
    | '/setting/area/detail'
    | '/bao-cao/ke-toan'
    | '/employee/role/'
    | '/menu/item-class/'
    | '/sale/promotion'
    | '/sales-channel/channel'
    | '/setting/area/'
    | '/setting/payment-method'
    | '/setting/source'
    | '/menu/categories/categories-in-brand/detail'
    | '/menu/category/detail/$id'
    | '/menu/customization/customization-in-city/detail'
    | '/menu/customization/customization-in-store/create'
    | '/menu/customization/customization-in-store/detail'
    | '/menu/item-class/detail/$id'
    | '/sales-channel/channel/detail/$uuid'
    | '/setting/area/detail/$areaId'
    | '/setting/payment-method/detail/$paymentMethodId'
    | '/setting/source/detail/$sourceId'
    | '/menu/category/detail/'
    | '/menu/customization/customization-in-city'
    | '/menu/customization/customization-in-store'
    | '/menu/item-class/detail/'
    | '/menu/items/items-in-city'
    | '/menu/items/items-in-store'
    | '/setting/area/detail/'
    | '/setting/payment-method/detail'
    | '/setting/source/detail'
    | '/menu/categories/categories-in-brand/detail/$id'
    | '/menu/customization/customization-in-city/detail/$customizationId'
    | '/menu/customization/customization-in-store/detail/$customizationId'
    | '/menu/customization/customization-in-store/detail/'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/clerk'
    | '/forgot-password'
    | '/otp'
    | '/sign-in'
    | '/sign-in-2'
    | '/sign-up'
    | '/401'
    | '/403'
    | '/404'
    | '/500'
    | '/503'
    | '/design-system'
    | '/'
    | '/bao-cao/doanh-thu'
    | '/bao-cao/kiem-soat'
    | '/devices/list'
    | '/devices/new'
    | '/devices/types'
    | '/employee/detail'
    | '/employee/list'
    | '/menu/quantity-day'
    | '/menu/schedule'
    | '/sale-channel/discount'
    | '/setting/store'
    | '/settings/account'
    | '/settings/appearance'
    | '/settings/display'
    | '/settings/notifications'
    | '/clerk/sign-in'
    | '/clerk/sign-up'
    | '/clerk/user-management'
    | '/apps'
    | '/bao-cao'
    | '/chats'
    | '/help-center'
    | '/sale'
    | '/settings'
    | '/tasks'
    | '/users'
    | '/bao-cao/ke-toan/bang-ke-hoa-don'
    | '/bao-cao/ke-toan/hoa-don'
    | '/employee/detail/$userId'
    | '/employee/role/detail'
    | '/menu/categories/categories-in-brand'
    | '/menu/customization/new'
    | '/menu/item-removed/item-removed-in-city'
    | '/menu/item-removed/item-removed-in-store'
    | '/sales-channel/channel/detail'
    | '/bao-cao/ke-toan'
    | '/employee/role'
    | '/menu/item-class'
    | '/sale/promotion'
    | '/sales-channel/channel'
    | '/setting/area'
    | '/setting/payment-method'
    | '/setting/source'
    | '/menu/categories/categories-in-brand/detail'
    | '/menu/category/detail/$id'
    | '/menu/customization/customization-in-city/detail'
    | '/menu/customization/customization-in-store/create'
    | '/menu/item-class/detail/$id'
    | '/sales-channel/channel/detail/$uuid'
    | '/setting/area/detail/$areaId'
    | '/setting/payment-method/detail/$paymentMethodId'
    | '/setting/source/detail/$sourceId'
    | '/menu/category/detail'
    | '/menu/customization/customization-in-city'
    | '/menu/customization/customization-in-store'
    | '/menu/item-class/detail'
    | '/menu/items/items-in-city'
    | '/menu/items/items-in-store'
    | '/setting/area/detail'
    | '/setting/payment-method/detail'
    | '/setting/source/detail'
    | '/menu/categories/categories-in-brand/detail/$id'
    | '/menu/customization/customization-in-city/detail/$customizationId'
    | '/menu/customization/customization-in-store/detail/$customizationId'
    | '/menu/customization/customization-in-store/detail'
  id:
    | '__root__'
    | '/_authenticated'
    | '/clerk'
    | '/_authenticated/settings'
    | '/clerk/(auth)'
    | '/clerk/_authenticated'
    | '/(auth)/forgot-password'
    | '/(auth)/otp'
    | '/(auth)/sign-in'
    | '/(auth)/sign-in-2'
    | '/(auth)/sign-up'
    | '/(errors)/401'
    | '/(errors)/403'
    | '/(errors)/404'
    | '/(errors)/500'
    | '/(errors)/503'
    | '/_authenticated/design-system'
    | '/_authenticated/'
    | '/_authenticated/bao-cao/doanh-thu'
    | '/_authenticated/bao-cao/kiem-soat'
    | '/_authenticated/devices/list'
    | '/_authenticated/devices/new'
    | '/_authenticated/devices/types'
    | '/_authenticated/employee/detail'
    | '/_authenticated/employee/list'
    | '/_authenticated/employee/role'
    | '/_authenticated/menu/item-class'
    | '/_authenticated/menu/quantity-day'
    | '/_authenticated/menu/schedule'
    | '/_authenticated/sale-channel/discount'
    | '/_authenticated/setting/area'
    | '/_authenticated/setting/store'
    | '/_authenticated/settings/account'
    | '/_authenticated/settings/appearance'
    | '/_authenticated/settings/display'
    | '/_authenticated/settings/notifications'
    | '/clerk/(auth)/sign-in'
    | '/clerk/(auth)/sign-up'
    | '/clerk/_authenticated/user-management'
    | '/_authenticated/apps/'
    | '/_authenticated/bao-cao/'
    | '/_authenticated/chats/'
    | '/_authenticated/help-center/'
    | '/_authenticated/sale/'
    | '/_authenticated/settings/'
    | '/_authenticated/tasks/'
    | '/_authenticated/users/'
    | '/_authenticated/bao-cao/ke-toan/bang-ke-hoa-don'
    | '/_authenticated/bao-cao/ke-toan/hoa-don'
    | '/_authenticated/employee/detail/$userId'
    | '/_authenticated/employee/role/detail'
    | '/_authenticated/menu/categories/categories-in-brand'
    | '/_authenticated/menu/category/detail'
    | '/_authenticated/menu/customization/new'
    | '/_authenticated/menu/item-class/detail'
    | '/_authenticated/menu/item-removed/item-removed-in-city'
    | '/_authenticated/menu/item-removed/item-removed-in-store'
    | '/_authenticated/sales-channel/channel/detail'
    | '/_authenticated/setting/area/detail'
    | '/_authenticated/bao-cao/ke-toan/'
    | '/_authenticated/employee/role/'
    | '/_authenticated/menu/item-class/'
    | '/_authenticated/sale/promotion/'
    | '/_authenticated/sales-channel/channel/'
    | '/_authenticated/setting/area/'
    | '/_authenticated/setting/payment-method/'
    | '/_authenticated/setting/source/'
    | '/_authenticated/menu/categories/categories-in-brand/detail'
    | '/_authenticated/menu/category/detail/$id'
    | '/_authenticated/menu/customization/customization-in-city/detail'
    | '/_authenticated/menu/customization/customization-in-store/create'
    | '/_authenticated/menu/customization/customization-in-store/detail'
    | '/_authenticated/menu/item-class/detail/$id'
    | '/_authenticated/sales-channel/channel/detail/$uuid'
    | '/_authenticated/setting/area/detail/$areaId'
    | '/_authenticated/setting/payment-method/detail/$paymentMethodId'
    | '/_authenticated/setting/source/detail/$sourceId'
    | '/_authenticated/menu/category/detail/'
    | '/_authenticated/menu/customization/customization-in-city/'
    | '/_authenticated/menu/customization/customization-in-store/'
    | '/_authenticated/menu/item-class/detail/'
    | '/_authenticated/menu/items/items-in-city/'
    | '/_authenticated/menu/items/items-in-store/'
    | '/_authenticated/setting/area/detail/'
    | '/_authenticated/setting/payment-method/detail/'
    | '/_authenticated/setting/source/detail/'
    | '/_authenticated/menu/categories/categories-in-brand/detail/$id'
    | '/_authenticated/menu/customization/customization-in-city/detail/$customizationId'
    | '/_authenticated/menu/customization/customization-in-store/detail/$customizationId'
    | '/_authenticated/menu/customization/customization-in-store/detail/'
  fileRoutesById: FileRoutesById
}

export interface RootRouteChildren {
  AuthenticatedRouteRoute: typeof AuthenticatedRouteRouteWithChildren
  ClerkRouteRoute: typeof ClerkRouteRouteWithChildren
  authForgotPasswordRoute: typeof authForgotPasswordRoute
  authOtpRoute: typeof authOtpRoute
  authSignInRoute: typeof authSignInRoute
  authSignIn2Route: typeof authSignIn2Route
  authSignUpRoute: typeof authSignUpRoute
  errors401Route: typeof errors401Route
  errors403Route: typeof errors403Route
  errors404Route: typeof errors404Route
  errors500Route: typeof errors500Route
  errors503Route: typeof errors503Route
}

const rootRouteChildren: RootRouteChildren = {
  AuthenticatedRouteRoute: AuthenticatedRouteRouteWithChildren,
  ClerkRouteRoute: ClerkRouteRouteWithChildren,
  authForgotPasswordRoute: authForgotPasswordRoute,
  authOtpRoute: authOtpRoute,
  authSignInRoute: authSignInRoute,
  authSignIn2Route: authSignIn2Route,
  authSignUpRoute: authSignUpRoute,
  errors401Route: errors401Route,
  errors403Route: errors403Route,
  errors404Route: errors404Route,
  errors500Route: errors500Route,
  errors503Route: errors503Route,
}

export const routeTree = rootRoute
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()

/* ROUTE_MANIFEST_START
{
  "routes": {
    "__root__": {
      "filePath": "__root.tsx",
      "children": [
        "/_authenticated",
        "/clerk",
        "/(auth)/forgot-password",
        "/(auth)/otp",
        "/(auth)/sign-in",
        "/(auth)/sign-in-2",
        "/(auth)/sign-up",
        "/(errors)/401",
        "/(errors)/403",
        "/(errors)/404",
        "/(errors)/500",
        "/(errors)/503"
      ]
    },
    "/_authenticated": {
      "filePath": "_authenticated/route.tsx",
      "children": [
        "/_authenticated/settings",
        "/_authenticated/design-system",
        "/_authenticated/",
        "/_authenticated/bao-cao/doanh-thu",
        "/_authenticated/bao-cao/kiem-soat",
        "/_authenticated/devices/list",
        "/_authenticated/devices/new",
        "/_authenticated/devices/types",
        "/_authenticated/employee/detail",
        "/_authenticated/employee/list",
        "/_authenticated/employee/role",
        "/_authenticated/menu/item-class",
        "/_authenticated/menu/quantity-day",
        "/_authenticated/menu/schedule",
        "/_authenticated/sale-channel/discount",
        "/_authenticated/setting/area",
        "/_authenticated/setting/store",
        "/_authenticated/apps/",
        "/_authenticated/bao-cao/",
        "/_authenticated/chats/",
        "/_authenticated/help-center/",
        "/_authenticated/sale/",
        "/_authenticated/tasks/",
        "/_authenticated/users/",
        "/_authenticated/bao-cao/ke-toan/bang-ke-hoa-don",
        "/_authenticated/bao-cao/ke-toan/hoa-don",
        "/_authenticated/menu/categories/categories-in-brand",
        "/_authenticated/menu/category/detail",
        "/_authenticated/menu/customization/new",
        "/_authenticated/menu/item-removed/item-removed-in-city",
        "/_authenticated/menu/item-removed/item-removed-in-store",
        "/_authenticated/sales-channel/channel/detail",
        "/_authenticated/bao-cao/ke-toan/",
        "/_authenticated/sale/promotion/",
        "/_authenticated/sales-channel/channel/",
        "/_authenticated/setting/payment-method/",
        "/_authenticated/setting/source/",
        "/_authenticated/menu/customization/customization-in-city/detail",
        "/_authenticated/menu/customization/customization-in-store/create",
        "/_authenticated/menu/customization/customization-in-store/detail",
        "/_authenticated/setting/payment-method/detail/$paymentMethodId",
        "/_authenticated/setting/source/detail/$sourceId",
        "/_authenticated/menu/customization/customization-in-city/",
        "/_authenticated/menu/customization/customization-in-store/",
        "/_authenticated/menu/items/items-in-city/",
        "/_authenticated/menu/items/items-in-store/",
        "/_authenticated/setting/payment-method/detail/",
        "/_authenticated/setting/source/detail/"
      ]
    },
    "/clerk": {
      "filePath": "clerk/route.tsx",
      "children": [
        "/clerk/(auth)",
        "/clerk/_authenticated"
      ]
    },
    "/_authenticated/settings": {
      "filePath": "_authenticated/settings/route.tsx",
      "parent": "/_authenticated",
      "children": [
        "/_authenticated/settings/account",
        "/_authenticated/settings/appearance",
        "/_authenticated/settings/display",
        "/_authenticated/settings/notifications",
        "/_authenticated/settings/"
      ]
    },
    "/clerk/(auth)": {
      "filePath": "clerk/(auth)/route.tsx",
      "parent": "/clerk",
      "children": [
        "/clerk/(auth)/sign-in",
        "/clerk/(auth)/sign-up"
      ]
    },
    "/clerk/_authenticated": {
      "filePath": "clerk/_authenticated/route.tsx",
      "parent": "/clerk",
      "children": [
        "/clerk/_authenticated/user-management"
      ]
    },
    "/(auth)/forgot-password": {
      "filePath": "(auth)/forgot-password.tsx"
    },
    "/(auth)/otp": {
      "filePath": "(auth)/otp.tsx"
    },
    "/(auth)/sign-in": {
      "filePath": "(auth)/sign-in.tsx"
    },
    "/(auth)/sign-in-2": {
      "filePath": "(auth)/sign-in-2.tsx"
    },
    "/(auth)/sign-up": {
      "filePath": "(auth)/sign-up.tsx"
    },
    "/(errors)/401": {
      "filePath": "(errors)/401.tsx"
    },
    "/(errors)/403": {
      "filePath": "(errors)/403.tsx"
    },
    "/(errors)/404": {
      "filePath": "(errors)/404.tsx"
    },
    "/(errors)/500": {
      "filePath": "(errors)/500.tsx"
    },
    "/(errors)/503": {
      "filePath": "(errors)/503.tsx"
    },
    "/_authenticated/design-system": {
      "filePath": "_authenticated/design-system.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/": {
      "filePath": "_authenticated/index.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/bao-cao/doanh-thu": {
      "filePath": "_authenticated/bao-cao/doanh-thu.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/bao-cao/kiem-soat": {
      "filePath": "_authenticated/bao-cao/kiem-soat.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/devices/list": {
      "filePath": "_authenticated/devices/list.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/devices/new": {
      "filePath": "_authenticated/devices/new.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/devices/types": {
      "filePath": "_authenticated/devices/types.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/employee/detail": {
      "filePath": "_authenticated/employee/detail.tsx",
      "parent": "/_authenticated",
      "children": [
        "/_authenticated/employee/detail/$userId"
      ]
    },
    "/_authenticated/employee/list": {
      "filePath": "_authenticated/employee/list.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/employee/role": {
      "filePath": "_authenticated/employee/role.tsx",
      "parent": "/_authenticated",
      "children": [
        "/_authenticated/employee/role/detail",
        "/_authenticated/employee/role/"
      ]
    },
    "/_authenticated/menu/item-class": {
      "filePath": "_authenticated/menu/item-class.tsx",
      "parent": "/_authenticated",
      "children": [
        "/_authenticated/menu/item-class/detail",
        "/_authenticated/menu/item-class/"
      ]
    },
    "/_authenticated/menu/quantity-day": {
      "filePath": "_authenticated/menu/quantity-day.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/menu/schedule": {
      "filePath": "_authenticated/menu/schedule.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/sale-channel/discount": {
      "filePath": "_authenticated/sale-channel/discount.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/setting/area": {
      "filePath": "_authenticated/setting/area.tsx",
      "parent": "/_authenticated",
      "children": [
        "/_authenticated/setting/area/detail",
        "/_authenticated/setting/area/"
      ]
    },
    "/_authenticated/setting/store": {
      "filePath": "_authenticated/setting/store.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/settings/account": {
      "filePath": "_authenticated/settings/account.tsx",
      "parent": "/_authenticated/settings"
    },
    "/_authenticated/settings/appearance": {
      "filePath": "_authenticated/settings/appearance.tsx",
      "parent": "/_authenticated/settings"
    },
    "/_authenticated/settings/display": {
      "filePath": "_authenticated/settings/display.tsx",
      "parent": "/_authenticated/settings"
    },
    "/_authenticated/settings/notifications": {
      "filePath": "_authenticated/settings/notifications.tsx",
      "parent": "/_authenticated/settings"
    },
    "/clerk/(auth)/sign-in": {
      "filePath": "clerk/(auth)/sign-in.tsx",
      "parent": "/clerk/(auth)"
    },
    "/clerk/(auth)/sign-up": {
      "filePath": "clerk/(auth)/sign-up.tsx",
      "parent": "/clerk/(auth)"
    },
    "/clerk/_authenticated/user-management": {
      "filePath": "clerk/_authenticated/user-management.tsx",
      "parent": "/clerk/_authenticated"
    },
    "/_authenticated/apps/": {
      "filePath": "_authenticated/apps/index.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/bao-cao/": {
      "filePath": "_authenticated/bao-cao/index.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/chats/": {
      "filePath": "_authenticated/chats/index.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/help-center/": {
      "filePath": "_authenticated/help-center/index.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/sale/": {
      "filePath": "_authenticated/sale/index.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/settings/": {
      "filePath": "_authenticated/settings/index.tsx",
      "parent": "/_authenticated/settings"
    },
    "/_authenticated/tasks/": {
      "filePath": "_authenticated/tasks/index.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/users/": {
      "filePath": "_authenticated/users/index.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/bao-cao/ke-toan/bang-ke-hoa-don": {
      "filePath": "_authenticated/bao-cao/ke-toan/bang-ke-hoa-don.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/bao-cao/ke-toan/hoa-don": {
      "filePath": "_authenticated/bao-cao/ke-toan/hoa-don.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/employee/detail/$userId": {
      "filePath": "_authenticated/employee/detail.$userId.tsx",
      "parent": "/_authenticated/employee/detail"
    },
    "/_authenticated/employee/role/detail": {
      "filePath": "_authenticated/employee/role/detail.tsx",
      "parent": "/_authenticated/employee/role"
    },
    "/_authenticated/menu/categories/categories-in-brand": {
      "filePath": "_authenticated/menu/categories/categories-in-brand.tsx",
      "parent": "/_authenticated",
      "children": [
        "/_authenticated/menu/categories/categories-in-brand/detail"
      ]
    },
    "/_authenticated/menu/category/detail": {
      "filePath": "_authenticated/menu/category/detail.tsx",
      "parent": "/_authenticated",
      "children": [
        "/_authenticated/menu/category/detail/$id",
        "/_authenticated/menu/category/detail/"
      ]
    },
    "/_authenticated/menu/customization/new": {
      "filePath": "_authenticated/menu/customization/new.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/menu/item-class/detail": {
      "filePath": "_authenticated/menu/item-class/detail.tsx",
      "parent": "/_authenticated/menu/item-class",
      "children": [
        "/_authenticated/menu/item-class/detail/$id",
        "/_authenticated/menu/item-class/detail/"
      ]
    },
    "/_authenticated/menu/item-removed/item-removed-in-city": {
      "filePath": "_authenticated/menu/item-removed/item-removed-in-city.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/menu/item-removed/item-removed-in-store": {
      "filePath": "_authenticated/menu/item-removed/item-removed-in-store.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/sales-channel/channel/detail": {
      "filePath": "_authenticated/sales-channel/channel/detail.tsx",
      "parent": "/_authenticated",
      "children": [
        "/_authenticated/sales-channel/channel/detail/$uuid"
      ]
    },
    "/_authenticated/setting/area/detail": {
      "filePath": "_authenticated/setting/area/detail.tsx",
      "parent": "/_authenticated/setting/area",
      "children": [
        "/_authenticated/setting/area/detail/$areaId",
        "/_authenticated/setting/area/detail/"
      ]
    },
    "/_authenticated/bao-cao/ke-toan/": {
      "filePath": "_authenticated/bao-cao/ke-toan/index.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/employee/role/": {
      "filePath": "_authenticated/employee/role/index.tsx",
      "parent": "/_authenticated/employee/role"
    },
    "/_authenticated/menu/item-class/": {
      "filePath": "_authenticated/menu/item-class/index.tsx",
      "parent": "/_authenticated/menu/item-class"
    },
    "/_authenticated/sale/promotion/": {
      "filePath": "_authenticated/sale/promotion/index.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/sales-channel/channel/": {
      "filePath": "_authenticated/sales-channel/channel/index.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/setting/area/": {
      "filePath": "_authenticated/setting/area/index.tsx",
      "parent": "/_authenticated/setting/area"
    },
    "/_authenticated/setting/payment-method/": {
      "filePath": "_authenticated/setting/payment-method/index.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/setting/source/": {
      "filePath": "_authenticated/setting/source/index.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/menu/categories/categories-in-brand/detail": {
      "filePath": "_authenticated/menu/categories/categories-in-brand/detail.tsx",
      "parent": "/_authenticated/menu/categories/categories-in-brand",
      "children": [
        "/_authenticated/menu/categories/categories-in-brand/detail/$id"
      ]
    },
    "/_authenticated/menu/category/detail/$id": {
      "filePath": "_authenticated/menu/category/detail/$id.tsx",
      "parent": "/_authenticated/menu/category/detail"
    },
    "/_authenticated/menu/customization/customization-in-city/detail": {
      "filePath": "_authenticated/menu/customization/customization-in-city/detail.tsx",
      "parent": "/_authenticated",
      "children": [
        "/_authenticated/menu/customization/customization-in-city/detail/$customizationId"
      ]
    },
    "/_authenticated/menu/customization/customization-in-store/create": {
      "filePath": "_authenticated/menu/customization/customization-in-store/create.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/menu/customization/customization-in-store/detail": {
      "filePath": "_authenticated/menu/customization/customization-in-store/detail.tsx",
      "parent": "/_authenticated",
      "children": [
        "/_authenticated/menu/customization/customization-in-store/detail/$customizationId",
        "/_authenticated/menu/customization/customization-in-store/detail/"
      ]
    },
    "/_authenticated/menu/item-class/detail/$id": {
      "filePath": "_authenticated/menu/item-class/detail/$id.tsx",
      "parent": "/_authenticated/menu/item-class/detail"
    },
    "/_authenticated/sales-channel/channel/detail/$uuid": {
      "filePath": "_authenticated/sales-channel/channel/detail/$uuid.tsx",
      "parent": "/_authenticated/sales-channel/channel/detail"
    },
    "/_authenticated/setting/area/detail/$areaId": {
      "filePath": "_authenticated/setting/area/detail/$areaId.tsx",
      "parent": "/_authenticated/setting/area/detail"
    },
    "/_authenticated/setting/payment-method/detail/$paymentMethodId": {
      "filePath": "_authenticated/setting/payment-method/detail/$paymentMethodId.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/setting/source/detail/$sourceId": {
      "filePath": "_authenticated/setting/source/detail/$sourceId.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/menu/category/detail/": {
      "filePath": "_authenticated/menu/category/detail/index.tsx",
      "parent": "/_authenticated/menu/category/detail"
    },
    "/_authenticated/menu/customization/customization-in-city/": {
      "filePath": "_authenticated/menu/customization/customization-in-city/index.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/menu/customization/customization-in-store/": {
      "filePath": "_authenticated/menu/customization/customization-in-store/index.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/menu/item-class/detail/": {
      "filePath": "_authenticated/menu/item-class/detail/index.tsx",
      "parent": "/_authenticated/menu/item-class/detail"
    },
    "/_authenticated/menu/items/items-in-city/": {
      "filePath": "_authenticated/menu/items/items-in-city/index.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/menu/items/items-in-store/": {
      "filePath": "_authenticated/menu/items/items-in-store/index.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/setting/area/detail/": {
      "filePath": "_authenticated/setting/area/detail/index.tsx",
      "parent": "/_authenticated/setting/area/detail"
    },
    "/_authenticated/setting/payment-method/detail/": {
      "filePath": "_authenticated/setting/payment-method/detail/index.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/setting/source/detail/": {
      "filePath": "_authenticated/setting/source/detail/index.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/menu/categories/categories-in-brand/detail/$id": {
      "filePath": "_authenticated/menu/categories/categories-in-brand/detail/$id.tsx",
      "parent": "/_authenticated/menu/categories/categories-in-brand/detail"
    },
    "/_authenticated/menu/customization/customization-in-city/detail/$customizationId": {
      "filePath": "_authenticated/menu/customization/customization-in-city/detail/$customizationId.tsx",
      "parent": "/_authenticated/menu/customization/customization-in-city/detail"
    },
    "/_authenticated/menu/customization/customization-in-store/detail/$customizationId": {
      "filePath": "_authenticated/menu/customization/customization-in-store/detail/$customizationId.tsx",
      "parent": "/_authenticated/menu/customization/customization-in-store/detail"
    },
    "/_authenticated/menu/customization/customization-in-store/detail/": {
      "filePath": "_authenticated/menu/customization/customization-in-store/detail/index.tsx",
      "parent": "/_authenticated/menu/customization/customization-in-store/detail"
    }
  }
}
ROUTE_MANIFEST_END */
