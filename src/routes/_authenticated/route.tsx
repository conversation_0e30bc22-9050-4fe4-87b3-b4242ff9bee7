import { createFileRoute } from '@tanstack/react-router'

import { AuthenticatedLayout } from '@/components/layout/authenticated-layout'

export const Route = createFileRoute('/_authenticated')({
  beforeLoad: () => {
    // Temporarily disabled for testing
    // const { user, jwtToken } = useAuthStore.getState().auth
    // Check if user is authenticated
    // if (!user || !jwtToken) {
    //   throw redirect({
    //     to: '/sign-in',
    //   })
    // }
  },
  component: AuthenticatedLayout
})
