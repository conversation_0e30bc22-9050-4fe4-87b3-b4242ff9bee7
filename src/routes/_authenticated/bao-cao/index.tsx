import { createFileRoute, redirect } from '@tanstack/react-router'
import { useAuthStore } from '@/stores/authStore'

export const Route = createFileRoute('/_authenticated/bao-cao/')({
  beforeLoad: () => {
    const { user, jwtToken } = useAuthStore.getState().auth
    
    // Check authentication before redirecting
    if (!user || !jwtToken) {
      throw redirect({
        to: '/sign-in',
      })
    }
    
    // Redirect to revenue reports if authenticated
    throw redirect({
      to: '/bao-cao/doanh-thu',
    })
  },
}) 