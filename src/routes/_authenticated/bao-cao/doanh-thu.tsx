import { createFileRoute, redirect } from '@tanstack/react-router'
import { useAuthStore } from '@/stores/authStore'
import Reports from '@/features/reports/revenue'

export const Route = createFileRoute('/_authenticated/bao-cao/doanh-thu')({
  beforeLoad: () => {
    const { user, jwtToken } = useAuthStore.getState().auth

    // Double-check authentication for sensitive reports
    if (!user || !jwtToken) {
      throw redirect({
        to: '/sign-in',
      })
    }

    // Optional: Add role-based access control here if needed
    // const { userRole } = useAuthStore.getState().auth
    // if (!userRole || !['admin', 'manager'].includes(userRole.role_name)) {
    //   throw redirect({ to: '/403' })
    // }
  },
  component: Reports,
})
