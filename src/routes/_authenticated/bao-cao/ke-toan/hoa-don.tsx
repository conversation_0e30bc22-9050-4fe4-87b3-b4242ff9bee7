import { createFileRoute, redirect } from '@tanstack/react-router'
import { useAuthStore } from '@/stores/authStore'
import Invoices from '@/features/reports/invoice/bao-cao-doi-soat-hoa-don-vat'

export const Route = createFileRoute('/_authenticated/bao-cao/ke-toan/hoa-don')(
  {
    beforeLoad: () => {
      const { user, jwtToken } = useAuthStore.getState().auth

      // Double-check authentication for sensitive reports
      if (!user || !jwtToken) {
        throw redirect({
          to: '/sign-in',
        })
      }

      // Optional: Add role-based access control here if needed
      // const { userRole } = useAuthStore.getState().auth
      // if (!userRole || !['admin', 'manager'].includes(userRole.role_name)) {
      //   throw redirect({ to: '/403' })
      // }
    },
    component: Invoices,
  }
)
