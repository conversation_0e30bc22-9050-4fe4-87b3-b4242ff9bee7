import { createFileRoute } from '@tanstack/react-router'

import { CreateAreaForm } from '@/features/areas'

interface AreaDetailSearch {
  store_uid: string
}

export const Route = createFileRoute('/_authenticated/setting/area/detail/$areaId')({
  component: AreaDetailPage,
  validateSearch: (search: Record<string, unknown>): AreaDetailSearch => ({
    store_uid: search.store_uid as string
  })
})

function AreaDetailPage() {
  const { areaId } = Route.useParams()
  const { store_uid } = Route.useSearch()

  console.log('🔍 AreaDetailPage rendered')
  console.log('📍 Route params:', { areaId })
  console.log('🔍 Route search:', { store_uid })
  console.log('📊 Full URL:', window.location.href)

  return <CreateAreaForm areaId={areaId} storeUid={store_uid} />
}
