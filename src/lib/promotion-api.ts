import { apiClient } from './api'

// Individual promotion detail within a promotion group
export interface PromotionDetail {
  promotion_uid: string
  active: number
  created_at: number
  store_uid: string
  store_name: string
}

// API response promotion structure (actual structure from your data)
export interface ApiPromotion {
  promotion_id: string
  promotion_name: string
  partner_auto_gen: number
  array_agg: number[]
  ids_same_promotion: string[]
  promotions: PromotionDetail[]
}

// API response structure
export interface PromotionsApiResponse {
  data: ApiPromotion[]
  track_id?: string
  pagination?: {
    current_page: number
    total_pages: number
    total_items: number
    per_page: number
  }
}

// Parameters for promotions API
export interface PromotionsApiParams {
  company_uid: string
  brand_uid: string
  page?: number
  aggregate?: boolean
  store_uid?: string
  search?: string
  status?: string
  type?: string
}

// Internal promotion structure (simplified to match actual API data)
export interface Promotion {
  id: string
  name: string
  partnerAutoGen: boolean
  createdAtTimestamps: number[]
  relatedPromotionIds: string[]
  stores: PromotionStore[]
  totalStores: number
  isActive: boolean
}

// Internal store structure for promotions
export interface PromotionStore {
  promotionUid: string
  storeUid: string
  storeName: string
  isActive: boolean
  createdAt: Date
}

/**
 * Fetch promotions from POS API
 */
export const fetchPromotions = async (
  params: PromotionsApiParams
): Promise<PromotionsApiResponse> => {
  const queryParams = new URLSearchParams({
    company_uid: params.company_uid,
    brand_uid: params.brand_uid,
    page: (params.page || 1).toString(),
  })

  if (params.aggregate !== undefined) {
    queryParams.append('aggregate', params.aggregate.toString())
  }

  if (params.store_uid) {
    queryParams.append('store_uid', params.store_uid)
  }

  if (params.search) {
    queryParams.append('search', params.search)
  }

  if (params.status) {
    queryParams.append('status', params.status)
  }

  if (params.type) {
    queryParams.append('type', params.type)
  }

  const response = await apiClient.get<ApiPromotion[]>(
    `/mdata/v1/promotions?${queryParams.toString()}`
  )

  return {
    data: response.data,
    track_id: 'generated-track-id',
  }
}

/**
 * Convert API promotion to our internal Promotion format
 */
export const convertApiPromotionToPromotion = (
  apiPromotion: ApiPromotion
): Promotion => {
  const stores: PromotionStore[] = apiPromotion.promotions.map((promo) => ({
    promotionUid: promo.promotion_uid,
    storeUid: promo.store_uid,
    storeName: promo.store_name,
    isActive: promo.active === 1,
    createdAt: new Date(promo.created_at * 1000), // Convert Unix timestamp to Date
  }))

  return {
    id: apiPromotion.promotion_id,
    name: apiPromotion.promotion_name,
    partnerAutoGen: apiPromotion.partner_auto_gen === 1,
    createdAtTimestamps: apiPromotion.array_agg,
    relatedPromotionIds: apiPromotion.ids_same_promotion,
    stores,
    totalStores: stores.length,
    isActive: stores.some((store) => store.isActive), // Active if any store is active
  }
}

/**
 * Get the most recent creation timestamp from a promotion
 */
export const getPromotionLatestTimestamp = (
  promotion: ApiPromotion
): number => {
  return Math.max(...promotion.array_agg)
}

/**
 * Get all active stores for a promotion
 */
export const getActiveStores = (promotion: ApiPromotion): PromotionDetail[] => {
  return promotion.promotions.filter((promo) => promo.active === 1)
}

/**
 * Check if promotion has any active stores
 */
export const isPromotionActive = (promotion: ApiPromotion): boolean => {
  return promotion.promotions.some((promo) => promo.active === 1)
}

/**
 * Get promotion by ID
 */
export const fetchPromotionById = async (
  id: string,
  companyUid: string,
  brandUid: string
): Promise<ApiPromotion> => {
  const queryParams = new URLSearchParams({
    company_uid: companyUid,
    brand_uid: brandUid,
    id,
  })

  const response = await apiClient.get<{
    data: ApiPromotion
    track_id: string
  }>(`/mdata/v1/promotions/id?${queryParams.toString()}`)

  return response.data.data
}

// Create promotion request payload
export interface CreatePromotionRequest {
  promotion_name: string
  company_uid: string
  brand_uid: string
  promotion_id: string
  list_store_uid: string[]
}

// Create promotion response
export interface CreatePromotionResponse {
  data?: unknown
  message?: string
  track_id?: string
}

// Update promotion request payload
export interface UpdatePromotionRequest {
  brand_uid: string
  company_uid: string
  sort: number
  is_fabi: number
  partner_auto_gen: number
  active: number
  extra_data: Record<string, unknown>
  source_uid: string
  promotion_id: string
  promotion_name: string
  description: string | null
  deleted: boolean
  created_by: string
  updated_by: string
  deleted_by: string | null
  created_at: number
  updated_at: number
  deleted_at: number | null
  promotions: Array<{
    id: string
    company_uid: string
    brand_uid: string
    sort: number
    is_fabi: number
    partner_auto_gen: number
    active: number
    extra_data: Record<string, unknown>
    source_uid: string
    promotion_id: string
    promotion_name: string
    description: string | null
    store_uid: string
  }>
  promotion_uids_same_promotion_id: string
  stores: string[]
  list_store_uid: string[]
  list_promotion_uid: string[]
}

// Update promotion response
export interface UpdatePromotionResponse {
  data?: unknown
  message?: string
  track_id?: string
}

/**
 * Create a new promotion
 */
export const createPromotion = async (
  promotionData: CreatePromotionRequest
): Promise<CreatePromotionResponse> => {
  const response = await apiClient.post<CreatePromotionResponse>(
    `/mdata/v1/promotion`,
    promotionData
  )

  return response.data
}

/**
 * Update an existing promotion
 */
export const updatePromotion = async (
  promotionData: UpdatePromotionRequest
): Promise<UpdatePromotionResponse> => {
  const response = await apiClient.put<UpdatePromotionResponse>(
    `/mdata/v1/promotion`,
    promotionData
  )

  return response.data
}

/**
 * Delete a promotion using list_promotion_uid parameter
 */
export const deletePromotion = async (
  promotionUid: string,
  companyUid: string,
  brandUid: string
): Promise<void> => {
  const queryParams = new URLSearchParams({
    company_uid: companyUid,
    brand_uid: brandUid,
    list_promotion_uid: promotionUid,
  })

  await apiClient.delete(`/mdata/v1/promotion?${queryParams.toString()}`)
}
