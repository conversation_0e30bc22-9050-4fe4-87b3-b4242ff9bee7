import { api } from './api'

// Types for Item Master Data API
export interface ItemExtraData {
  is_buffet_item: number
  up_size_buffet: []
  is_item_service: number
  is_virtual_item: number
  price_by_source: []
  enable_edit_price: number
  exclude_items_buffet: []
  no_update_quantity_toping: number
}

export interface ItemCity {
  id: string
  city_id: string
  fb_city_id: string
  city_name: string
  image_path: string | null
  description: string
  active: number
  extra_data: unknown
  revision: number
  sort: number
  created_by: string | null
  updated_by: string | null
  deleted_by: string | null
  created_at: number
  updated_at: number
  deleted_at: number | null
  items_cities: {
    item_uid: string
    city_uid: string
  }
}

export interface Item {
  id: string
  item_id: string
  item_name: string
  description: string
  ots_price: number
  ots_tax: number
  ta_price: number
  ta_tax: number
  time_sale_hour_day: number
  time_sale_date_week: number
  allow_take_away: number
  is_eat_with: number
  image_path: string
  image_path_thumb: string
  item_color: string
  list_order: number
  is_service: number
  is_material: number
  active: number
  user_id: string
  is_foreign: number
  quantity_default: number
  price_change: number
  currency_type_id: string
  point: number
  is_gift: number
  is_fc: number
  show_on_web: number
  show_price_on_web: number
  cost_price: number
  is_print_label: number
  quantity_limit: number
  is_kit: number
  time_cooking: number
  item_id_barcode: string
  process_index: number
  is_allow_discount: number
  quantity_per_day: number
  item_id_eat_with: string
  is_parent: number
  is_sub: number
  item_id_mapping: string
  effective_date: number
  expire_date: number
  sort: number
  sort_online: number
  extra_data: ItemExtraData
  revision: number
  unit_uid: string
  unit_secondary_uid: string | null
  item_type_uid: string
  item_class_uid: string
  source_uid: string | null
  brand_uid: string
  city_uid: string
  company_uid: string
  customization_uid: string | null
  is_fabi: number
  deleted: boolean
  created_by: string
  updated_by: string
  deleted_by: string | null
  created_at: number
  updated_at: number
  deleted_at: number | null
  cities: ItemCity[]
}

export interface ItemsApiResponse {
  data: Item[]
  total_item: number
  track_id: string
}

export interface GetItemsParams {
  company_uid: string
  brand_uid: string
  list_store_uid?: string
  city_uid?: string
  list_city_uid?: string
  skip_limit?: boolean
  page?: number
  limit?: number
  search?: string
  active?: number
}

export interface CreateItemRequest {
  unit_uid: string
  ta_price: number
  ots_tax: number
  ta_tax: number
  extra_data: {
    price_by_source: unknown[]
    is_virtual_item: number
    is_item_service: number
    no_update_quantity_toping: number
    enable_edit_price: number
    is_buffet_item: number
    exclude_items_buffet: unknown[]
    up_size_buffet: unknown[]
  }
  item_type_uid: string
  item_name: string
  city_uid: string
  company_uid: string
  brand_uid: string
  item_id: string
  ots_price: number
  sort: number
}

// Cache for items requests
const itemsCache = new Map<string, { data: ItemsApiResponse; timestamp: number }>()
const pendingRequests = new Map<string, Promise<ItemsApiResponse>>()
const CACHE_DURATION = 10 * 60 * 1000 // 10 minutes

// Item Master Data API Service
export const itemApi = {
  /**
   * Get items master data with request deduplication and caching
   */
  getItems: async (params: GetItemsParams): Promise<ItemsApiResponse> => {
    const requestKey = `${params.company_uid}-${params.brand_uid}-${params.list_store_uid || 'all'}-${params.city_uid || 'all'}-${params.list_city_uid || 'all'}-${params.skip_limit || false}-${params.page || 1}-${params.limit || 50}-${params.search || ''}-${params.active ?? 1}`

    // Check cache first
    const cached = itemsCache.get(requestKey)
    if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
      return cached.data
    }

    // Check if there's already a pending request
    const existingRequest = pendingRequests.get(requestKey)
    if (existingRequest) {
      return existingRequest
    }

    const requestPromise = (async () => {
      try {
        const queryParams = new URLSearchParams({
          company_uid: params.company_uid,
          brand_uid: params.brand_uid
        })

        if (params.list_store_uid) {
          queryParams.set('list_store_uid', params.list_store_uid)
        }

        if (params.city_uid) {
          queryParams.set('city_uid', params.city_uid)
        }

        if (params.list_city_uid) {
          queryParams.set('list_city_uid', params.list_city_uid)
        }

        if (params.skip_limit !== undefined) {
          queryParams.set('skip_limit', params.skip_limit.toString())
        }

        if (params.page !== undefined) {
          queryParams.set('page', params.page.toString())
        }

        if (params.limit !== undefined) {
          queryParams.set('limit', params.limit.toString())
        }

        if (params.search) {
          queryParams.set('search', params.search)
        }

        if (params.active !== undefined) {
          queryParams.set('active', params.active.toString())
        }

        const response = await api.get(`/mdata/v1/items?${queryParams.toString()}`, {
          headers: {
            Accept: 'application/json, text/plain, */*',
            'accept-language': 'vi',
            fabi_type: 'pos-cms',
            'x-client-timezone': '25200000' // GMT+7 timezone offset in milliseconds
          },
          timeout: 30000 // 30 seconds timeout
        })

        // Validate response structure
        if (!response.data || typeof response.data !== 'object') {
          throw new Error('Invalid response format from items API')
        }

        const result = response.data as ItemsApiResponse

        // Cache the result
        itemsCache.set(requestKey, {
          data: result,
          timestamp: Date.now()
        })

        return result
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
      } catch (error: any) {
        if (error.code === 'ECONNABORTED' || error.message?.includes('timeout')) {
          throw new Error(
            'Request timeout - server is taking too long to respond. Please try again.'
          )
        }

        if (error.response?.status === 504) {
          throw new Error('Gateway timeout (504) - server is overloaded. Please try again later.')
        }

        if (error.response?.status === 503) {
          throw new Error(
            'Service unavailable (503) - server is temporarily down. Please try again later.'
          )
        }

        if (error.response?.status >= 500) {
          throw new Error(`Server error (${error.response.status}) - please try again later.`)
        }

        if (error.response?.status === 429) {
          throw new Error('Too many requests - please wait a moment before trying again.')
        }

        if (error.response?.status === 401) {
          throw new Error('Unauthorized - please check your authentication.')
        }

        if (error.response?.status === 403) {
          throw new Error('Forbidden - you do not have permission to access this resource.')
        }

        throw error
      } finally {
        pendingRequests.delete(requestKey)
      }
    })()

    pendingRequests.set(requestKey, requestPromise)

    return requestPromise
  },

  /**
   * Format currency for display
   */
  formatCurrency: (amount: number): string => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount)
  },

  /**
   * Create a new item
   */
  createItem: async (data: CreateItemRequest): Promise<unknown> => {
    try {
      const response = await api.post('/mdata/v1/item', data, {
        headers: {
          'Content-Type': 'application/json',
          'accept-language': 'vi',
          fabi_type: 'pos-cms',
          'x-client-timezone': '25200000'
        },
        timeout: 30000
      })

      // Clear cache after successful creation
      itemsCache.clear()

      return response.data.data || response.data
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
    } catch (error: any) {
      if (error.response?.status === 400) {
        throw new Error(error.response.data?.message || 'Invalid data provided.')
      }
      throw error
    }
  },

  /**
   * Clear cache (useful for testing or manual refresh)
   */
  clearCache: (): void => {
    itemsCache.clear()
    pendingRequests.clear()
  }
}

export default itemApi
