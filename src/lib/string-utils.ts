/**
 * Generate a random source ID with format SOURCE-XXXX
 * @returns A string in format SOURCE-XXXX where X is a random alphanumeric character
 */
export const generateSourceId = (): string => {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'
  let result = ''
  for (let i = 0; i < 4; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return `SOURCE-${result}`
}