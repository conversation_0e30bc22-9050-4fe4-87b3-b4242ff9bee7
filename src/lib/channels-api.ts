import type { Channel, ChannelApiData } from '@/types/channels'
import { convertApiChannelToChannel } from '@/types/channels'

import { apiClient } from './api'

// API response interface
interface ChannelsApiResponse {
  data: ChannelApiData[]
  track_id?: string
}

// Parameters for fetching channels
export interface GetChannelsParams {
  searchTerm?: string
  page?: number
  storeId?: string
}

/**
 * Fetch channels from the API
 */
export const getChannels = async (params: GetChannelsParams = {}): Promise<Channel[]> => {
  const posUserData = localStorage.getItem('pos_user_data')
  const posBrandData = localStorage.getItem('pos_brands_data')
  let companyUid = ''
  let brandUid = ''

  if (posUserData) {
    try {
      const userData = JSON.parse(posUserData)
      companyUid = userData.company_uid || ''
    } catch {
      // Error parsing user data
    }
  }

  if (posBrandData) {
    try {
      const brandData = JSON.parse(posBrandData)
      // pos_brands_data is an array, get the first brand
      if (Array.isArray(brandData) && brandData.length > 0) {
        brandUid = brandData[0].id || ''
      }
    } catch {
      // Error parsing brand data
    }
  }

  if (!companyUid || !brandUid) {
    throw new Error('Company or brand UID not found in localStorage')
  }

  // Build query parameters
  const queryParams = new URLSearchParams({
    company_uid: companyUid,
    brand_uid: brandUid,
    page: (params.page || 1).toString()
  })

  // Add search term if provided
  if (params.searchTerm) {
    queryParams.append('search', params.searchTerm)
  }

  // Add store ID if provided
  if (params.storeId) {
    queryParams.append('store_uid', params.storeId)
  }

  const response = await apiClient.get<ChannelsApiResponse>(
    `/mdata/v1/channels?${queryParams.toString()}`
  )

  if (response.data?.data) {
    // Convert API data to Channel objects
    return response.data.data.map((apiChannel: ChannelApiData) => {
      return convertApiChannelToChannel(apiChannel)
    })
  }

  return []
}

/**
 * Get a single channel by ID
 */
export const getChannelById = async (channelId: string): Promise<Channel> => {
  // Get auth data from localStorage
  let authData = localStorage.getItem('auth-storage')

  if (!authData) {
    // Try alternative keys
    authData =
      localStorage.getItem('auth') ||
      localStorage.getItem('authData') ||
      localStorage.getItem('user')

    if (!authData) {
      throw new Error('Authentication data not found')
    }
  }

  const { state } = JSON.parse(authData)
  const companyUid = state?.auth?.company?.id
  const brandUid = state?.auth?.brands?.[0]?.id

  if (!companyUid || !brandUid) {
    throw new Error('Company or brand UID not found in localStorage')
  }

  // Build query parameters
  const queryParams = new URLSearchParams({
    company_uid: companyUid,
    brand_uid: brandUid,
    id: channelId
  })

  const apiUrl = `/mdata/v1/channel?${queryParams.toString()}`
  const response = await apiClient.get<{ data: ChannelApiData; track_id?: string }>(apiUrl)

  if (response.data?.data) {
    // Convert API data to Channel object
    return convertApiChannelToChannel(response.data.data)
  }

  throw new Error('Channel not found')
}

/**
 * Create a new channel - Updated to match API format from curl
 */
export interface CreateChannelPayload {
  source_id: string
  source_name: string
  description: string | null
  active: number
  sort: number
  is_fabi: number
  source_type: string[]
  company_uid: string
  brand_uid: string
  store_uid: string
  partner_config: number
  extra_data: {
    require_tran_no: number
    commission: number
    deduct_tax_rate: number
    payment_method_id: string
    payment_type: string
    marketing_partner_cost_type: string
    marketing_partner_cost: number
    voucher_run_partner: string
    not_show_partner_bill: number
    use_order_online: number
    exclude_ship: number
    marketing_partner_cost_from_date: number
    marketing_partner_cost_to_date: number
    marketing_partner_cost_date_week: number
    marketing_partner_cost_hour_day: number
    payment_method_name?: string
  }
}

export const createChannel = async (
  payload: CreateChannelPayload | CreateChannelPayload[]
): Promise<Channel[]> => {
  try {
    // Check if payload is a single object with 'id' field (edit mode)
    const isEditMode = !Array.isArray(payload) && 'id' in payload

    let finalPayload: any
    let apiUrl: string

    if (isEditMode) {
      // For edit mode, send single object to /channel endpoint
      finalPayload = payload
      apiUrl = '/mdata/v1/channel'
      console.log('Updating channel with payload:', finalPayload)
    } else {
      // For create mode, ensure payload is always an array to /channels endpoint
      finalPayload = Array.isArray(payload) ? payload : [payload]
      apiUrl = '/mdata/v1/channels'
      console.log('Creating channels with payload:', finalPayload)
    }

    const response = await apiClient.post(apiUrl, finalPayload)

    console.log('API Response status:', response.status)
    console.log('API Response data:', response.data)

    // If we get here, the request was successful (axios only resolves for 2xx status codes)
    const successMessage = isEditMode
      ? 'Channel updated successfully!'
      : 'Channel created successfully!'
    console.log(successMessage)
    return []
  } catch (error: any) {
    console.error('API Error details:', {
      message: error.message,
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data,
      config: error.config
    })

    // Re-throw the error with more details
    throw new Error(`Failed to create channel: ${error.message}`)
  }
}

/**
 * Update a channel
 */
export const updateChannel = async (channel: Channel): Promise<Channel> => {
  // Prepare the payload with all required fields
  const payload = {
    id: channel.id,
    created_at: channel.created_at,
    created_by: channel.created_by,
    updated_at: channel.updated_at,
    updated_by: channel.updated_by,
    deleted: channel.deleted,
    deleted_at: channel.deleted_at,
    deleted_by: channel.deleted_by,
    source_id: channel.source_id,
    source_name: channel.source_name,
    source_type: channel.source_type,
    description: channel.description,
    extra_data: channel.extra_data,
    is_fb: channel.is_fb,
    active: channel.active,
    revision: channel.revision,
    brand_uid: channel.brand_uid,
    company_uid: channel.company_uid,
    sort: channel.sort,
    is_fabi: channel.is_fabi,
    store_uid: channel.store_uid,
    partner_config: channel.partner_config,
    stores: channel.stores
  }

  const response = await apiClient.put<ChannelApiData>(`/mdata/v1/channel`, payload)

  if (response.data) {
    return convertApiChannelToChannel(response.data)
  }

  throw new Error('Failed to update channel')
}

/**
 * Delete a channel
 */
export const deleteChannel = async (channelId: string): Promise<void> => {
  const posUserData = localStorage.getItem('pos_user_data')
  const posBrandData = localStorage.getItem('pos_brands_data')
  let companyUid = ''
  let brandUid = ''

  if (posUserData) {
    try {
      const userData = JSON.parse(posUserData)
      companyUid = userData.company_uid || ''
    } catch {
      // Error parsing user data
    }
  }

  if (posBrandData) {
    try {
      const brandData = JSON.parse(posBrandData)
      // pos_brands_data is an array, get the first brand
      if (Array.isArray(brandData) && brandData.length > 0) {
        brandUid = brandData[0].id || ''
      }
    } catch {
      // Error parsing brand data
    }
  }

  if (!companyUid || !brandUid) {
    throw new Error('Company or brand UID not found in localStorage')
  }

  // Build query parameters
  const queryParams = new URLSearchParams({
    company_uid: companyUid,
    brand_uid: brandUid,
    id: channelId
  })

  await apiClient.delete(`/mdata/v1/channel?${queryParams.toString()}`)
}

/**
 * Copy channels to target stores
 */
export interface CopyChannelPayload {
  source_id: string
  source_name: string
  source_type: string[]
  description: string | null
  extra_data: {
    commission: number
    exclude_ship: number
    payment_type: string
    deduct_tax_rate: number
    require_tran_no: number
    use_order_online: number
    payment_method_id: string
    payment_method_name: string
    voucher_run_partner: string | null
    not_show_partner_bill: number
    marketing_partner_cost: number
    marketing_partner_cost_type: string
    marketing_partner_cost_to_date: number
    marketing_partner_cost_hour_day: number
    marketing_partner_cost_date_week: number
    marketing_partner_cost_from_date: number
  }
  active: number
  sort: number
  is_fabi: number
  company_uid: string
  brand_uid: string
  store_uid: string
}

export interface CopyChannelsParams {
  channels: Channel[]
  targetStoreIds: string[]
}

export const copyChannels = async (
  params: CopyChannelsParams,
  companyUid: string,
  brandUid: string
): Promise<void> => {
  const { channels, targetStoreIds } = params

  // Create copy requests for each target store
  const copyPromises = targetStoreIds.map(async storeUid => {
    // Create payload array for this store
    const payloads: CopyChannelPayload[] = channels.map(channel => ({
      source_id: channel.source_id,
      source_name: channel.source_name,
      source_type: channel.source_type,
      description: channel.description,
      extra_data: channel.extra_data,
      active: channel.active,
      sort: channel.sort,
      is_fabi: channel.is_fabi,
      company_uid: companyUid,
      brand_uid: brandUid,
      store_uid: storeUid
    }))

    // Send POST request for this store
    return apiClient.post('/mdata/v1/channels', payloads)
  })

  // Execute all copy requests in parallel
  await Promise.all(copyPromises)
}

// Export all API functions
export const channelsApi = {
  getChannels,
  getChannelById,
  createChannel,
  updateChannel,
  deleteChannel,
  copyChannels
}
