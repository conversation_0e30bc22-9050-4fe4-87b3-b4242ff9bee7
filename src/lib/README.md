# API Library

This directory contains the shared API configuration and authentication services for the POS application.

## Overview

The API library provides a centralized way to handle HTTP requests to the POS API with automatic authentication, error handling, and consistent response formatting.

## Key Components

### 1. Shared Axios Instance (`api.ts`)

- **Base URL**: `https://posapi.ipos.vn/api`
- **Timeout**: 30 seconds
- **Headers**: Pre-configured with required headers for the POS API
- **Interceptors**: 
  - Request interceptor adds authentication token automatically
  - Response interceptor handles 401 errors globally

### 2. Authentication API (`auth-api.ts`)

Provides authentication-related API endpoints:

- `login(credentials)` - User login
- `getProfile()` - Get current user profile
- `logout()` - User logout
- `refreshToken()` - Refresh access token
- `verifyToken()` - Verify token validity

### 3. Authentication Hooks (`../hooks/use-auth.ts`)

React Query hooks for authentication:

- `useLogin()` - Login mutation with automatic token storage
- `useLogout()` - Logout mutation with state cleanup
- `useProfile()` - Get user profile query
- `useVerifyToken()` - Token verification query
- `useRefreshToken()` - Token refresh mutation

## Usage Examples

### Basic API Call

```typescript
import { api } from '@/lib/api'

// GET request
const response = await api.get('/some-endpoint')

// POST request
const response = await api.post('/some-endpoint', { data: 'value' })
```

### Authentication & Data Access

```typescript
import { useLogin, useLogout, useCurrentUser, usePermissions } from '@/hooks/use-auth'
import { usePosData } from '@/hooks/use-pos-data'

function LoginComponent() {
  const loginMutation = useLogin()

  const handleLogin = () => {
    loginMutation.mutate({
      email: '<EMAIL>',
      password: 'password'
    })
  }

  return (
    <button
      onClick={handleLogin}
      disabled={loginMutation.isPending}
    >
      {loginMutation.isPending ? 'Signing in...' : 'Login'}
    </button>
  )
}

function UserProfile() {
  const { user, company, stores, isAuthenticated } = useCurrentUser()
  const permissions = usePermissions()

  if (!isAuthenticated) {
    return <div>Please log in</div>
  }

  return (
    <div>
      <h1>Welcome, {user?.full_name}!</h1>
      <p>Company: {company?.company_name}</p>
      <p>Stores: {stores.length}</p>
      {permissions.isOwner() && <p>You are an owner</p>}
      {permissions.canAccessPOS() && <button>Access POS</button>}
    </div>
  )
}

function BusinessDashboard() {
  const {
    activeBrands,
    activeStores,
    getBrandById,
    getStoresByBrand,
    getPrimaryBrand,
    getStats
  } = usePosData()

  const stats = getStats()
  const primaryBrand = getPrimaryBrand()

  return (
    <div>
      <h2>Business Overview</h2>
      <p>Active Brands: {stats.activeBrands}</p>
      <p>Active Stores: {stats.activeStores}</p>
      <p>Primary Brand: {primaryBrand?.brand_name}</p>

      {/* Get stores for a specific brand */}
      {activeBrands.map(brand => (
        <div key={brand.id}>
          <h3>{brand.brand_name}</h3>
          <p>Stores: {getStoresByBrand(brand.id).length}</p>
        </div>
      ))}
    </div>
  )
}
```

## Configuration

The API client is pre-configured with the following headers:

- `Content-Type`: `application/json;charset=UTF-8`
- `Accept`: `application/json, text/plain, */*`
- `Origin`: `https://fabi.ipos.vn`
- `Referer`: `https://fabi.ipos.vn/`
- `accept-language`: `vi`
- `access_token`: `5c885b2ef8c34fb7b1d1fad11eef7bec` (static token from your curl request)
- `fabi_type`: `pos-cms`
- `x-client-timezone`: `25200000`

### Access Token Configuration

The API requires a static access token that is included in all requests. This token can be configured in two ways:

1. **Environment Variable** (Recommended for production):
   ```bash
   VITE_POS_ACCESS_TOKEN=5c885b2ef8c34fb7b1d1fad11eef7bec
   ```

2. **Hardcoded** (Current setup):
   The token is hardcoded in the API configuration for immediate use.

The system will use the environment variable if available, otherwise it falls back to the hardcoded token.

## Error Handling

- **401 Unauthorized**: Automatically clears auth state and redirects to login
- **500 Internal Server Error**: Handled by the global error handler in main.tsx
- **Network Errors**: Handled by React Query with automatic retries

## Integration with Auth Store

The API client integrates seamlessly with the Zustand auth store:

- Automatically reads tokens from the store for requests
- Clears auth state on 401 errors
- Updates auth state on successful login/refresh
- Stores complete login response in localStorage for persistence

### Data Storage

The system uses multiple storage mechanisms:

1. **Static Access Token**: Required for all API requests (`access_token` header)
2. **JWT Token**: Returned from login, stored in localStorage (`pos_jwt_token`)
3. **User Data**: Complete user profile stored in localStorage (`pos_user_data`)
4. **Session Data**: User role, company, brands, stores stored in memory

### Login Response Handling

The login API returns comprehensive data that is automatically stored:

```typescript
{
  user: { id, email, full_name, phone, role_uid, company_uid, ... },
  user_role: { role_id, role_name, description, allow_access, ... },
  company: { company_name, company_id, active, ... },
  brands: [{ brand_name, brand_id, currency, active, ... }],
  stores: [{ store_name, store_id, address, phone, active, ... }],
  token: "JWT_TOKEN_HERE",
  // ... other data
}
```

## Mermaid Diagrams

### Authentication Flow

```mermaid
sequenceDiagram
    participant User
    participant LoginForm
    participant useLogin
    participant authApi
    participant AuthStore
    participant Router

    User->>LoginForm: Enter credentials
    LoginForm->>useLogin: mutate(credentials)
    useLogin->>authApi: login(credentials)
    authApi->>API: POST /accounts/v1/user/login
    API-->>authApi: LoginResponse
    authApi-->>useLogin: LoginResponse
    useLogin->>AuthStore: setAccessToken()
    useLogin->>AuthStore: setUser()
    useLogin->>Router: navigate to dashboard
```

### API Request Flow

```mermaid
flowchart TD
    A[API Request] --> B[Request Interceptor]
    B --> C{Token Available?}
    C -->|Yes| D[Add access_token header]
    C -->|No| E[Continue without token]
    D --> F[Send Request]
    E --> F
    F --> G[Response Interceptor]
    G --> H{Status Code?}
    H -->|401| I[Clear Auth State]
    H -->|Other| J[Return Response]
    I --> K[Redirect to Login]
    J --> L[Handle in Component]
```

## Available Hooks

### Authentication Hooks (`@/hooks/use-auth`)
- `useLogin()` - Login mutation with automatic data storage
- `useLogout()` - Logout mutation with cleanup
- `useCurrentUser()` - Get current user and all related data
- `usePermissions()` - Check user permissions and roles
- `useProfile()` - Get user profile (API call)
- `useVerifyToken()` - Verify JWT token validity
- `useRefreshToken()` - Refresh JWT token

### Data Access Hooks (`@/hooks/use-pos-data`)
- `usePosData()` - Comprehensive data access with utility functions
- `useLocalStorageData()` - Direct localStorage access for debugging

### Utility Functions (from `usePosData()`)
- `getBrandById(id)` - Get brand by ID
- `getStoreById(id)` - Get store by ID
- `getCityById(id)` - Get city by ID
- `getStoresByBrand(brandId)` - Get all stores for a brand
- `getStoresByCity(cityId)` - Get all stores in a city
- `getActiveStoresByBrand(brandId)` - Get active stores for a brand
- `getPrimaryBrand()` - Get user's primary brand
- `getPrimaryStore()` - Get user's primary store
- `getStats()` - Get summary statistics
- `exportData()` - Export all data for backup/debugging

## Components

### Debug Components
- `UserInfoDebug` - Comprehensive debug view of all user data
- `PosDataExample` - Example component showing data usage patterns

## LocalStorage Keys

The system uses these localStorage keys:
- `pos_jwt_token` - JWT token from login response
- `pos_user_data` - User profile data (JSON)

## Quick Start

1. **Login**: Use the pre-filled login form or call `useLogin()` hook
2. **Access Data**: Use `usePosData()` for comprehensive data access
3. **Check Permissions**: Use `usePermissions()` for role-based logic
4. **Debug**: Use `UserInfoDebug` component to inspect stored data
