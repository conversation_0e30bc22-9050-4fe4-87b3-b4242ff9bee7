import { api } from './api'

// Types for Items API
export interface ItemDayData {
  date: string
  tran_date: number
  quantity_sold: number
  revenue_gross: number
  revenue_net: number
  discount_amount: number
  commission_amount: number
  partner_marketing_amount: number
}

export interface ItemData {
  group_id: string
  item_id: string
  item_name: string
  unit_id: string
  unit_name: string
  item_class_id: string
  item_class_name: string
  item_type_id: string
  item_type_name: string
  quantity_sold: number
  revenue_gross: number
  revenue_net: number
  discount_amount: number
  commission_amount: number
  partner_marketing_amount: number
  list_data: ItemDayData[]
}

export interface ItemsResponse {
  data: {
    list_data_item_return: ItemData[]
    other_data: Array<{
      item_type_id: string
      item_type_name: string
    }>
  }
  message: string
  track_id: string
}

export interface ItemsSummary {
  itemData: Array<{
    itemId: string
    itemName: string
    itemClass: string
    itemType: string
    quantitySold: number
    revenue: number
    revenueNet: number
    percentage: number
    unit: string
  }>
  totalQuantity: number
  totalRevenue: number
  totalRevenueNet: number
  itemCount: number
}

// Cache for items requests
const itemsCache = new Map<string, { data: ItemsResponse; timestamp: number }>()
const pendingRequests = new Map<string, Promise<ItemsResponse>>()
const CACHE_DURATION = 5 * 60 * 1000 // 5 minutes

// Items API Service
export const itemsApi = {
  /**
   * Get items summary with request deduplication
   */
  getItemsSummary: async (params: {
    companyUid: string
    brandUid: string
    startDate: number
    endDate: number
    storeUids?: string[]
    byDays?: number
    limit?: number
  }): Promise<ItemsResponse> => {
    const requestKey = `${params.companyUid}-${params.brandUid}-${params.startDate}-${params.endDate}-${params.storeUids?.join(',') || 'all'}-${params.byDays || 1}`

    const cached = itemsCache.get(requestKey)
    if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
      return cached.data
    }

    const existingRequest = pendingRequests.get(requestKey)
    if (existingRequest) {
      return existingRequest
    }

    const requestPromise = (async () => {
      try {
        const queryParams = new URLSearchParams({
          brand_uid: params.brandUid,
          company_uid: params.companyUid,
          start_date: params.startDate.toString(),
          end_date: params.endDate.toString(),
          store_open_at: '0',
          by_days: (params.byDays || 0).toString(),
          limit: (params.limit || 100).toString(),
          order_by: 'quantity_sold',
        })

        if (params.storeUids && params.storeUids.length > 0) {
          queryParams.set('list_store_uid', params.storeUids.join(','))
        }

        const response = await api.get(
          `/v1/reports/sale-summary/items?${queryParams.toString()}`,
          {
            headers: {
              Accept: 'application/json, text/plain, */*',
              'accept-language': 'vi',
              fabi_type: 'pos-cms',
              'x-client-timezone': '25200000', // GMT+7 timezone offset in milliseconds
            },
            timeout: 45000, // Increased to 45 seconds timeout for items API
          }
        )

        // Validate response structure
        if (!response.data || typeof response.data !== 'object') {
          throw new Error('Invalid response format from items API')
        }

        // Cache the result
        itemsCache.set(requestKey, {
          data: response.data as ItemsResponse,
          timestamp: Date.now(),
        })

        return response.data as ItemsResponse
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
      } catch (error: any) {
        if (
          error.code === 'ECONNABORTED' ||
          error.message?.includes('timeout')
        ) {
          throw new Error(
            'Request timeout - server is taking too long to respond. Try reducing the date range or number of stores.'
          )
        }

        if (error.response?.status === 504) {
          throw new Error(
            'Gateway timeout (504) - server is overloaded. Please try again later or reduce the data range.'
          )
        }

        if (error.response?.status === 503) {
          throw new Error(
            'Service unavailable (503) - server is temporarily down. Please try again later.'
          )
        }

        if (error.response?.status >= 500) {
          throw new Error(
            `Server error (${error.response.status}) - please try again later.`
          )
        }

        if (error.response?.status === 429) {
          throw new Error(
            'Too many requests - please wait a moment before trying again.'
          )
        }

        throw error
      } finally {
        pendingRequests.delete(requestKey)
      }
    })()

    pendingRequests.set(requestKey, requestPromise)

    return requestPromise
  },

  /**
   * Process items summary data with performance optimization
   */
  processItemsSummary: (response: ItemsResponse): ItemsSummary => {
    if (
      !response.data ||
      !response.data.list_data_item_return ||
      response.data.list_data_item_return.length === 0
    ) {
      return {
        itemData: [],
        totalQuantity: 0,
        totalRevenue: 0,
        totalRevenueNet: 0,
        itemCount: 0,
      }
    }

    const items = response.data.list_data_item_return

    // Optimize: Calculate totals and transform in single loop
    let totalQuantity = 0
    let totalRevenue = 0
    let totalRevenueNet = 0

    const itemData = items.map((item) => {
      const quantitySold = item.quantity_sold || 0
      const revenue = item.revenue_gross || 0
      const revenueNet = item.revenue_net || 0

      // Accumulate totals
      totalQuantity += quantitySold
      totalRevenue += revenue
      totalRevenueNet += revenueNet

      return {
        itemId: item.item_id,
        itemName: item.item_name,
        itemClass: item.item_class_name,
        itemType: item.item_type_name,
        quantitySold,
        revenue,
        revenueNet,
        percentage: 0, // Calculate later to avoid division by zero
        unit: item.unit_name,
      }
    })

    if (totalRevenue > 0) {
      itemData.forEach((item) => {
        item.percentage = (item.revenue / totalRevenue) * 100
      })
    }

    return {
      itemData,
      totalQuantity,
      totalRevenue,
      totalRevenueNet,
      itemCount: items.length,
    }
  },

  /**
   * Format currency for display
   */
  formatCurrency: (amount: number): string => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount)
  },
}

export default itemsApi
