import type { ItemClass, ItemClassApiData } from '@/types/item-class'
import { convertApiItemClassToItemClass } from '@/types/item-class'

import { apiClient } from './api'

// API response interface
interface ItemClassesApiResponse {
  data: ItemClassApiData[]
  track_id: string
}

// Parameters for fetching item classes
export interface GetItemClassesParams {
  company_uid?: string
  brand_uid?: string
  searchTerm?: string
  page?: number
  skip_limit?: boolean
}

/**
 * Fetch item classes from the API
 */
export const getItemClasses = async (params: GetItemClassesParams = {}): Promise<ItemClass[]> => {
  const posUserData = localStorage.getItem('pos_user_data')
  const posBrandData = localStorage.getItem('pos_brands_data')
  let companyUid = ''
  let brandUid = ''

  if (posUserData) {
    try {
      const userData = JSON.parse(posUserData)
      companyUid = userData.company_uid || ''
    } catch {
      // Error parsing user data
    }
  }

  if (posBrandData) {
    try {
      const brandData = JSON.parse(posBrandData)
      if (Array.isArray(brandData) && brandData.length > 0) {
        brandUid = brandData[0].id || ''
      }
    } catch {
      // Error parsing brand data
    }
  }

  if (!companyUid || !brandUid) {
    throw new Error('Company or brand UID not found in localStorage')
  }

  const queryParams = new URLSearchParams({
    company_uid: companyUid,
    brand_uid: brandUid
  })

  if (params.skip_limit) {
    queryParams.append('skip_limit', 'true')
  } else {
    queryParams.append('page', (params.page || 1).toString())
  }

  if (params.searchTerm) {
    queryParams.append('search', params.searchTerm)
  }

  const response = await apiClient.get<ItemClassesApiResponse>(
    `/mdata/v1/item-classes?${queryParams.toString()}`
  )

  if (response.data?.data) {
    return response.data.data.map((apiItemClass: ItemClassApiData) => {
      return convertApiItemClassToItemClass(apiItemClass)
    })
  }

  return []
}

/**
 * Fetch a single item class by ID
 */
export const getItemClass = async (id: string): Promise<ItemClass> => {
  const response = await apiClient.get<{ data: ItemClassApiData; track_id: string }>(
    `/mdata/v1/item-class?id=${id}`
  )

  if (response.data?.data) {
    return convertApiItemClassToItemClass(response.data.data)
  }

  throw new Error('Item class not found')
}

// Parameters for creating item class
export interface CreateItemClassParams {
  item_class_name: string
  item_class_id?: string
  description?: string
  list_item?: string[]
}

/**
 * Create a new item class
 */
export const createItemClass = async (params: CreateItemClassParams): Promise<ItemClass> => {
  const posUserData = localStorage.getItem('pos_user_data')
  const posBrandData = localStorage.getItem('pos_brands_data')
  let companyUid = ''
  let brandUid = ''

  if (posUserData) {
    try {
      const userData = JSON.parse(posUserData)
      companyUid = userData.company_uid || ''
    } catch {
      // Error parsing user data
    }
  }

  if (posBrandData) {
    try {
      const brandData = JSON.parse(posBrandData)
      if (Array.isArray(brandData) && brandData.length > 0) {
        brandUid = brandData[0].id || ''
      }
    } catch {
      // Error parsing brand data
    }
  }

  if (!companyUid || !brandUid) {
    throw new Error('Company or brand UID not found in localStorage')
  }

  const payload = {
    item_class_name: params.item_class_name,
    item_class_id:
      params.item_class_id ||
      `ITEM_CLASS-${Math.random().toString(36).substring(2, 6).toUpperCase()}`,
    company_uid: companyUid,
    brand_uid: brandUid,
    list_item: params.list_item || [],
    sort: 1000
  }

  const response = await apiClient.post<{ data: ItemClassApiData }>(
    `/mdata/v1/item-classes`,
    payload
  )

  if (response.data?.data) {
    return convertApiItemClassToItemClass(response.data.data)
  }

  throw new Error('Failed to create item class')
}

/**
 * Update an item class
 */
export const updateItemClass = async (itemClass: ItemClass): Promise<ItemClass> => {
  const payload = {
    id: itemClass.id,
    item_class_id: itemClass.item_class_id,
    item_class_name: itemClass.item_class_name,
    description: itemClass.description,
    sort: itemClass.sort,
    extra_data: itemClass.extra_data,
    active: itemClass.active,
    revision: itemClass.revision,
    brand_uid: itemClass.brand_uid,
    company_uid: itemClass.company_uid,
    created_by: itemClass.created_by,
    updated_by: itemClass.updated_by,
    deleted_by: itemClass.deleted_by,
    created_at: itemClass.created_at,
    updated_at: itemClass.updated_at,
    deleted_at: itemClass.deleted_at,
    deleted: itemClass.deleted,
    list_item: itemClass.list_item || []
  }

  const response = await apiClient.put<{ data: ItemClassApiData }>(`/mdata/v1/item-class`, payload)

  if (response.data?.data) {
    return convertApiItemClassToItemClass(response.data.data)
  }

  throw new Error('Failed to update item class')
}

/**
 * Delete an item class
 */
export const deleteItemClass = async (itemClassId: string): Promise<void> => {
  const posUserData = localStorage.getItem('pos_user_data')
  const posBrandData = localStorage.getItem('pos_brands_data')

  let companyUid = ''
  let brandUid = ''

  if (posUserData) {
    try {
      const userData = JSON.parse(posUserData)
      companyUid = userData.company_uid || ''
    } catch {
      // Error parsing user data
    }
  }

  if (posBrandData) {
    try {
      const brandData = JSON.parse(posBrandData)
      if (Array.isArray(brandData) && brandData.length > 0) {
        brandUid = brandData[0].id || ''
      }
    } catch {
      // Error parsing brand data
    }
  }

  if (!companyUid || !brandUid) {
    throw new Error('Company or brand UID not found in localStorage')
  }

  const queryParams = new URLSearchParams({
    company_uid: companyUid,
    brand_uid: brandUid,
    id: itemClassId
  })

  await apiClient.delete(`/mdata/v1/item-class?${queryParams.toString()}`)
}

// Export all API functions
export const itemClassesApi = {
  getItemClasses,
  getItemClass,
  createItemClass,
  updateItemClass,
  deleteItemClass
}
