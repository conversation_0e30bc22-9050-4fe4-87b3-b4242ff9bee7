import type {
  Customization,
  CustomizationApiData,
  CustomizationsApiResponse,
  GetCustomizationsParams,
  CopyCustomizationParams,
  CreateCustomizationParams
} from '@/types/customizations'
import { convertApiCustomizationToCustomization } from '@/types/customizations'
import { ExistingCustomization } from '@/types/customizations'

import { apiClient } from './api'
import { createCustomizationsExcelFile, bulkImportCustomizations } from './customizations-excel'

export type {
  GetCustomizationsParams,
  CopyCustomizationParams,
  CreateCustomizationParams
} from '@/types/customizations'

interface CityData {
  id: string
  city_name: string
}

/**
 * Get all city UIDs from localStorage for filtering
 */
const getAllCityUids = (): string[] => {
  try {
    const citiesData = localStorage.getItem('pos_cities_data')
    if (citiesData) {
      const cities: CityData[] = JSON.parse(citiesData)
      return cities.map(city => city.id)
    }
  } catch {
    // Error parsing cities data
  }
  return []
}

/**
 * Fetch customizations from the API
 */
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const getCustomizations = async (params: any = {}): Promise<Customization[]> => {
  const queryParams = new URLSearchParams()

  if (params.company_uid) queryParams.append('company_uid', params.company_uid)
  if (params.brand_uid) queryParams.append('brand_uid', params.brand_uid)

  if (params.skip_limit) queryParams.append('skip_limit', 'true')
  if (params.store_uid) queryParams.append('store_uid', params.store_uid)
  if (params.page) queryParams.append('page', String(params.page))
  if (params.limit) queryParams.append('limit', String(params.limit))

  if (params.searchTerm) queryParams.append('search', params.searchTerm)

  if (!params.skip_limit) {
    if (params.listStoreUid && params.listStoreUid.length > 0) {
      if (params.listStoreUid.length === 1) {
        queryParams.append('store_uid', params.listStoreUid[0])
      } else {
        queryParams.append('list_store_uid', params.listStoreUid.join(','))
      }
    } else if (params.listCityUid && params.listCityUid.length > 0) {
      const allCityUids = getAllCityUids()
      const isAllCitiesSelected =
        params.listCityUid.length === allCityUids.length &&
        params.listCityUid.every((cityUid: string) => allCityUids.includes(cityUid))

      if (isAllCitiesSelected) {
        queryParams.append('list_city_uid', allCityUids.join(','))
      } else {
        queryParams.append('list_city_uid', params.listCityUid.join(','))
      }
    } else {
      const allCityUids = getAllCityUids()

      if (allCityUids.length > 0) {
        queryParams.append('list_city_uid', allCityUids.join(','))
      } else {
        throw new Error('No cities or stores available for filtering')
      }
    }
  }

  const apiUrl = `/mdata/v1/customizations?${queryParams.toString()}`
  const response = await apiClient.get<CustomizationsApiResponse>(apiUrl)

  if (response.data?.data) {
    const convertedCustomizations = response.data.data.map(
      (apiCustomization: CustomizationApiData) => {
        return convertApiCustomizationToCustomization(apiCustomization)
      }
    )

    return convertedCustomizations
  }

  return []
}

/**
 * Get customization details by ID
 */
export const getCustomizationById = async (
  customizationId: string
): Promise<CustomizationApiData> => {
  const apiUrl = `/mdata/v1/customization?id=${customizationId}`
  const response = await apiClient.get<{
    data: CustomizationApiData
    track_id: string
  }>(apiUrl)

  if (response.data?.data) {
    return response.data.data
  }

  throw new Error('Customization not found')
}

/**
 * Copy a customization (two-step process: get details then create new)
 */
export const copyCustomization = async (params: CopyCustomizationParams): Promise<void> => {
  const posUserData = localStorage.getItem('pos_user_data')
  const posBrandData = localStorage.getItem('pos_brands_data')
  let companyUid = ''
  let brandUid = ''

  if (posUserData) {
    try {
      const userData = JSON.parse(posUserData)
      companyUid = userData.company_uid || ''
    } catch {
      // Error parsing user data
    }
  }

  if (posBrandData) {
    try {
      const brandData = JSON.parse(posBrandData)
      if (Array.isArray(brandData) && brandData.length > 0) {
        brandUid = brandData[0].id || ''
      }
    } catch {
      // Error parsing brand data
    }
  }

  if (!companyUid || !brandUid) {
    throw new Error('Company or brand UID not found in localStorage')
  }

  const originalCustomization = await getCustomizationById(params.customizationId)

  const payload: Record<string, unknown> = {
    data: originalCustomization.data,
    sort: originalCustomization.sort,
    brand_uid: brandUid,
    company_uid: companyUid,
    list_item: originalCustomization.list_item || [],
    name: params.newName
  }

  if (params.targetStoreUid) {
    payload.store_uid = params.targetStoreUid
  } else {
    payload.city_uid = params.targetCityUid || originalCustomization.city_uid
  }

  const apiUrl = `/mdata/v1/customization`

  await apiClient.post(apiUrl, payload)
}

/**
 * Create a new customization
 */
export const createCustomization = async (params: CreateCustomizationParams): Promise<void> => {
  const posUserData = localStorage.getItem('pos_user_data')
  const posBrandData = localStorage.getItem('pos_brands_data')
  let companyUid = ''
  let brandUid = ''

  if (posUserData) {
    try {
      const userData = JSON.parse(posUserData)
      companyUid = userData.company_uid || ''
    } catch {
      // Error parsing user data
    }
  }

  if (posBrandData) {
    try {
      const brandData = JSON.parse(posBrandData)
      if (Array.isArray(brandData) && brandData.length > 0) {
        brandUid = brandData[0].id || ''
      }
    } catch {
      // Error parsing brand data
    }
  }

  if (!companyUid || !brandUid) {
    throw new Error('Company or brand UID not found in localStorage')
  }

  const payload: Record<string, unknown> = {
    name: params.name,
    data: params.data,
    list_item: params.listItem,
    company_uid: companyUid,
    brand_uid: brandUid,
    sort: params.sort || 1000,
    is_update_same_customization: params.isUpdateSameCustomization || false
  }

  if (params.storeUid) {
    payload.store_uid = params.storeUid
  } else if (params.cityUid) {
    payload.city_uid = params.cityUid
  } else {
    throw new Error('Either storeUid or cityUid must be provided')
  }

  const apiUrl = `/mdata/v1/customization`

  await apiClient.post(apiUrl, payload)
}

/**
 * Update customization by ID
 */
export const updateCustomization = async (
  customizationId: string,
  params: CreateCustomizationParams,
  existingCustomization: ExistingCustomization
): Promise<void> => {
  const posUserData = localStorage.getItem('pos_user_data')
  const posBrandData = localStorage.getItem('pos_brands_data')
  let companyUid = ''
  let brandUid = ''

  if (posUserData) {
    try {
      const userData = JSON.parse(posUserData)
      companyUid = userData.company_uid || ''
    } catch {
      // Error parsing user data
    }
  }

  if (posBrandData) {
    try {
      const brandData = JSON.parse(posBrandData)
      if (Array.isArray(brandData) && brandData.length > 0) {
        brandUid = brandData[0].id || ''
      }
    } catch {
      // Error parsing brand data
    }
  }

  if (!companyUid || !brandUid) {
    throw new Error('Company or brand UID not found in localStorage')
  }

  const payload: Record<string, unknown> = {
    id: customizationId,
    name: params.name,
    data: params.data,
    extra_data: existingCustomization?.extra_data || existingCustomization?.extraData || {},
    active: existingCustomization?.active || 1,
    revision: existingCustomization?.revision || 0,
    sort: params.sort || 1000,
    brand_uid: brandUid,
    company_uid: companyUid,
    is_customization_in_store:
      existingCustomization?.is_customization_in_store ||
      existingCustomization?.isCustomizationInStore ||
      1,
    customization_cloned_id:
      existingCustomization?.customization_cloned_id ||
      existingCustomization?.customizationClonedId ||
      null,
    is_fabi: existingCustomization?.is_fabi || 1,
    created_at: existingCustomization?.created_at,
    updated_at: Math.floor(Date.now() / 1000), // Current timestamp
    deleted_at: existingCustomization?.deleted_at || null,
    created_by: existingCustomization?.created_by || '',
    updated_by: existingCustomization?.updated_by || '',
    deleted_by: existingCustomization?.deleted_by || null,
    deleted: existingCustomization?.deleted || false,
    list_item: params.listItem,
    is_update_same_customization: params.isUpdateSameCustomization || false
  }

  if (params.storeUid) {
    payload.store_uid = params.storeUid
    payload.city_uid = existingCustomization?.city_uid || existingCustomization?.cityUid || ''
  } else if (params.cityUid) {
    payload.city_uid = params.cityUid
    payload.store_uid = existingCustomization?.store_uid || existingCustomization?.storeUid || ''
  } else {
    throw new Error('Either storeUid or cityUid must be provided')
  }

  const apiUrl = `/mdata/v1/customization`

  await apiClient.put(apiUrl, payload)
}

/**
 * Delete a customization
 */
export const deleteCustomization = async (customizationId: string): Promise<void> => {
  const posUserData = localStorage.getItem('pos_user_data')
  const posBrandData = localStorage.getItem('pos_brands_data')
  let companyUid = ''
  let brandUid = ''

  if (posUserData) {
    try {
      const userData = JSON.parse(posUserData)
      companyUid = userData.company_uid || ''
    } catch {
      // Error parsing user data
    }
  }

  if (posBrandData) {
    try {
      const brandData = JSON.parse(posBrandData)
      if (Array.isArray(brandData) && brandData.length > 0) {
        brandUid = brandData[0].id || ''
      }
    } catch {
      // Error parsing brand data
    }
  }

  if (!companyUid || !brandUid) {
    throw new Error('Company or brand UID not found in localStorage')
  }

  const queryParams = new URLSearchParams({
    company_uid: companyUid,
    brand_uid: brandUid,
    id: customizationId
  })

  const apiUrl = `/mdata/v1/customization?${queryParams.toString()}`

  await apiClient.delete(apiUrl)
}

/**
 * Export customizations to Excel
 */
export const exportCustomizations = async (params: GetCustomizationsParams = {}): Promise<void> => {
  const posUserData = localStorage.getItem('pos_user_data')
  const posBrandData = localStorage.getItem('pos_brands_data')
  let companyUid = ''
  let brandUid = ''

  if (posUserData) {
    try {
      const userData = JSON.parse(posUserData)
      companyUid = userData.company_uid || ''
    } catch {
      // Error parsing user data
    }
  }

  if (posBrandData) {
    try {
      const brandData = JSON.parse(posBrandData)
      if (Array.isArray(brandData) && brandData.length > 0) {
        brandUid = brandData[0].id || ''
      }
    } catch {
      // Error parsing brand data
    }
  }

  if (!companyUid || !brandUid) {
    throw new Error('Company or brand UID not found in localStorage')
  }

  const queryParams = new URLSearchParams({
    skip_limit: 'true',
    company_uid: companyUid,
    brand_uid: brandUid
  })

  if (params.listStoreUid && params.listStoreUid.length > 0) {
    if (params.listStoreUid.length === 1) {
      queryParams.append('store_uid', params.listStoreUid[0])
    } else {
      queryParams.append('list_store_uid', params.listStoreUid.join(','))
    }
  } else if (params.listCityUid && params.listCityUid.length > 0) {
    const allCityUids = getAllCityUids()
    const isAllCitiesSelected =
      params.listCityUid.length === allCityUids.length &&
      params.listCityUid.every(cityUid => allCityUids.includes(cityUid))

    if (isAllCitiesSelected) {
      queryParams.append('list_city_uid', allCityUids.join(','))
    } else {
      queryParams.append('list_city_uid', params.listCityUid.join(','))
    }
  } else {
    const allCityUids = getAllCityUids()

    if (allCityUids.length > 0) {
      queryParams.append('list_city_uid', allCityUids.join(','))
    } else {
      throw new Error('No cities or stores available for filtering')
    }
  }

  const apiUrl = `/mdata/v1/customizations?${queryParams.toString()}`

  const response = await apiClient.get<CustomizationsApiResponse>(apiUrl)

  if (response.data?.data) {
    const customizations = response.data.data.map((apiCustomization: CustomizationApiData) => {
      return convertApiCustomizationToCustomization(apiCustomization)
    })

    // For store-based customizations, add store names
    const customizationsWithStoreNames = customizations.map(customization => {
      // Check if this is a store-based customization request
      if (params.listStoreUid && params.listStoreUid.length > 0 && params.storeName) {
        return {
          ...customization,
          storeName: params.storeName,
          storeUid: params.listStoreUid[0] // Add storeUid for detection
        }
      }
      return customization
    })

    await createCustomizationsExcelFile(customizationsWithStoreNames)
  } else {
    throw new Error('No customizations data received from API')
  }
}

export const customizationsApi = {
  getCustomizations,
  getCustomizationById,
  copyCustomization,
  createCustomization,
  updateCustomization,
  deleteCustomization,
  exportCustomizations,
  bulkImportCustomizations
}
