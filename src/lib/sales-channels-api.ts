import type { GetSourcesParams, SourcesApiResponse, Source } from '@/types/sources'

import { apiClient } from './api'

/**
 * Fetch sales channels (sources) from API
 */
export const getSalesChannels = async (params: GetSourcesParams = {}): Promise<Source[]> => {
  const queryParams = new URLSearchParams()

  // Add company and brand UIDs
  if (params.companyUid) {
    queryParams.append('company_uid', params.companyUid)
  }
  if (params.brandUid) {
    queryParams.append('brand_uid', params.brandUid)
  }

  // Add partner config
  if (params.partnerConfig !== undefined) {
    queryParams.append('partner_config', params.partnerConfig.toString())
  }

  // Add store UIDs
  if (params.listStoreUid && params.listStoreUid.length > 0) {
    queryParams.append('list_store_uid', params.listStoreUid.join(','))
  }

  // Add skip limit
  if (params.skipLimit) {
    queryParams.append('skip_limit', 'true')
  }

  const response = await apiClient.get<SourcesApiResponse>(
    `/mdata/v1/sources?${queryParams.toString()}`
  )

  if (response.data?.data) {
    // Import the conversion function dynamically to avoid circular dependency
    const { convertApiSourceToSource } = await import('@/types/sources')
    return response.data.data.map(convertApiSourceToSource)
  }

  return []
}
