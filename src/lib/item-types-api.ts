import { api } from './api'

// Types for Item Types API
export interface ItemType {
  id: string
  item_type_id: string
  item_type_name: string
  item_type_parent_id: string | null
  item_type_color: string | null
  list_order: number | null
  is_material: number | null
  print_name_menu: string | null
  image_path: string | null
  description: string | null
  sort: number
  sort_online: number
  extra_data: Record<string, unknown> | null
  active: number
  revision: number
  store_uid: string | null
  brand_uid: string
  company_uid: string
  is_fabi: number
  created_by: string | null
  updated_by: string | null
  deleted_by: string | null
  created_at: number
  updated_at: number
  deleted_at: number | null
  deleted: boolean
  list_item?: string[]
}

export interface ItemTypesApiResponse {
  data: ItemType[]
  track_id: string
}

export interface GetItemTypesParams {
  company_uid: string
  brand_uid: string
  skip_limit?: boolean
  page?: number
  limit?: number
  search?: string
  active?: number
  store_uid?: string
}

// Cache for item types requests
const itemTypesCache = new Map<string, { data: ItemTypesApiResponse; timestamp: number }>()
const pendingRequests = new Map<string, Promise<ItemTypesApiResponse>>()
const CACHE_DURATION = 15 * 60 * 1000 // 15 minutes (item types change less frequently)

// Item Types API Service
export const itemTypesApi = {
  /**
   * Get item types data with request deduplication and caching
   */
  getItemTypes: async (params: GetItemTypesParams): Promise<ItemTypesApiResponse> => {
    const requestKey = `${params.company_uid}-${params.brand_uid}-${params.skip_limit || false}-${params.page || 1}-${params.limit || 50}-${params.search || ''}-${params.active ?? 1}-${params.store_uid || 'all'}`

    // Check cache first
    const cached = itemTypesCache.get(requestKey)
    if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
      return cached.data
    }

    // Check if there's already a pending request
    const existingRequest = pendingRequests.get(requestKey)
    if (existingRequest) {
      return existingRequest
    }

    // Create new request
    const requestPromise = (async () => {
      try {
        const queryParams = new URLSearchParams()
        queryParams.append('company_uid', params.company_uid)
        queryParams.append('brand_uid', params.brand_uid)

        if (params.skip_limit !== undefined) {
          queryParams.append('skip_limit', params.skip_limit.toString())
        }

        if (params.page) {
          queryParams.append('page', params.page.toString())
        }

        if (params.limit) {
          queryParams.append('limit', params.limit.toString())
        }

        if (params.search) {
          queryParams.append('search', params.search)
        }

        if (params.active !== undefined) {
          queryParams.append('active', params.active.toString())
        }

        if (params.store_uid) {
          queryParams.append('store_uid', params.store_uid)
        }

        const response = await api.get(`/mdata/v1/item-types?${queryParams.toString()}`, {
          headers: {
            Accept: 'application/json, text/plain, */*',
            'accept-language': 'vi',
            fabi_type: 'pos-cms',
            'x-client-timezone': '25200000' // GMT+7 timezone offset in milliseconds
          },
          timeout: 30000 // 30 seconds timeout
        })

        // Validate response structure
        if (!response.data || typeof response.data !== 'object') {
          throw new Error('Invalid response format from item types API')
        }

        const result = response.data as ItemTypesApiResponse

        // Cache the result
        itemTypesCache.set(requestKey, {
          data: result,
          timestamp: Date.now()
        })

        return result
      } finally {
        pendingRequests.delete(requestKey)
      }
    })()

    // Store the pending request
    pendingRequests.set(requestKey, requestPromise)

    return requestPromise
  },

  /**
   * Clear cache for item types
   */
  clearCache: () => {
    itemTypesCache.clear()
    pendingRequests.clear()
  },

  /**
   * Get cache stats
   */
  getCacheStats: () => ({
    cacheSize: itemTypesCache.size,
    pendingRequests: pendingRequests.size
  }),

  /**
   * Get item type by ID from cache
   */
  getItemTypeById: (id: string): ItemType | undefined => {
    for (const cached of itemTypesCache.values()) {
      const itemType = cached.data.data.find(item => item.id === id)
      if (itemType) return itemType
    }
    return undefined
  },

  /**
   * Get item type by item_type_id from cache
   */
  getItemTypeByTypeId: (itemTypeId: string): ItemType | undefined => {
    for (const cached of itemTypesCache.values()) {
      const itemType = cached.data.data.find(item => item.item_type_id === itemTypeId)
      if (itemType) return itemType
    }
    return undefined
  },

  /**
   * Get single item type by ID from server
   */
  getItemTypeByIdFromServer: async (id: string): Promise<ItemType> => {
    const response = await api.get(`/mdata/v1/item-type?id=${id}`, {
      headers: {
        Accept: 'application/json, text/plain, */*',
        'accept-language': 'vi',
        fabi_type: 'pos-cms',
        'x-client-timezone': '25200000'
      },
      timeout: 30000
    })

    if (!response.data || typeof response.data !== 'object') {
      throw new Error('Invalid response format from item type API')
    }

    const result = response.data as { data: ItemType; track_id: string }

    if (!result.data) {
      throw new Error('Item type not found')
    }

    return result.data
  },

  /**
   * Update item type status (toggle active/inactive)
   */
  updateItemTypeStatus: async (itemType: ItemType): Promise<ItemType> => {
    const updateData = {
      ...itemType,
      list_item: itemType.list_item || [] // Use provided list_item or empty array
    }

    const response = await api.put('/mdata/v1/item-type', updateData, {
      headers: {
        Accept: 'application/json, text/plain, */*',
        'Content-Type': 'application/json;charset=UTF-8',
        'accept-language': 'vi',
        fabi_type: 'pos-cms',
        'x-client-timezone': '25200000'
      },
      timeout: 30000
    })

    itemTypesCache.clear()

    return response.data.data as ItemType
  },

  /**
   * Update item type (name, sort, etc.)
   */
  updateItemType: async (itemType: ItemType): Promise<ItemType> => {
    const updateData = {
      ...itemType,
      list_item: itemType.list_item || [] // Required field based on cURL data
    }

    const response = await api.put('/mdata/v1/item-type', updateData, {
      headers: {
        Accept: 'application/json, text/plain, */*',
        'Content-Type': 'application/json;charset=UTF-8',
        'accept-language': 'vi',
        fabi_type: 'pos-cms',
        'x-client-timezone': '25200000'
      },
      timeout: 30000
    })

    itemTypesCache.clear()

    return response.data.data as ItemType
  },

  /**
   * Delete item type
   */
  deleteItemType: async (itemTypeId: string): Promise<void> => {
    const posUserData = localStorage.getItem('pos_user_data')
    const posBrandData = localStorage.getItem('pos_brands_data')
    let companyUid = ''
    let brandUid = ''

    if (posUserData) {
      try {
        const userData = JSON.parse(posUserData)
        companyUid = userData.company_uid || ''
      } catch {
        // Error parsing user data
      }
    }

    if (posBrandData) {
      try {
        const brandData = JSON.parse(posBrandData)
        if (Array.isArray(brandData) && brandData.length > 0) {
          brandUid = brandData[0].id || ''
        }
      } catch {
        // Error parsing brand data
      }
    }

    if (!companyUid || !brandUid) {
      throw new Error('Company or brand UID not found in localStorage')
    }

    const queryParams = new URLSearchParams()
    queryParams.append('company_uid', companyUid)
    queryParams.append('brand_uid', brandUid)
    queryParams.append('id', itemTypeId)

    await api.delete(`/mdata/v1/item-type?${queryParams.toString()}`, {
      headers: {
        Accept: 'application/json, text/plain, */*',
        'accept-language': 'vi',
        fabi_type: 'pos-cms',
        'x-client-timezone': '25200000'
      },
      timeout: 30000
    })

    // Clear cache after delete
    itemTypesCache.clear()
  },

  /**
   * Bulk create item types
   */
  bulkCreateItemTypes: async (
    itemTypes: Array<{
      item_type_name: string
      item_type_id: string
      sort: number
    }>
  ): Promise<ItemType[]> => {
    const posUserData = localStorage.getItem('pos_user_data')
    const posBrandData = localStorage.getItem('pos_brands_data')
    let companyUid = ''
    let brandUid = ''

    if (posUserData) {
      try {
        const userData = JSON.parse(posUserData)
        companyUid = userData.company_uid || ''
      } catch {
        // Error parsing user data
      }
    }

    if (posBrandData) {
      try {
        const brandData = JSON.parse(posBrandData)
        if (Array.isArray(brandData) && brandData.length > 0) {
          brandUid = brandData[0].id || ''
        }
      } catch {
        // Error parsing brand data
      }
    }

    if (!companyUid || !brandUid) {
      throw new Error('Company or brand UID not found in localStorage')
    }

    // Add company_uid and brand_uid to each item
    const itemTypesWithAuth = itemTypes.map(item => ({
      ...item,
      company_uid: companyUid,
      brand_uid: brandUid
    }))

    const response = await api.post('/mdata/v1/item-types', itemTypesWithAuth, {
      headers: {
        Accept: 'application/json, text/plain, */*',
        'Content-Type': 'application/json;charset=UTF-8',
        'accept-language': 'vi',
        fabi_type: 'pos-cms',
        'x-client-timezone': '25200000'
      },
      timeout: 30000
    })

    // Clear cache after create
    itemTypesCache.clear()

    const result = response.data as ItemTypesApiResponse
    return result.data || []
  }
}
