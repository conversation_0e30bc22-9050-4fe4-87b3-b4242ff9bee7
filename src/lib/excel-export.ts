/* eslint-disable @typescript-eslint/no-explicit-any */
import * as XLSX from 'xlsx'
import { SaleNotSyncVatData } from '@/lib/sales-api'

// Format currency for Excel (remove formatting, keep numbers)
function formatCurrencyForExcel(amount: number): number {
  return amount
}

// Format date for Excel
function formatDateForExcel(timestamp: number): string {
  return new Date(timestamp).toLocaleDateString('vi-VN', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  })
}

// Format percentage for Excel
function formatPercentageForExcel(value: number): string {
  return `${value.toFixed(1)}%`
}

// Export Sale Not Sync VAT data to Excel
export function exportSaleNotSyncVatToExcel(
  data: (SaleNotSyncVatData & { storeName: string })[],
  options: {
    filename?: string
    sheetName?: string
    showStoreInfo?: boolean
    showEmployeeInfo?: boolean
    showPaymentMethod?: boolean
  } = {}
) {
  const {
    filename = `sale-not-sync-vat-${new Date().toISOString().split('T')[0]}.xlsx`,
    sheetName = 'Giao dịch chưa đồng bộ VAT',
    showStoreInfo = true,
    showEmployeeInfo = true,
    showPaymentMethod = true,
  } = options

  // Prepare data for Excel
  const excelData = data.map((sale) => {
    const discountPercentage =
      sale.amount_origin > 0
        ? ((sale.amount_origin - sale.total_amount) / sale.amount_origin) * 100
        : 0

    const row: Record<string, any> = {
      'Mã giao dịch': sale.tran_no,
      'Thời gian': formatDateForExcel(sale.tran_date),
    }

    if (showStoreInfo) {
      row['Cửa hàng'] = sale.storeName
    }

    if (showEmployeeInfo) {
      row['Nhân viên'] = sale.employee_name
    }

    row['Loại bàn'] = sale.table_name

    if (showPaymentMethod) {
      row['Phương thức thanh toán'] = sale.payment_method_name
    }

    row['Mã voucher'] = sale.voucher_code || '-'
    row['Số tiền gốc (VNĐ)'] = formatCurrencyForExcel(sale.amount_origin)
    row['Thành tiền (VNĐ)'] = formatCurrencyForExcel(sale.total_amount)
    row['Giảm giá'] = formatPercentageForExcel(discountPercentage)
    row['Số tiền giảm (VNĐ)'] = formatCurrencyForExcel(
      sale.amount_origin - sale.total_amount
    )

    return row
  })

  // Create workbook and worksheet
  const workbook = XLSX.utils.book_new()
  const worksheet = XLSX.utils.json_to_sheet(excelData)

  // Set column widths
  const columnWidths = [
    { wch: 15 }, // Mã giao dịch
    { wch: 20 }, // Thời gian
  ]

  if (showStoreInfo) columnWidths.push({ wch: 25 }) // Cửa hàng
  if (showEmployeeInfo) columnWidths.push({ wch: 20 }) // Nhân viên

  columnWidths.push(
    { wch: 15 } // Loại bàn
  )

  if (showPaymentMethod) columnWidths.push({ wch: 20 }) // Phương thức thanh toán

  columnWidths.push(
    { wch: 15 }, // Mã voucher
    { wch: 18 }, // Số tiền gốc
    { wch: 18 }, // Thành tiền
    { wch: 12 }, // Giảm giá
    { wch: 18 } // Số tiền giảm
  )

  worksheet['!cols'] = columnWidths

  // Add worksheet to workbook
  XLSX.utils.book_append_sheet(workbook, worksheet, sheetName)

  // Save file
  XLSX.writeFile(workbook, filename)
}

// Export any data array to Excel (generic function)
export function exportToExcel<T extends Record<string, any>>(
  data: T[],
  options: {
    filename?: string
    sheetName?: string
    columnMapping?: Record<keyof T, string> // Map field names to column headers
  } = {}
) {
  const {
    filename = `export-${new Date().toISOString().split('T')[0]}.xlsx`,
    sheetName = 'Data',
    columnMapping,
  } = options

  // Transform data if column mapping is provided
  const excelData = columnMapping
    ? data.map((row) => {
        const transformedRow: Record<string, any> = {}
        Object.entries(columnMapping).forEach(([key, header]) => {
          transformedRow[header] = row[key]
        })
        return transformedRow
      })
    : data

  // Create workbook and worksheet
  const workbook = XLSX.utils.book_new()
  const worksheet = XLSX.utils.json_to_sheet(excelData)

  // Auto-size columns
  const range = XLSX.utils.decode_range(worksheet['!ref'] || 'A1')
  const columnWidths: { wch: number }[] = []

  for (let col = range.s.c; col <= range.e.c; col++) {
    let maxWidth = 10
    for (let row = range.s.r; row <= range.e.r; row++) {
      const cellAddress = XLSX.utils.encode_cell({ r: row, c: col })
      const cell = worksheet[cellAddress]
      if (cell && cell.v) {
        const cellLength = cell.v.toString().length
        maxWidth = Math.max(maxWidth, cellLength)
      }
    }
    columnWidths.push({ wch: Math.min(maxWidth + 2, 50) }) // Max width 50
  }

  worksheet['!cols'] = columnWidths

  // Add worksheet to workbook
  XLSX.utils.book_append_sheet(workbook, worksheet, sheetName)

  // Save file
  XLSX.writeFile(workbook, filename)
}

// Export multiple sheets to one Excel file
export function exportMultipleSheetsToExcel(
  sheets: Array<{
    data: Record<string, any>[]
    sheetName: string
    columnMapping?: Record<string, string>
  }>,
  filename: string = `multi-sheet-export-${new Date().toISOString().split('T')[0]}.xlsx`
) {
  const workbook = XLSX.utils.book_new()

  sheets.forEach(({ data, sheetName, columnMapping }) => {
    // Transform data if column mapping is provided
    const excelData = columnMapping
      ? data.map((row) => {
          const transformedRow: Record<string, any> = {}
          Object.entries(columnMapping).forEach(([key, header]) => {
            transformedRow[header] = row[key]
          })
          return transformedRow
        })
      : data

    const worksheet = XLSX.utils.json_to_sheet(excelData)

    // Auto-size columns
    const range = XLSX.utils.decode_range(worksheet['!ref'] || 'A1')
    const columnWidths: { wch: number }[] = []

    for (let col = range.s.c; col <= range.e.c; col++) {
      let maxWidth = 10
      for (let row = range.s.r; row <= range.e.r; row++) {
        const cellAddress = XLSX.utils.encode_cell({ r: row, c: col })
        const cell = worksheet[cellAddress]
        if (cell && cell.v) {
          const cellLength = cell.v.toString().length
          maxWidth = Math.max(maxWidth, cellLength)
        }
      }
      columnWidths.push({ wch: Math.min(maxWidth + 2, 50) })
    }

    worksheet['!cols'] = columnWidths
    XLSX.utils.book_append_sheet(workbook, worksheet, sheetName)
  })

  XLSX.writeFile(workbook, filename)
}
