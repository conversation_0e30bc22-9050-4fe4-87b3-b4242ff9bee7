import { salesApiCore } from './sales-api-core'
import {
  SaleData,
  VoucherSalesSummary,
  GetSalesByVoucherCodesParams,
  GetVoucherSummaryParams,
  GetMultiStoreVoucherSummaryParams,
  TotalVoucherAmountOriginResponse,
} from './sales-types'

/**
 * Voucher-related Sales API functions
 */
export const salesApiVoucher = {
  /**
   * Get sales data filtered by specific voucher codes
   */
  getSalesByVoucherCodes: async (
    params: GetSalesByVoucherCodesParams
  ): Promise<SaleData[]> => {
    const salesData = await salesApiCore.getAllSalesData({
      ...params,
      isDiscount: 1, // Only get discounted sales
    })

    // Filter sales data by voucher codes
    const filteredSales = salesData.filter(
      (sale) =>
        sale.voucher_code && params.voucherCodes.includes(sale.voucher_code)
    )

    return filteredSales
  },

  /**
   * Get voucher summary for specific voucher codes
   */
  getVoucherSummary: async (
    params: GetVoucherSummaryParams
  ): Promise<VoucherSalesSummary> => {
    // Get voucher sales
    const voucherSales = await salesApiVoucher.getSalesByVoucherCodes(params)

    // Calculate totals from voucher sales
    const totalAmountOrigin = voucherSales.reduce(
      (sum, sale) => sum + sale.amount_origin,
      0
    )
    const totalDiscountAmount = voucherSales.reduce(
      (sum, sale) => sum + sale.discount_amount,
      0
    )
    const totalNetAmount = voucherSales.reduce(
      (sum, sale) => sum + sale.net_amount,
      0
    )

    // Get store name from first voucher sale (if available)
    const storeName = voucherSales.length > 0 ? voucherSales[0].store_name : ''

    return {
      storeUid: params.listStoreUid,
      storeName,
      totalAmountOrigin,
      totalDiscountAmount,
      totalNetAmount,
      transactionCount: voucherSales.length,
      voucherSales,
    }
  },

  /**
   * Get voucher summary for multiple stores
   */
  getMultiStoreVoucherSummary: async (
    params: GetMultiStoreVoucherSummaryParams
  ): Promise<VoucherSalesSummary[]> => {
    const summaries: VoucherSalesSummary[] = []

    // Process stores in batches to avoid overwhelming the API
    const batchSize = 5
    for (let i = 0; i < params.storeUids.length; i += batchSize) {
      const batch = params.storeUids.slice(i, i + batchSize)

      const batchPromises = batch.map(async (storeUid) => {
        try {
          return await salesApiVoucher.getVoucherSummary({
            companyUid: params.companyUid,
            brandUid: params.brandUid,
            listStoreUid: storeUid,
            startDate: params.startDate,
            endDate: params.endDate,
            voucherCodes: params.voucherCodes,
            sourceId: params.sourceId,
          })
        } catch (_error) {
          return null
        }
      })

      const batchResults = await Promise.all(batchPromises)
      const validResults = batchResults.filter(
        (result): result is VoucherSalesSummary =>
          result !== null && result.totalAmountOrigin > 0
      )

      summaries.push(...validResults)
    }

    return summaries
  },

  /**
   * Get total voucher amount origin across all stores
   */
  getTotalVoucherAmountOrigin: async (
    params: GetMultiStoreVoucherSummaryParams
  ): Promise<TotalVoucherAmountOriginResponse> => {
    const storeSummaries =
      await salesApiVoucher.getMultiStoreVoucherSummary(params)

    const totalAmountOrigin = storeSummaries.reduce(
      (sum, store) => sum + store.totalAmountOrigin,
      0
    )
    const totalDiscountAmount = storeSummaries.reduce(
      (sum, store) => sum + store.totalDiscountAmount,
      0
    )
    const totalNetAmount = storeSummaries.reduce(
      (sum, store) => sum + store.totalNetAmount,
      0
    )
    const totalTransactions = storeSummaries.reduce(
      (sum, store) => sum + store.transactionCount,
      0
    )

    return {
      totalAmountOrigin,
      totalDiscountAmount,
      totalNetAmount,
      totalTransactions,
      storeCount: storeSummaries.length,
      storeSummaries,
    }
  },
}
