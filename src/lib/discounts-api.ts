import type { GetDiscountsParams, DiscountsApiResponse, Discount } from '@/types/discounts'
import type { DiscountApiData } from '@/types/discounts'

import { apiClient } from './api'

/**
 * Fetch discounts from API
 */
export const getDiscounts = async (params: GetDiscountsParams = {}): Promise<Discount[]> => {
  const queryParams = new URLSearchParams()

  // Add company and brand UIDs
  if (params.companyUid) {
    queryParams.append('company_uid', params.companyUid)
  }
  if (params.brandUid) {
    queryParams.append('brand_uid', params.brandUid)
  }

  // Add pagination
  if (params.page) {
    queryParams.append('page', params.page.toString())
  }

  // Add store UIDs
  if (params.listStoreUid && params.listStoreUid.length > 0) {
    queryParams.append('list_store_uid', params.listStoreUid.join(','))
  }

  // Add promotion partner auto gen
  if (params.promotionPartnerAutoGen !== undefined) {
    queryParams.append('promotion_partner_auto_gen', params.promotionPartnerAutoGen.toString())
  }

  // Add status filter
  if (params.status) {
    queryParams.append('status', params.status)
  }

  // Add active filter
  if (params.active !== undefined) {
    queryParams.append('active', params.active.toString())
  }

  // Add search term
  if (params.searchTerm) {
    queryParams.append('search', params.searchTerm)
  }

  const response = await apiClient.get<DiscountsApiResponse>(
    `/mdata/v1/discounts?${queryParams.toString()}`
  )

  if (response.data?.data) {
    // Import the conversion function dynamically to avoid circular dependency
    const { convertApiDiscountToDiscount } = await import('@/types/discounts')
    return response.data.data.map(convertApiDiscountToDiscount)
  }

  return []
}

/**
 * Delete discount by ID
 */
export const deleteDiscount = async (params: {
  companyUid: string
  brandUid: string
  id: string
}): Promise<void> => {
  const queryParams = new URLSearchParams()
  queryParams.append('company_uid', params.companyUid)
  queryParams.append('brand_uid', params.brandUid)
  queryParams.append('id', params.id)

  await apiClient.delete(`/mdata/v1/discount?${queryParams.toString()}`)
}

/**
 * Update discount (toggle active status)
 */
export const updateDiscount = async (discountData: DiscountApiData): Promise<void> => {
  await apiClient.put('/mdata/v1/discount', discountData)
}
