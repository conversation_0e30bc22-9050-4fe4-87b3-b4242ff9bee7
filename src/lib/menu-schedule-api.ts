import type { MenuSchedule } from '@/features/menu/schedule/data'

import { apiClient } from './api'

export interface MenuScheduleListParams {
  company_uid?: string
  brand_uid?: string
  city_uid?: string
  store_uid?: string
  active?: string
  page?: number
  limit?: number
  [key: string]: string | number | undefined // Allow flexible additional params
}

export interface MenuScheduleListResponse {
  data: MenuSchedule[]
  total?: number
  page?: number
  limit?: number
}

/**
 * Get menu schedules list from API
 */
export const getMenuSchedules = async (
  params: MenuScheduleListParams = {}
): Promise<MenuSchedule[]> => {
  // Build query parameters, filtering out undefined values
  const queryParams: Record<string, string | number> = {}

  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== '') {
      queryParams[key] = value
    }
  })

  // Set default page if not provided
  if (!queryParams.page) {
    queryParams.page = 1
  }

  // Debug logging to see what params are being sent
  console.log('API Query Params:', queryParams)

  const response = await apiClient.get('/mdata/v1/item-schedules', {
    params: queryParams
  })

  return response.data?.data || response.data || []
}

/**
 * Get menu schedule by ID
 */
export const getMenuScheduleById = async (id: string): Promise<MenuSchedule> => {
  const response = await apiClient.get(`/mdata/v1/item-schedules/${id}`)
  return response.data
}

/**
 * Create new menu schedule
 */
export const createMenuSchedule = async (
  scheduleData: Partial<MenuSchedule>
): Promise<MenuSchedule> => {
  const response = await apiClient.post('/mdata/v1/item-schedules', scheduleData)
  return response.data
}

/**
 * Update menu schedule
 */
export const updateMenuSchedule = async (
  id: string,
  scheduleData: Partial<MenuSchedule>
): Promise<MenuSchedule> => {
  const response = await apiClient.put(`/mdata/v1/item-schedules/${id}`, scheduleData)
  return response.data
}

/**
 * Delete menu schedule
 */
export const deleteMenuSchedule = async (scheduleData: {
  time: number
  store_uid: string
  city_uid: string
  brand_uid: string
  company_uid: string
}): Promise<void> => {
  const queryParams: Record<string, string | number> = {
    time: scheduleData.time,
    store_uid: scheduleData.store_uid,
    city_uid: scheduleData.city_uid,
    brand_uid: scheduleData.brand_uid,
    company_uid: scheduleData.company_uid
  }

  await apiClient.delete('/mdata/v1/item-schedule', {
    params: queryParams
  })
}

/**
 * Get menu schedules by date range
 */
export const getMenuSchedulesByDateRange = async (
  startDate: string,
  endDate: string,
  params: Omit<MenuScheduleListParams, 'page' | 'limit'> = {}
): Promise<MenuSchedule[]> => {
  const queryParams: Record<string, string | number> = {
    start_date: startDate,
    end_date: endDate
  }

  // Add additional params, filtering out undefined values
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== '') {
      queryParams[key] = value
    }
  })

  const response = await apiClient.get('/mdata/v1/item-schedules', {
    params: queryParams
  })

  return response.data?.data || response.data || []
}

/**
 * Get active menu schedules for a specific store
 */
export const getActiveMenuSchedulesForStore = async (
  storeUid: string,
  date?: string
): Promise<MenuSchedule[]> => {
  const queryParams: Record<string, string | number> = {
    store_uid: storeUid
  }

  if (date) {
    queryParams.date = date
  }

  const response = await apiClient.get('/mdata/v1/item-schedules', {
    params: queryParams
  })

  return response.data?.data || response.data || []
}

// Export all functions as a single object for easier importing
export const menuScheduleApi = {
  getMenuSchedules,
  getMenuScheduleById,
  createMenuSchedule,
  updateMenuSchedule,
  deleteMenuSchedule,
  getMenuSchedulesByDateRange,
  getActiveMenuSchedulesForStore
}
