import type { Customization, ParsedCustomizationData } from '@/types/customizations'

import { apiClient } from './api'

// Import payload interface
interface ImportPayload {
  name: string
  list_item: string[]
  data: {
    LstItem_Options: Array<{
      Name: string
      Min_Permitted: number
      Max_Permitted: number
      LstItem_Id: string[]
    }>
  }
  company_uid: string
  brand_uid: string
  store_uid?: string
}

/**
 * Create Excel file for customizations export
 */
export const createCustomizationsExcelFile = async (
  customizations: Customization[]
): Promise<void> => {
  const XLSX = await import('xlsx')

  // Check if this is store-based customizations (has storeUid or storeName)
  // Also check if any customization has isCustomizationInStore = 1
  const isStoreBased = customizations.some(
    c =>
      c.storeUid ||
      (c as Customization & { storeName?: string }).storeName ||
      c.isCustomizationInStore === 1
  )

  const excelData = [
    isStoreBased
      ? [
          'Tên',
          '<PERSON><PERSON><PERSON> hàng',
          '<PERSON><PERSON> món áp dụng',
          'Tên nhóm',
          '<PERSON><PERSON><PERSON> cầu chọn',
          '<PERSON>iớ<PERSON> hạn chọn',
          '<PERSON>ã món theo nhóm'
        ]
      : [
          'ID',
          'Tên',
          'Thành phố',
          'Mã món áp dụng',
          'Tên nhóm',
          'Yêu cầu chọn',
          'Giới hạn chọn',
          'Mã món theo nhóm'
        ]
  ]

  customizations.forEach(customization => {
    const firstOption = customization.data.LstItem_Options?.[0]

    if (isStoreBased) {
      excelData.push([
        customization.name,
        (customization as Customization & { storeName?: string }).storeName || 'Cửa hàng hiện tại',
        customization.listItem.join(','), // Mã món áp dụng
        firstOption?.Name || '',
        String(firstOption?.Min_Permitted || ''),
        String(firstOption?.Max_Permitted || ''),
        firstOption?.LstItem_Id?.join(',') || '' // Mã món theo nhóm
      ])
    } else {
      excelData.push([
        customization.id,
        customization.name,
        customization.cityName,
        customization.listItem.join(','), // Mã món áp dụng
        firstOption?.Name || '',
        String(firstOption?.Min_Permitted || ''),
        String(firstOption?.Max_Permitted || ''),
        firstOption?.LstItem_Id?.join(',') || '' // Mã món theo nhóm
      ])
    }
  })

  const workbook = XLSX.utils.book_new()
  const worksheet = XLSX.utils.aoa_to_sheet(excelData)

  XLSX.utils.book_append_sheet(workbook, worksheet, 'Customizations')

  const now = new Date()
  const dateStr = now.toISOString().split('T')[0]
  const filename = `customizations_export_${dateStr}.xlsx`

  XLSX.writeFile(workbook, filename)
}

/**
 * Bulk import customizations from parsed Excel data
 */
export const bulkImportCustomizations = async (
  parsedData: ParsedCustomizationData[],
  storeUid?: string
): Promise<void> => {
  const posUserData = localStorage.getItem('pos_user_data')
  const posBrandData = localStorage.getItem('pos_brands_data')
  let companyUid = ''
  let brandUid = ''

  if (posUserData) {
    try {
      const userData = JSON.parse(posUserData)
      companyUid = userData.company_uid || ''
    } catch {
      // Error parsing user data
    }
  }

  if (posBrandData) {
    try {
      const brandData = JSON.parse(posBrandData)
      if (Array.isArray(brandData) && brandData.length > 0) {
        brandUid = brandData[0].id || ''
      }
    } catch {
      // Error parsing brand data
    }
  }

  if (!companyUid || !brandUid) {
    throw new Error('Company or brand UID not found in localStorage')
  }

  const payload = parsedData.map(item => {
    const groupItemCodes = item.groupItemCodes
      ? item.groupItemCodes
          .split(',')
          .map(code => code.trim())
          .filter(Boolean)
      : []

    const appliedItemCodes = item.appliedItemCodes
      ? item.appliedItemCodes
          .split(',')
          .map(code => code.trim())
          .filter(Boolean)
      : []

    const payload: ImportPayload = {
      name: item.name,
      list_item: appliedItemCodes,
      data: {
        LstItem_Options: [
          {
            Name: item.groupName,
            Min_Permitted: item.minRequired,
            Max_Permitted: item.maxAllowed,
            LstItem_Id: groupItemCodes
          }
        ]
      },
      company_uid: companyUid,
      brand_uid: brandUid
    }

    // Add store_uid if provided (for store-based customizations)
    if (storeUid) {
      payload.store_uid = storeUid
    }

    return payload
  })

  const apiUrl = `/mdata/v1/customizations/import`

  await apiClient.post(apiUrl, payload)
}
