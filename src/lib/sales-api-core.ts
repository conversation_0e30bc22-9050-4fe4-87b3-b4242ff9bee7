import { api } from './api'
import { salesCacheUtils } from './sales-cache'
import {
  SaleData,
  SalesResponse,
  SalesSummary,
  GetSalesReportParams,
  GetAllSalesDataParams,
  GetSalesSummaryParams,
} from './sales-types'

/**
 * Core Sales API functions
 */
export const salesApiCore = {
  /**
   * Get sales report data
   */
  getSalesReport: async (
    params: GetSalesReportParams
  ): Promise<SalesResponse> => {
    const requestKey = salesCacheUtils.generateKey(params)

    // Check cache first
    const cached = salesCacheUtils.get(requestKey)
    if (cached) {
      return cached
    }

    // Check if request is already pending
    const existingRequest = salesCacheUtils.getPendingRequest(requestKey)
    if (existingRequest) {
      return existingRequest
    }

    // Create new request
    const requestPromise = (async (): Promise<SalesResponse> => {
      try {
        const queryParams = new URLSearchParams({
          company_uid: params.companyUid,
          brand_uid: params.brandUid,
          list_store_uid: params.listStoreUid,
          start_date: params.startDate.toString(),
          end_date: params.endDate.toString(),
          page: (params.page || 1).toString(),
        })

        if (params.sourceId !== undefined) {
          queryParams.append('source_id', params.sourceId.toString())
        }

        if (params.isDiscount !== undefined) {
          queryParams.append('is_discount', params.isDiscount.toString())
        }

        const response = await api.get(
          `/v1/accounting/report/sale?${queryParams.toString()}`,
          {
            headers: {
              Accept: 'application/json, text/plain, */*',
              'accept-language': 'vi',
              fabi_type: 'pos-cms',
              'x-client-timezone': '********',
            },
            timeout: 30000,
          }
        )

        const salesData = response.data as SalesResponse

        // Cache the result
        salesCacheUtils.set(requestKey, salesData)

        return salesData
      } finally {
        salesCacheUtils.removePendingRequest(requestKey)
      }
    })()

    salesCacheUtils.setPendingRequest(requestKey, requestPromise)
    return requestPromise
  },

  /**
   * Get sales data for multiple pages
   */
  getAllSalesData: async (
    params: GetAllSalesDataParams
  ): Promise<SaleData[]> => {
    const allSalesData: SaleData[] = []
    let currentPage = 1
    const maxPages = params.maxPages || 100

    while (currentPage <= maxPages) {
      const response = await salesApiCore.getSalesReport({
        ...params,
        page: currentPage,
      })

      if (!response.data || response.data.length === 0) {
        break
      }

      allSalesData.push(...response.data)
      currentPage++
    }

    return allSalesData
  },

  /**
   * Get sales summary for a store
   */
  getSalesSummary: async (
    params: GetSalesSummaryParams
  ): Promise<SalesSummary> => {
    const salesData = await salesApiCore.getAllSalesData(params)

    const totalTransactions = salesData.length
    const totalAmount = salesData.reduce(
      (sum, sale) => sum + sale.total_amount,
      0
    )
    const discountAmount = salesData.reduce(
      (sum, sale) => sum + sale.discount_amount,
      0
    )
    const netAmount = salesData.reduce((sum, sale) => sum + sale.net_amount, 0)
    const discountPercentage =
      totalAmount > 0 ? (discountAmount / totalAmount) * 100 : 0

    // Get store name from first record (assuming all records are from same store)
    const storeName = salesData.length > 0 ? salesData[0].store_name : ''

    return {
      storeUid: params.listStoreUid,
      storeName,
      totalTransactions,
      totalAmount,
      discountAmount,
      netAmount,
      discountPercentage,
      salesData,
    }
  },

  /**
   * Get sales data with discount filter
   */
  getDiscountedSales: async (
    params: Omit<GetSalesReportParams, 'isDiscount'>
  ): Promise<SalesResponse> => {
    return salesApiCore.getSalesReport({
      ...params,
      isDiscount: 1,
    })
  },

  /**
   * Get sales data without discount filter
   */
  getNonDiscountedSales: async (
    params: Omit<GetSalesReportParams, 'isDiscount'>
  ): Promise<SalesResponse> => {
    return salesApiCore.getSalesReport({
      ...params,
      isDiscount: 0,
    })
  },
}
