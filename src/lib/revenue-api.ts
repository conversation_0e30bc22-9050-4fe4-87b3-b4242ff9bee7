import { api } from './api'

// Request deduplication for revenue API
const ongoingRevenueRequests = new Map<string, Promise<RevenueResponse>>()
const revenueCache = new Map<
  string,
  { data: RevenueResponse; timestamp: number }
>()
const REVENUE_CACHE_DURATION = 2 * 60 * 1000 // 2 minutes

// Request deduplication for payment methods API
const ongoingPaymentMethodsRequests = new Map<
  string,
  Promise<PaymentMethodsResponse>
>()
const paymentMethodsCache = new Map<
  string,
  { data: PaymentMethodsResponse; timestamp: number }
>()
const PAYMENT_METHODS_CACHE_DURATION = 2 * 60 * 1000 // 2 minutes

// Revenue API Types
export interface RevenueDataItem {
  date: string
  tran_date: number
  total_sales: number
  discount_amount: number
  commission_amount: number
  peo_count: number
  revenue_net: number
  revenue_gross: number
  discount_extra_amount: number
  amount_discount_detail: number
  partner_marketing_amount: number
  voucher_amount: number
  vat_amount: number
  discount_vat_amount: number
  payment_methods: unknown[]
  amount: number
}

// Payment Methods API Types
export interface PaymentMethodData {
  total_bill: number
  total_amount: number
  total_count: number
  payment_method_id: string
  payment_method_name: string
  list_data: unknown[]
  total_payment_fee_amount: number
}

export interface PaymentMethodsResponse {
  data: PaymentMethodData[]
  message: string
  track_id: string
}

export interface RevenueStoreData {
  store_uid: string
  store_name: string
  total_sales: number
  revenue_gross: number
  discount_amount: number
  commission_amount: number
  revenue_net: number
  peo_count: number
  list_data: RevenueDataItem[]
  discount_extra_amount: number
  amount_discount_detail: number
  partner_marketing_amount: number
  voucher_amount: number
  vat_amount: number
  discount_vat_amount: number
}

export interface RevenueResponse {
  data: RevenueStoreData[]
  message: string
  track_id: string
}

export interface RevenueSummary {
  totalRevenue: number
  totalOrders: number
  storeCount: number
  dailyData: Array<{
    date: string
    revenue: number
    orders: number
  }>
}

// Revenue API Service
export const revenueApi = {
  /**
   * Get revenue summary for stores with request deduplication
   */
  getRevenueSummary: async (params: {
    companyUid: string
    brandUid: string
    startDate: number // timestamp in milliseconds
    endDate: number // timestamp in milliseconds
    storeUids?: string[] // optional list of store UIDs
    byDays?: number // 1 for daily breakdown
    limit?: number
  }): Promise<RevenueResponse> => {
    // Create a unique key for this request
    const requestKey = `${params.companyUid}-${params.brandUid}-${params.startDate}-${params.endDate}-${params.storeUids?.join(',') || 'all'}`

    // Check cache first
    const cached = revenueCache.get(requestKey)
    if (cached && Date.now() - cached.timestamp < REVENUE_CACHE_DURATION) {
      return cached.data
    }

    // If there's already an ongoing request, return it
    if (ongoingRevenueRequests.has(requestKey)) {
      return ongoingRevenueRequests.get(requestKey)!
    }

    const requestPromise = (async () => {
      try {
        const queryParams = new URLSearchParams({
          company_uid: params.companyUid,
          brand_uid: params.brandUid,
          start_date: params.startDate.toString(),
          end_date: params.endDate.toString(),
          limit: (params.limit || 1000).toString(),
          by_days: (params.byDays || 1).toString(),
          store_open_at: '0',
        })

        // Always add store UIDs - never use 'all-stores' text
        if (params.storeUids && params.storeUids.length > 0) {
          queryParams.set('list_store_uid', params.storeUids.join(','))
        }

        const response = await api.get(
          `/v1/reports/sale-summary/stores?${queryParams.toString()}`
        )

        // Cache the result
        revenueCache.set(requestKey, {
          data: response.data as RevenueResponse,
          timestamp: Date.now(),
        })

        return response.data as RevenueResponse
      } finally {
        // Clean up the ongoing request
        ongoingRevenueRequests.delete(requestKey)
      }
    })()

    // Store the promise to prevent duplicate requests
    ongoingRevenueRequests.set(requestKey, requestPromise)

    return requestPromise
  },

  /**
   * Process revenue data into summary format
   */
  processRevenueSummary: (revenueData: RevenueStoreData[]): RevenueSummary => {
    const totalRevenue = revenueData.reduce(
      (sum, store) => sum + store.revenue_gross,
      0
    )
    const totalOrders = revenueData.reduce(
      (sum, store) => sum + store.total_sales,
      0
    )
    const storeCount = revenueData.length

    // Aggregate daily data across all stores
    const dailyDataMap = new Map<string, { revenue: number; orders: number }>()

    revenueData.forEach((store) => {
      store.list_data.forEach((day) => {
        const existing = dailyDataMap.get(day.date)
        if (existing) {
          existing.revenue += day.revenue_gross
          existing.orders += day.total_sales
        } else {
          dailyDataMap.set(day.date, {
            revenue: day.revenue_gross,
            orders: day.total_sales,
          })
        }
      })
    })

    const dailyData = Array.from(dailyDataMap.entries())
      .map(([date, data]) => ({
        date,
        revenue: data.revenue,
        orders: data.orders,
      }))
      .sort((a, b) => a.date.localeCompare(b.date))

    return {
      totalRevenue,
      totalOrders,
      storeCount,
      dailyData,
    }
  },

  /**
   * Get payment methods summary with request deduplication
   */
  getPaymentMethodsSummary: async (params: {
    companyUid: string
    brandUid: string
    startDate: number // timestamp in milliseconds
    endDate: number // timestamp in milliseconds
    storeUids?: string[] // optional list of store UIDs
    sourceId?: string // optional source ID for filtering
    limit?: number
  }): Promise<PaymentMethodsResponse> => {
    // Create a unique key for this request
    const requestKey = `${params.companyUid}-${params.brandUid}-${params.startDate}-${params.endDate}-${params.storeUids?.join(',') || 'all'}-${params.sourceId || 'all-sources'}`

    // Check cache first
    const cached = paymentMethodsCache.get(requestKey)
    if (
      cached &&
      Date.now() - cached.timestamp < PAYMENT_METHODS_CACHE_DURATION
    ) {
      return cached.data
    }

    // Check if there's an ongoing request for the same data
    const ongoingRequest = ongoingPaymentMethodsRequests.get(requestKey)
    if (ongoingRequest) {
      return ongoingRequest
    }

    const requestPromise = (async () => {
      try {
        const queryParams = new URLSearchParams({
          company_uid: params.companyUid,
          brand_uid: params.brandUid,
          start_date: params.startDate.toString(),
          end_date: params.endDate.toString(),
          limit: (params.limit || 5).toString(),
          store_open_at: '0',
        })

        // Always add store UIDs - never use 'all-stores' text
        if (params.storeUids && params.storeUids.length > 0) {
          queryParams.set('list_store_uid', params.storeUids.join(','))
        }

        // Add source_id if provided (only if not 'all-sources')
        if (params.sourceId && params.sourceId !== 'all-sources') {
          queryParams.set('source_id', params.sourceId)
        }

        const response = await api.get(
          `/v1/reports/sale-summary/payment-methods?${queryParams.toString()}`
        )

        // Cache the result
        paymentMethodsCache.set(requestKey, {
          data: response.data as PaymentMethodsResponse,
          timestamp: Date.now(),
        })

        return response.data as PaymentMethodsResponse
      } finally {
        // Remove from ongoing requests
        ongoingPaymentMethodsRequests.delete(requestKey)
      }
    })()

    // Store the ongoing request
    ongoingPaymentMethodsRequests.set(requestKey, requestPromise)

    return requestPromise
  },

  /**
   * Format currency for display
   */
  formatCurrency: (amount: number): string => {
    return new Intl.NumberFormat('vi-VN').format(amount)
  },

  /**
   * Get date range for different periods
   */
  getDateRange: (period: string): { startDate: number; endDate: number } => {
    const now = new Date()
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())

    switch (period) {
      case 'today':
        return {
          startDate: today.getTime(),
          endDate: today.getTime() + 24 * 60 * 60 * 1000 - 1,
        }

      case 'yesterday': {
        const yesterday = new Date(today)
        yesterday.setDate(yesterday.getDate() - 1)
        return {
          startDate: yesterday.getTime(),
          endDate: yesterday.getTime() + 24 * 60 * 60 * 1000 - 1,
        }
      }

      case 'this-week': {
        const startOfWeek = new Date(today)
        startOfWeek.setDate(today.getDate() - today.getDay())
        return {
          startDate: startOfWeek.getTime(),
          endDate: now.getTime(),
        }
      }

      case 'last-week': {
        const startOfThisWeek = new Date(today)
        startOfThisWeek.setDate(today.getDate() - today.getDay())
        const startOfLastWeek = new Date(startOfThisWeek)
        startOfLastWeek.setDate(startOfThisWeek.getDate() - 7)
        const endOfLastWeek = new Date(startOfThisWeek)
        endOfLastWeek.setTime(endOfLastWeek.getTime() - 1)
        return {
          startDate: startOfLastWeek.getTime(),
          endDate: endOfLastWeek.getTime(),
        }
      }

      case 'this-month': {
        const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1)
        return {
          startDate: startOfMonth.getTime(),
          endDate: now.getTime(),
        }
      }

      case 'last-month': {
        const startOfLastMonth = new Date(
          today.getFullYear(),
          today.getMonth() - 1,
          1
        )
        const endOfLastMonth = new Date(
          today.getFullYear(),
          today.getMonth(),
          0,
          23,
          59,
          59,
          999
        )
        return {
          startDate: startOfLastMonth.getTime(),
          endDate: endOfLastMonth.getTime(),
        }
      }

      case 'this-quarter': {
        const currentQuarter = Math.floor(today.getMonth() / 3)
        const startOfQuarter = new Date(
          today.getFullYear(),
          currentQuarter * 3,
          1
        )
        return {
          startDate: startOfQuarter.getTime(),
          endDate: now.getTime(),
        }
      }

      case 'last-quarter': {
        const currentQuarter = Math.floor(today.getMonth() / 3)
        const lastQuarter = currentQuarter === 0 ? 3 : currentQuarter - 1
        const lastQuarterYear =
          currentQuarter === 0 ? today.getFullYear() - 1 : today.getFullYear()
        const startOfLastQuarter = new Date(lastQuarterYear, lastQuarter * 3, 1)
        const endOfLastQuarter = new Date(
          lastQuarterYear,
          (lastQuarter + 1) * 3,
          0,
          23,
          59,
          59,
          999
        )
        return {
          startDate: startOfLastQuarter.getTime(),
          endDate: endOfLastQuarter.getTime(),
        }
      }

      case 'this-year': {
        const startOfYear = new Date(today.getFullYear(), 0, 1)
        return {
          startDate: startOfYear.getTime(),
          endDate: now.getTime(),
        }
      }

      case 'last-year': {
        const startOfLastYear = new Date(today.getFullYear() - 1, 0, 1)
        const endOfLastYear = new Date(
          today.getFullYear() - 1,
          11,
          31,
          23,
          59,
          59,
          999
        )
        return {
          startDate: startOfLastYear.getTime(),
          endDate: endOfLastYear.getTime(),
        }
      }

      case 'last-7-days': {
        const sevenDaysAgo = new Date(today)
        sevenDaysAgo.setDate(today.getDate() - 7)
        return {
          startDate: sevenDaysAgo.getTime(),
          endDate: now.getTime(),
        }
      }

      case 'last-30-days': {
        const thirtyDaysAgo = new Date(today)
        thirtyDaysAgo.setDate(today.getDate() - 30)
        return {
          startDate: thirtyDaysAgo.getTime(),
          endDate: now.getTime(),
        }
      }

      case 'last-90-days': {
        const ninetyDaysAgo = new Date(today)
        ninetyDaysAgo.setDate(today.getDate() - 90)
        return {
          startDate: ninetyDaysAgo.getTime(),
          endDate: now.getTime(),
        }
      }

      default: {
        // Default to this month
        const defaultStart = new Date(today.getFullYear(), today.getMonth(), 1)
        return {
          startDate: defaultStart.getTime(),
          endDate: now.getTime(),
        }
      }
    }
  },
}

export default revenueApi
