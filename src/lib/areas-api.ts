import { apiClient } from './api'

export interface Area {
  id: string
  area_id: string
  area_name: string
  description?: string
  extra_data?: any
  active: number
  revision: number
  sort: number
  store_uid: string
  brand_uid: string
  company_uid: string
  store_id?: string
  brand_id?: string
  company_id?: string
  is_fabi: number
  created_by: string
  updated_by?: string
  created_at: string
  updated_at: string
  list_table_id: string[]
}

export interface AreasListParams {
  company_uid: string
  brand_uid: string
  store_uid: string
  page?: number
  limit?: number
}

export interface AreasListResponse {
  data: Area[]
  track_id: string
}

export interface CreateAreaRequest {
  area_name: string
  area_id?: string
  description?: string
  store_uid: string
  brand_uid: string
  company_uid: string
  active?: number
  sort?: number
  extra_data?: {
    background?: string
  }
}

export interface UpdateAreaRequest extends CreateAreaRequest {
  id: string
}

export interface DeleteAreaParams {
  id: string
  company_uid: string
  brand_uid: string
  store_uid: string
}

export interface DeleteAreasRequest {
  company_uid: string
  brand_uid: string
  list_id: string[]
  store_uid: string
}

export interface BulkImportAreaItem {
  company_uid: string
  brand_uid: string
  area_name: string
  area_id: string
  description?: string
  store_uid: string
  sort: number
}

export type BulkImportAreasRequest = BulkImportAreaItem[]

export interface ImageUploadResponse {
  data: {
    image_url: string
  }
  track_id: string
}

/**
 * Areas API client
 */
export const areasApi = {
  /**
   * Get areas list
   */
  getAreasList: async (params: AreasListParams): Promise<AreasListResponse> => {
    const queryParams = new URLSearchParams({
      company_uid: params.company_uid,
      brand_uid: params.brand_uid,
      store_uid: params.store_uid,
      page: (params.page || 1).toString(),
      ...(params.limit && { limit: params.limit.toString() })
    })

    const response = await apiClient.get(`/pos/v1/area?${queryParams.toString()}`)
    return response.data
  },

  /**
   * Update an existing area
   */
  updateArea: async (data: Area): Promise<Area> => {
    // Use POST method to /pos/v1/area with complete area object (like in cURL)
    console.log('Updating area with POST:', data)
    const response = await apiClient.post('/pos/v1/area', data)
    return response.data.data || response.data
  },

  /**
   * Delete an area
   */
  deleteArea: async (params: DeleteAreaParams): Promise<void> => {
    const queryParams = new URLSearchParams({
      company_uid: params.company_uid,
      brand_uid: params.brand_uid,
      store_uid: params.store_uid
    })

    await apiClient.delete(`/pos/v1/area/${params.id}?${queryParams.toString()}`)
  },

  /**
   * Get area by ID
   */
  getAreaById: async (
    id: string,
    companyUid: string,
    brandUid: string,
    storeUid: string
  ): Promise<Area> => {
    const queryParams = new URLSearchParams({
      company_uid: companyUid,
      brand_uid: brandUid,
      store_uid: storeUid,
      id: id
    })

    const response = await apiClient.get(`/pos/v1/area?${queryParams.toString()}`)
    return response.data.data || response.data
  },

  /**
   * Delete multiple areas
   */
  deleteAreas: async (params: DeleteAreasRequest): Promise<void> => {
    const response = await apiClient.delete('/pos/v1/area', {
      data: params
    })
    return response.data
  },

  /**
   * Upload image for area background
   */
  uploadImage: async (file: File): Promise<ImageUploadResponse> => {
    const formData = new FormData()
    formData.append('file', file)

    const response = await apiClient.post('/v3/pos-cms/image/upload', formData, {
      headers: {
        // Let axios set Content-Type automatically with boundary
        'Content-Type': undefined
      }
    })

    return response.data
  },

  /**
   * Create a new area
   */
  createArea: async (data: CreateAreaRequest): Promise<Area> => {
    const response = await apiClient.post('/pos/v1/area', [data])
    return response.data.data?.[0] || response.data[0]
  },

  /**
   * Bulk import areas
   */
  bulkImportAreas: async (areas: BulkImportAreasRequest): Promise<Area[]> => {
    const response = await apiClient.post('/pos/v1/area', areas)
    return response.data.data || response.data
  }
}

export default areasApi
