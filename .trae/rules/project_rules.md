# Project Rules

This document defines the coding standards and rules that <PERSON><PERSON> should follow when working on this POS project.

---

# API Integration Standards

## API Client Structure
- **Separate API logic** from UI components in `lib/[domain]-api.ts`
- **Define clear interfaces** for API parameters and responses in `types/api/`
- **Use React Query hooks** in `hooks/api/use-[domain].ts`
- **Implement proper caching** and query invalidation
- **Follow consistent naming** patterns for API functions and hooks

## Error Handling
- **Use consistent error patterns** across API calls
- **Implement proper loading states** in hooks
- **Show user-friendly error messages** with toast notifications
- **Handle edge cases** gracefully

## Caching Strategies
- **Use React Query** for server state management
- **Implement proper cache invalidation** after mutations
- **Set appropriate stale times** based on data freshness requirements
- **Use optimistic updates** for better user experience
- **Set up key** in queryKeys

---

# Architecture Principles

## 1. Feature-Driven Structure

```
src/features/[domain]/[subdomain]/
├── components/         # Feature-specific UI components
├── hooks/              # Feature-specific custom hooks
├── context/            # Feature-specific context providers
├── data/               # Data schemas and types
└── index.tsx           # Main feature entry point
```

### Benefits
- **Logical grouping**: Related functionality stays together
- **Easy navigation**: Find feature code in predictable locations
- **Scalability**: Add new features without affecting existing structure
- **Team collaboration**: Clear ownership boundaries

## 2. Modular Hook Organization

### Structure Rules
- **Create dedicated `hooks/` directories** within feature folders
- **One hook per file** with focused responsibility
- **Use index.ts** for clean exports: `export { useFeatureHook } from './use-feature-hook'`
- **Hook naming**: `use[Domain][Action]` (e.g., `useCustomizationForm`, `useMenuItemSelection`)

### Example Structure
```
src/features/menu/
├── hooks/
│   ├── index.ts
│   ├── use-menu-items.ts
│   ├── use-menu-categories.ts
│   └── use-menu-form.ts
└── components/
    └── menu-list.tsx
```

### Hook Responsibilities
- **Data fetching**: API calls and caching
- **Form management**: Validation and submission
- **State management**: Local component state
- **Business logic**: Domain-specific operations

## 3. Component Architecture

### Design Principles
- **Prefer composition over inheritance**
- **Extract reusable logic into custom hooks**
- **Keep components focused on rendering and user interaction**
- **Use TypeScript interfaces for all props and data structures**

### Component Types
- **Feature Components**: Domain-specific UI logic
- **Layout Components**: Page structure and navigation
- **UI Components**: Reusable design system elements
- **POS Components**: Business-specific shared components

## 4. Separation of Concerns

### Clear Boundaries
- **UI Layer**: Components handle rendering and user interaction
- **Logic Layer**: Hooks manage state and business logic
- **Data Layer**: API clients handle server communication
- **Type Layer**: Interfaces define data contracts

## Component Styling Patterns

- **Consistent formatting**: Follow established prettier configuration
- **Logical grouping**: Group related JSX elements together
- **Clean prop passing**: Use object destructuring for cleaner prop handling
- **Minimal nesting**: Avoid deeply nested conditional rendering
- **NOT README**: Not creating README after successfully task

---

# Component Standards

## Component Structure
- **Initialize hooks** at the top of components
- **Extract complex logic** into custom hooks
- **Create handler functions** that combine hook actions
- **Keep JSX clean** and focused on rendering
- **Use proper TypeScript** for all props and state

## Code Splitting Patterns
- **Lazy load feature components** using `React.lazy()` and `Suspense`
- **Route-based splitting** - Split at page/route level first
- **Feature-based splitting** - Large features should be separate chunks
- **Component-based splitting** - Heavy components (charts, editors) should be lazy loaded
- **Use dynamic imports** for conditional feature loading
- **Implement proper loading states** with Suspense fallbacks
- **Preload critical routes** using `<link rel="prefetch">` or router preloading

## State Management Rules
- **Use custom hooks** for complex state logic
- **Zustand stores** for global application state
- **React Query** for server state management
- **Local useState** only for simple UI state

---

# File Organization Standards

## Directory Structure Rules

```
src/
├── components/ui/          # Reusable UI components (shadcn/ui)
├── components/layout/      # Layout-specific components
├── components/pos/         # POS-specific shared components
├── features/               # Feature modules
├── hooks/                  # Global custom hooks
├── hooks/api/              # API-related hooks
├── lib/                    # Utility libraries and API clients
├── stores/                 # Zustand state management
├── types/                  # Global TypeScript definitions
├── types/api/              # API-specific TypeScript definitions
├── utils/                  # Utility functions
└── constants/              # Application constants
```

## Import Organization

Follow the established import order from `.prettierrc`:

1. **External libraries** (React, third-party packages)
2. **Internal libraries** (@/lib, @/utils, @/constants)
3. **Hooks** (@/hooks)
4. **Components** (@/components)
5. **Features** (@/features)
6. **Relative imports** (./hooks, ../components)

### Example Import Order
```typescript
import React from 'react'
import { useQuery } from '@tanstack/react-query'

import { cn } from '@/lib/utils'
import { API_ENDPOINTS } from '@/constants/api'

import { useToast } from '@/hooks/use-toast'

import { Button, Card } from '@/components/ui'

import { MenuItemForm } from '@/features/menu/components'

import { useMenuItems } from './hooks/use-menu-items'
```

## Module Organization Patterns

### Feature Module Structure
```
src/features/menu/
├── components/
│   ├── index.ts
│   ├── menu-list.tsx
│   ├── menu-item-card.tsx
│   └── menu-form.tsx
├── hooks/
│   ├── index.ts
│   ├── use-menu-items.ts
│   ├── use-menu-form.ts
│   └── use-menu-categories.ts
├── context/
│   ├── index.ts
│   └── menu-context.tsx
├── data/
│   ├── index.ts
│   ├── menu-schemas.ts
│   └── menu-types.ts
└── index.tsx
```

### Index File Patterns
```typescript
// features/menu/components/index.ts
export { MenuList } from './menu-list'
export { MenuItemCard } from './menu-item-card'
export { MenuForm } from './menu-form'

// features/menu/hooks/index.ts
export { useMenuItems } from './use-menu-items'
export { useMenuForm } from './use-menu-form'
export { useMenuCategories } from './use-menu-categories'

export * from './components'
export * from './hooks'
export * from './context'
export * from './data'
```

## File Naming Conventions

### Component Files
- **PascalCase**: `MenuItemCard.tsx`
- **kebab-case**: `menu-item-card.tsx` (preferred)
- **Consistent within project**: Choose one and stick to it

### Hook Files
- **kebab-case**: `use-menu-items.ts`
- **Descriptive names**: `use-menu-form-validation.ts`

### Utility Files
- **kebab-case**: `format-currency.ts`
- **Clear purpose**: `date-helpers.ts`, `api-client.ts`

### Type Files
- **kebab-case**: `menu-types.ts`
- **Domain-specific**: `api-types.ts`, `form-types.ts`

## Best Practices

### Avoid Deep Nesting
```typescript
// Bad: Deep nesting
import { MenuForm } from '../../../features/menu/components/forms/menu-form'

// Good: Proper index exports
import { MenuForm } from '@/features/menu'
```

### Group Related Files
```
src/features/inventory/
├── components/
│   ├── tables/           # Table-related components
│   ├── forms/            # Form-related components
│   └── modals/           # Modal-related components
```

### Consistent Exports
```typescript
export const MenuList = () => { ... }

export default MenuList
```

---

# Form Handling Standards

## Zod Schema Definition
- **Use Zod schemas** for all form validation and type safety
- **Define clear validation rules** with appropriate error messages
- **Export TypeScript types** using `z.infer<typeof schema>`
- **Use Vietnamese error messages** for user-facing validation

## React Hook Form Integration
- **Use React Hook Form** with Zod resolver for all form handling
- **Set appropriate default values** for form initialization
- **Handle form submission** with proper error handling
- **Return form state** including validation status and errors

## Form Component Pattern
- **Use shadcn/ui Form components** for consistent styling
- **Implement FormField** for all form inputs with proper validation
- **Show validation errors** using FormMessage components
- **Handle loading states** during form submission

## Form Validation Rules
- **Required fields**: Use appropriate Zod validators with Vietnamese error messages
- **Field validation**: Implement real-time validation with proper error display
- **Cross-field validation**: Use Zod refinements for complex validation logic
- **Async validation**: Implement server-side validation for unique constraints

## Form State Management
- **Use React Hook Form state**: Avoid additional useState for form data
- **Controlled components**: Use FormField for all form inputs
- **Reset handling**: Implement proper form reset functionality

---

# TypeScript Standards

## Interface Definitions
- **Clear, descriptive interfaces** with meaningful property names
- **Extend base interfaces** when appropriate to maintain consistency
- **Use proper typing** for all props and data structures
- **Avoid `any` type** - use specific types or unions instead

## Hook Patterns
- **Focused hooks** with clear return types and single responsibility
- **Consistent naming** following `use[Domain][Action]` convention
- **Return objects** with descriptive property names